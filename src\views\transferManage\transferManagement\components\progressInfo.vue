<template>
    <div class="mb20 progressInfo">
        <div class="title">{{ title }}</div>
        <el-steps :active="active" class="mt20" align-center>
            <el-step v-for="v in progressList" :key="v" :title="v.label" />
        </el-steps>
    </div>
</template>

<script setup>
const props = defineProps({
    title: { type: String, default: '' },
    progressList: { type: Array, default: [] },
    active: { type: [String, Number], default: 1 },
})
</script>

<style lang="scss" scoped>
.title {
    margin-bottom: 40px;
}

.progressInfo {
    padding: 20px;
    background-color: #fff;
}

:deep(.el-steps) {
    padding-bottom: 20px;

    .el-step__icon {
        width: 44px;
        height: 44px;
        top: -10px;
        color: #BBBBBB;
        border-color: #DEE1E3;
    }

    .el-step__line {
        background-color: #DEE1E3;
    }

    .is-process {
        color: #BBBBBB;
        font-weight: normal;
    }

    .is-finish {
        .el-step__icon {
            color: #fff;
            border-color: #409eff;
            background-color: #409eff;
        }
    }
}
</style>