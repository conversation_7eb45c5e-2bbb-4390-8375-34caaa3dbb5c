<template>
    <div :class="`node ${status}`">
        <img :src="images.logo" v-if="['success','failed','running'].includes(status)" alt="logo" />
        <img :src="images?.[status]" v-else alt="logo"/>
        <span class="label" :title="label">{{label}}</span>
        <span class="status">
            <img v-if="['success'].includes(status)" :src="images.success" alt="success" />
            <img v-if="status === 'failed'" :src="images.failed" alt="failed" />
            <img v-if="status === 'running'" :src="images.running" alt="running" />
        </span>
    </div>
</template>
<script setup>
import { inject } from "vue";

const { proxy } = getCurrentInstance();
const props = defineProps({
    node: { type: Object }
})
const label = ref("")
const status = ref(undefined)
const images = ref({
    logo: 'https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*evDjT5vjkX0AAAAAAAAAAAAAARQnAQ',
    success:
        'https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*6l60T6h8TTQAAAAAAAAAAAAAARQnAQ',
    failed:
        'https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*SEISQ6My-HoAAAAAAAAAAAAAARQnAQ',
    running:
        'https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*t8fURKfgSOgAAAAAAAAAAAAAARQnAQ',
    start:
        'https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*zUgORbGg1HIAAAAAAAAAAAAADtOHAQ/original',
    end:'https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*RXnuTpQ22xkAAAAAAAAAAAAADtOHAQ/original',
    rules:'https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*ZJ6qToit8P4AAAAAAAAAAAAADtOHAQ/original',
    keep:'https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*EHqyQoDeBvIAAAAAAAAAAAAADtOHAQ/original'
    
})
onMounted(() => {
    const data = props.node?.getData()
    status.value = data?.status ?? 'success'
    label.value = data.label ?? "节点";
})
</script>
<style lang="scss" scoped>
.node {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 1px solid #c2c8d5;
  border-left: 4px solid #5F95FF;
  border-radius: 4px;
  box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);
  // margin-top: 10px;
}
.node img {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-left: 8px;
}
.node .label {
  display: inline-block;
  flex-shrink: 0;
  width: 75px;
  margin-left: 8px;
  // margin-top: 2px;
  color: #666;
  font-size: 12px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.node .status {
  flex-shrink: 0;
  img{
    margin-top: 4px;
  }
}
.node.success {
  border-left: 4px solid #52c41a;
}
.node.failed {
  border-left: 4px solid #ff4d4f;
}
.node.running .status img {
  animation: spin 1s linear infinite;
}
.x6-node-selected .node {
  border-color: #1890ff;
  border-radius: 2px;
  box-shadow: 0 0 0 4px #d4e8fe;
}
.x6-node-selected .node.success {
  border-color: #52c41a;
  border-radius: 2px;
  box-shadow: 0 0 0 4px #ccecc0;
}
.x6-node-selected .node.failed {
  border-color: #ff4d4f;
  border-radius: 2px;
  box-shadow: 0 0 0 4px #fedcdc;
}
</style>