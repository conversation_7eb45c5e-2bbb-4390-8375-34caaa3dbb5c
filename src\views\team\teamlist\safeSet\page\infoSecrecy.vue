<template>
  <div>
    <div class="title mb20">
      <span>开启后,系统中相关字段信息将强制脱敏</span>
      <el-switch
        class="myswitch ml20"
        v-model="state.informationStatus"
        active-color="#2ECC71"
        :active-value="1"
        :inactive-value="0"
        active-text="开"
        inactive-text="关"
        @change="change"
      ></el-switch>
    </div>
    <infoSe v-if="state.informationStatus" />
  </div>
</template>

<script setup>
import infoSe from "@/views/team/components/infoSecrecy/index.vue";
import { changeSafeStatus } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);

function change() {
  changeSafeStatus(state.value)
    .then((res) => {})
    .catch(() => {
      getTeamSafe();
    });
}
</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
