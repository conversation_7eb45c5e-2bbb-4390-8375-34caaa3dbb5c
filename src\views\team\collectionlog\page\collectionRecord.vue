<template>
  <div style="height: calc(100vh - 203px); overflow: auto">
    <el-form class="form-content h-50" :class="{ 'h-auto': showSearch }" :model="queryParams" ref="queryRef"
      :inline="true" label-width="102px">
      <el-form-item label="案件ID" prop="caseId">
        <el-input onkeyup="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')"
          oninput="value=value.replace(/[^\x00-\xff]|\s|[A-z]/g, '')" v-model="queryParams.caseId" placeholder="请输入案件ID"
          clearable style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="clientName">
        <el-input v-model="queryParams.clientName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="证件号码" prop="clientIdcard">
        <el-input v-model="queryParams.clientIdcard" placeholder="请输入证件号码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="clientPhone">
        <el-input v-model="queryParams.clientPhone" placeholder="请输入手机号码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>

    <el-row class="mb8 h32">
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" :types="[1, 2, 3]" />
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="案件ID" align="left" key="caseId" prop="caseId" v-if="columns[0].visible" />
      <el-table-column label="资产批次号【转让方-产品类型】" align="center" key="batchNum" prop="batchNum" v-if="columns[1].visible">
        <template #default="{ row }">
          {{
            `${row.batchNum || "--"}【${row.entrustingPartyName || "--"} - ${row.productName || "--"
              }】`
          }}
        </template>
      </el-table-column>
      <el-table-column label="所属公司" align="center" key="outsourcingTeamName" prop="outsourcingTeamName"
        show-overflow-tooltip v-if="columns[2].visible" />
      <el-table-column label="姓名" align="center" key="clientName" prop="clientName" v-if="columns[3].visible" />
      <el-table-column label="手机号码" align="center" key="clientPhone" prop="clientPhone" v-if="columns[4].visible" />
      <el-table-column label="催收员" align="center" key="odvName" prop="odvName" show-overflow-tooltip
        v-if="columns[5].visible" />
      <el-table-column label="跟进状态" align="center" key="followUpState" prop="followUpState" show-overflow-tooltip
        v-if="columns[6].visible" />
      <el-table-column label="催收状态" align="center" key="urgeState" prop="urgeState" show-overflow-tooltip
        v-if="columns[7].visible" />
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="openUrgeDetails(row)">催记详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="催记详情" v-model="openDetail" width="1000px" append-to-body>
      <el-table max-height="600" :data="urgeDetail.list">
        <el-table-column label="催记时间" prop="createTime" align="center" />
        <el-table-column label="联络人（关系）" prop="liaison" align="center">
          <template #default="{ row }">
            {{ `${row.liaison}（${row.relation || "--"}）` }}
          </template>
        </el-table-column>
        <el-table-column label="联络方式" prop="contactMode" align="center" />
        <el-table-column label="跟进状态" prop="followUpState" align="center" />
        <el-table-column label="催收状态" prop="urgeState" align="center" />
        <el-table-column label="沟通内容" prop="content" align="center">
          <template #default="{row}">
            <Tooltip :content="row.content" :length="15" />
          </template>
        </el-table-column>
        <el-table-column label="承诺还款信息/备注" prop="remarks" align="center" />
        <el-table-column label="催收员" prop="odvName" align="center" />
      </el-table>
      <pagination v-show="urgeDetail.total > 0" :total="urgeDetail.total" v-model:page="urgeDetail.query.pageNum"
        v-model:limit="urgeDetail.query.pageSize" @pagination="getUrgeList" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openDetail = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getUrgeTeamList, getUrgeDetailList } from "@/api/team/collectionlog";
const { proxy } = getCurrentInstance();
const showSearch = ref(false);
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    clientPhone: undefined,
  },
});
const { queryParams } = toRefs(data);
const columns = ref([
  { key: 0, label: `案件ID`, visible: true },
  { key: 1, label: `资产批次号【转让方-产品类型】`, visible: true },
  { key: 2, label: `所属公司`, visible: true },
  { key: 3, label: `姓名`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `催收员`, visible: true },
  { key: 6, label: `跟进状态`, visible: true },
  { key: 7, label: `催收状态`, visible: true },
]);

//催记详情
const openDetail = ref(false);
const urgeDetail = ref({
  total: 0,
  query: {
    caseId: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  list: [],
});

//获取列表
function getList() {
  loading.value = true;
  getUrgeTeamList(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    caseId: undefined,
    clientName: undefined,
    clientIdcard: undefined,
    clientPhone: undefined,
  };
  getList();
}

//获取催记详情列表
function getUrgeList() {
  getUrgeDetailList(urgeDetail.value.query).then((res) => {
    urgeDetail.value.total = res.total;
    urgeDetail.value.list = res.rows;
  });
}

//打开催记详情
function openUrgeDetails(row) {
  urgeDetail.value.query.pageNum = 1;
  urgeDetail.value.query.caseId = row.caseId;
  getUrgeDetailList(urgeDetail.value.query).then((res) => {
    urgeDetail.value.total = res.total;
    urgeDetail.value.list = res.rows;
    openDetail.value = true;
  });
}
</script>

<style scoped></style>
