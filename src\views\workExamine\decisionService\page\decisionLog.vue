<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="96px">
      <el-form-item label="委案日期" style="width: 336px">
        <el-date-picker v-model="queryParams.entrustingCaseDate" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="委案批次号" prop="entrustingBatchNum">
        <el-select v-model="queryParams.entrustingBatchNum" multiple collapse-tags collapse-tags-tooltip
          placeholder="请输入或选择委案批次号" clearable filterable :reserve-keyword="false" @visible-change="BatchList"
          :loading="selectLoading" style="width: 240px">
          <el-option v-for="item in batchs" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="机构名称" prop="teamName">
        <el-input v-model="queryParams.teamName" clearable @keyup.enter="handleQuery" placeholder="请输入机构名称"
          style="width: 240px" />
      </el-form-item>
      <el-form-item label="委案债权总额" style="width: 336px">
        <div class="section-range">
          <div class="range-input"> <el-input v-model="queryParams.totalAmount1" clearable /></div>
          <div class="transverse-line"> -</div>
          <div class="range-input"> <el-input v-model="queryParams.totalAmount2" clearable /></div>
        </div>
      </el-form-item>
      <el-form-item label="案件量" style="width: 336px">
        <div class="section-range">
          <div class="range-input"> <el-input v-model="queryParams.totalCaseNum1" clearable /></div>
          <div class="transverse-line">-</div>
          <div class="range-input"><el-input v-model="queryParams.totalCaseNum2" clearable /></div>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button :loading="loading" type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button :loading="loading" icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row class="mb8" :gutter="10">
      <el-button type="primary" plain :loading="loading" :disabled="ids.length == 0 || loading"
        @click="handleExport()">批量下载分案决策记录</el-button>
    </el-row>

    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="分案决策名称" align="center" key="decisionName" prop="decisionName" width="180px" />
      <el-table-column label="分案决策批次号" align="center" key="decisionBatchNum" prop="decisionBatchNum" width="180px" />
      <el-table-column label="委案批次号" align="center" key="entrustingBatchNums" prop="entrustingBatchNums" width="180" />
      <el-table-column label="总分配案件量" align="center" key="totalCaseNum" prop="totalCaseNum" width="110" />
      <el-table-column label="成功分配案量" align="center" key="successCaseNum" prop="successCaseNum" width="110" />
      <el-table-column label="失败分配案件" align="center" key="failCaseNum" prop="failCaseNum" width="110" />
      <el-table-column label="委案债权总额" align="center" key="totalAmount" prop="totalAmount" width="110" />
      <el-table-column label="分案类型" align="center" key="allocationType" prop="allocationType" width="100" :formatter="allocationTypeFor" />
      <el-table-column label="机构（数量）" align="center" key="teamName" prop="teamName" width="110">
        <template #default="{ row }">
          <div v-if="row.teamNameList && row.teamCaseNumList">
            <div v-for="(item,index) in row.teamNameList" :key="index">
              {{`${item}(${row.teamCaseNumList[index]})`}}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="委案时间" align="center" key="createTime" prop="createTime" width="100" />
      <el-table-column label="分案状态" align="center" key="status" prop="status" width="100" :formatter="statusFor" />
      <el-table-column label="分案结果" align="center" key="result" prop="result" min-width="100" :formatter="resultFor">
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180px">
        <template #default="{ row }">
          <el-button type="primary" link :loading="loading" :disabled="loading"
            @click="handleDownload(row)">下载明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup>
import {
  selectExeRecord,
} from "@/api/workExamine/decisionService";
import {
  getEntrustingCaseBatchNums,
} from "@/api/assets/caseApprove/allocated";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const route = useRoute()
const rangFields = ["entrustingCaseDate"]; //范围字段
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  decisionId:route.query.id,
  entrustingCaseDate: [],
  entrustingBatchNum: undefined,
  teamName: undefined,
  totalAmount1: undefined,
  totalAmount2: undefined,
  totalCaseNum1: undefined,
  totalCaseNum2: undefined,
});
const ids = ref([]);
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const selectLoading = ref(false);
const batchs = ref([]);

//获取列表数据
function getList() {
  loading.value = true;
  let req = JSON.parse(JSON.stringify(queryParams.value));
  req.entrustingBatchNumArr = req.entrustingBatchNum?.toString();
  req.entrustingBatchNum = undefined;
  selectExeRecord(proxy.addFieldsRange(req, rangFields)).then((res) =>{
    loading.value = false;
    dataList.value = res.rows;
    total.value = res.total;
  }).catch((error) =>{
    loading.value = false;
    dataList.value = [];
  })
}
getList();

//搜索
function handleQuery() {
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    decisionId:route.query.id,
    entrustingCaseDate: [],
    entrustingBatchNum: undefined,
    teamName: undefined,
    totalAmount1: undefined,
    totalAmount2: undefined,
    totalCaseNum1: undefined,
    totalCaseNum2: undefined,
  };
  handleQuery();
}

//获取批次号
function BatchList(val) {
  if (!val) return;
  selectLoading.value = true;
  getEntrustingCaseBatchNums()
    .then((res) => {
      batchs.value = res.data;
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

// 下载明细
function handleDownload(row) {
  if(row?.detailFileUrl){
    window.open(row.detailFileUrl)
  }else{
    proxy.$modal.msgWarning("没有明细文件！");
  }
}

//分案类型枚举
function allocationTypeFor(row){
  return  ['--','按户数','按金额','按数量'][row.allocationType];
}

//分案类型枚举
function statusFor(row){
  return  ['分案中','分案完成','分案失败'][row.status];
}

//分案类型枚举
function resultFor(row){
  return  ['成功','失败'][row.result];
}

//选择列表
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

//导出
function handleExport(){
  let req = {
    ids:ids.value
  }
  proxy.downloadforjson("/caseManage/decision/exeRecord/export",req, `分案决策记录_${new Date().getTime()}.xlsx`);
}
</script>

<style lang="scss" scoped>
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none;
}

.section-range {
  display: flex;

  .transverse-line {
    width: 25px;
    text-align: center;
  }

  .range-input {
    flex: 1;
  }
}
</style>
