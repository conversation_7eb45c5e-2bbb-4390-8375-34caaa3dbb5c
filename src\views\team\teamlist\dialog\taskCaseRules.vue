<template>
  <el-dialog
    title="接案规则设置"
    v-model="open"
    width="600px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" class="mt10" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item label="逾期天数" prop="overdueDaysBegin">
        <el-row>
          <el-col :span="11">
            <el-input
              v-model="form.overdueDaysBegin"
              clearable
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span>-</span>
          </el-col>
          <el-col :span="11">
            <el-input v-model="form.overdueDaysEnd" clearable />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="接案周期" prop="cycleUnit">
        <el-select
          v-model="form.cycleUnit"
          style="width: 180px"
          clearable
          filterable
          :reserve-keyword="false"
        >
          <el-option value="月" label="自然月"> </el-option>
          <el-option value="日" label="自然日"> </el-option>
        </el-select>
        <el-input
          class="ml20"
          v-model="form.cycle"
          clearable
          style="width: 180px"
          show-word-limit
          placeholder="请输入1~10000"
        />
      </el-form-item>
      <el-form-item>
        <div>注：自然月后面的数值，是以月为单位</div>
        <div class="ml28">自然日后面的数值，是以日为单位</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="TaskCaseRules">
import { getTeamAcceptRule, editTeamAcceptRule } from "@/api/team/teamlist/operate";
//全局配置
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
//表单属性
const open = ref(false);
const loading = ref(false);
const reqId = ref(undefined);
const checkCycleUnit = (rules, value, callback) => {
  if (value === "") {
    callback(new Error("请选择自然月！"));
  } else if (!form.value.cycle || form.value.cycle?.length == 0) {
    callback(new Error("请输入1~10000！"));
  } else if (form.value.cycle > 10000) {
    callback(new Error("请输入1~10000！"));
  } else if (!/^([1-9][0-9]*)$/.test(form.value.cycle)) {
    callback(new Error("数值请输入正整数！"));
  } else {
    callback();
  }
};

//检测逾期天数
const checkOverdueDays = (rules, value, callback) => {
  if (!form.value?.overdueDaysBegin) {
    callback(new Error("请输入逾期天数！"));
  }else if (!form.value?.overdueDaysEnd) {
    callback(new Error("请输入逾期天数！"));
  }else if (!/^(0|[1-9][0-9]*)$/.test(form.value?.overdueDaysBegin)) {
    callback(new Error("逾期天数请输入正整数或0！"));
  }else if (!/^(0|[1-9][0-9]*)$/.test(form.value?.overdueDaysEnd)) {
    callback(new Error("逾期天数请输入正整数或0！"));
  }else if (+form.value?.overdueDaysEnd < +form.value?.overdueDaysBegin) {
    callback(new Error("请输入逾期天数前区间不能大于于后区间！"));
  }else if (+form.value?.overdueDaysEnd > 10000 || +form.value?.overdueDaysBegin > 10000) {
    callback(new Error("逾期天数不能超过10000"));
  }else {
    callback();
  }
}
//表单参数
const data = reactive({
  form: {
    id:undefined,
    overdueDaysBegin: undefined,
    overdueDaysEnd: undefined,
    cycleUnit: undefined,
    cycle: undefined,
  },
  rules: {
    overdueDaysBegin: [{ required: true, validator: checkOverdueDays, trigger: "blur" }],
    cycleUnit: [{ required: true, validator: checkCycleUnit, trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//开启弹窗
function opendialog(row) {
  reqId.value = row.id;
  getTaskCase();
  open.value = true;
}

//获取接案规则
function getTaskCase() {
  let req = {
    teamId: reqId.value,
  };
  getTeamAcceptRule(req).then((res) => {
    form.value = res?.data || {
      id:undefined,
      overdueDaysBegin: undefined,
      overdueDaysEnd: undefined,
      cycleUnit: undefined,
      cycle: undefined,
    };
    form.value.id = reqId.value;
  });
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = JSON.parse(JSON.stringify(form.value));
      req.teamId = reqId.value;
      editTeamAcceptRule(req)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功");
          cancel();
          emit("getList");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    id:undefined,
    overdueDays: undefined,
    overdueDaysBegin: undefined,
    overdueDaysEnd: undefined,
    cycleUnit: undefined,
    cycle: undefined,
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped></style>
