<template>
    <div>
        <el-form inline :model="queryParams" ref="queryRef">
            <el-form-item label="签章名称" prop="">
                <el-input style="width: 240px" v-model="queryParams.signatureName" placeholder="请输入签章名称" />
            </el-form-item>
            <el-form-item label="签章ID" prop="">
                <el-input style="width: 240px" v-model="queryParams.signatureId" placeholder="请输入签章ID" />
            </el-form-item>
            <el-form-item label="状态" prop="">
                <el-select style="width: 240px" v-model="queryParams.stutas" placeholder="请选择状态">
                    <el-option v-for="item in stutasOption" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="operation-area">
            <el-button type="primary" @click="addSignatureInfo()">添加签章</el-button>
            <el-button type="primary" @click="handleDownload()">批量导出</el-button>
        </div>
        <el-table>
            <el-table-column prop="" label="签章名称" />
            <el-table-column prop="" label="签章类型" />
            <el-table-column prop="" label="签章ID" />
            <el-table-column prop="" label="状态" />
            <el-table-column prop="" label="备注" />
            <el-table-column prop="" label="修改时间" />
            <el-table-column prop="" label="操作">
                <template #default="{ row }">
                    <div>
                        <el-button type="text" @click="addSignatureInfo(row)">修改</el-button>
                        <el-button type="text" @click="removeSignature(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        <signatureInfo ref="signatureInfoRef" :getlist="getList" />
    </div>
</template>
<script setup>
import signatureInfo from '../dialog/signatureInfo';
// 全局变量
const { proxy } = getCurrentInstance()
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        signatureName: undefined,
        signatureType: undefined,
        signatureId: undefined,
        stutas: undefined,
    }
})
const stutasOption = ref([
    { label: '启用', value: 1 },
    { label: '停用', value: 0 },
])
const total = ref(0)
const { queryParams } = toRefs(data)
// 获取数据
function getList() {

}
getList()

// 添加、编辑签章
function addSignatureInfo(row) {
    const data = { title: '添加签章' }
    if (row) {
        data.title = '修改签章'
        data.id = row.id
    }
    proxy.$refs['signatureInfoRef'].openDialog(data)
}
// 删除
function removeSignature(row) {

}
// 批量导出
function handleDownload() {

}

</script>


