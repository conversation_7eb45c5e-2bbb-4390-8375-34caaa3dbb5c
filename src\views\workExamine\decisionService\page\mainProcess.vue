<template>
  <div id="container" :key="route.params?.id" style="width: 100vw; height: 100vh"></div>
  <TeleportContainer />
  <!-- 保存设置 -->
  <div class="submit-box">
    <el-button type="primary" plain @click="submit">保存</el-button>
    <el-button plain @click="toBack">取消</el-button>
  </div>
  <!-- 新增决策节点 -->
  <addCaseNode ref="addCaseNodeRef" @getList="getList" />
  <!-- 新增决策规则 -->
  <addCaseRules ref="addCaseRulesRef" @getList="getList" />
</template>
<script setup>
const { proxy } = getCurrentInstance();
const route = useRoute();
const store = useStore();
import {
  selectNodeList,
  getSelectRule,
  selectListWithNodeDetail,
  selectListWithRuleDetail,
} from "@/api/workExamine/parameter";
import {
  saveStrategyApi,
  getMappingRelationJSON
} from "@/api/workExamine/decisionService";
import AlgoNode from "./component/AlgoNode.vue";
import addCaseNode from "../../parameter/dialog/addCaseNode.vue";
import addCaseRules from "../../parameter/dialog/addCaseRules.vue";
import { register, getTeleport } from "@antv/x6-vue-shape";
import { Graph, Shape } from "@antv/x6";
import { Stencil } from "@antv/x6-plugin-stencil";
import { Transform } from "@antv/x6-plugin-transform";
import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { History } from "@antv/x6-plugin-history";
import insertCss from "insert-css";
//分案规则
const rulesList = ref([]);
const nodeList = ref([]);
const graphObj = ref(undefined);
const writePassList = ref([
  "开始-规则",
  "规则-节点",
  "节点-结束"
])

//分案节点
function getCaseNodes() {
  let req = {
    pageNum: 1,
    pageSize: 100,
  };
  return new Promise((reslove, reject) => {
    getSelectRule(req)
      .then((res) => {
        rulesList.value = res.rows.filter(item => item.status == "0");
        reslove(true);
      })
      .catch(() => {
        rulesList.value = [];
        reject();
      });
  });
}

//获取分案规则
function getCaseRules() {
  let req = {
    pageNum: 1,
    pageSize: 100,
  };
  return new Promise((reslove, reject) => {
    selectNodeList(req)
      .then((res) => {
        nodeList.value = res.rows.filter(item => item.status == "0");
        reslove(true);
      })
      .catch(() => {
        nodeList.value = [];
        reject();
      });
  });
}

async function getMap() {
  await getCaseRules();
  await getCaseNodes();
  // #region 初始化画布
  const graph = new Graph({
    container: document.getElementById("graph-container"),
    grid: true,
    panning: {
      enabled: true,
      modifiers: [],
      eventTypes: ["leftMouseDown"],
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      factor: 0.8,
      maxScale: 3,
    },
    connecting: {
      router: "manhattan",
      connector: {
        snap: true,
        allowBlank: false,
        allowLoop: false,
        allowNode: false,
        highlight: true,
        name: "rounded",
        args: {
          radius: 8,
        },
      },
      anchor: "center",
      connectionPoint: "anchor",
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: "#A2B1C3",
              strokeWidth: 2,
              targetMarker: {
                name: "block",
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: 0,
        });
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet;
      },
      validateMagnet({ magnet }) {
        return magnet.getAttribute("port-group") !== "in";
      },
    },
    validateConnection({ sourceMagnet, targetMagnet }) {
      // 只能从输出链接桩创建连接
      if (!sourceMagnet || sourceMagnet.getAttribute("port-group") === "in") {
        return false;
      }
      // 只能连接到输入链接桩
      if (!targetMagnet || targetMagnet.getAttribute("port-group") !== "in") {
        return false;
      }
      return true;
    },
    highlighting: {
      magnetAdsorbed: {
        name: "stroke",
        args: {
          attrs: {
            fill: "#5F95FF",
            stroke: "#5F95FF",
          },
        },
      },
    },
  });
  // #endregion

  graphObj.value = graph;
  // #region 使用插件
  graph
    .use(
      new Transform({
        resizing: true,
        rotating: true,
      })
    )
    .use(
      new Selection({
        rubberband: true,
        showNodeSelectionBox: true,
      })
    )
    .use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History());
  // #endregion

  // #region 初始化 stencil
  const stencil = new Stencil({
    title: "流程图",
    target: graph,
    stencilGraphWidth: 300,
    stencilGraphHeight: 0,
    collapsable: true,
    groups: [
      {
        title: "基础流程图",
        name: "group1",
        graphHeight: 0,
      },
      {
        title: '规则',
        name: 'group2',
        graphHeight: 0,
      },
      {
        title: '分案节点',
        name: 'group3',
        graphHeight: 0,
      },
    ],
    layoutOptions: {
      columns: 1,
      columnWidth: 240,
      rowHeight: 60,
    },
  });
  document.getElementById("stencil").appendChild(stencil.container);
  // #endregion

  // #region 快捷键与事件
  graph.bindKey(["meta+c", "ctrl+c"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.copy(cells);
    }
    return false;
  });
  graph.bindKey(["meta+x", "ctrl+x"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.cut(cells);
    }
    return false;
  });
  graph.bindKey(["meta+v", "ctrl+v"], () => {
    if (!graph.isClipboardEmpty()) {
      const cells = graph.paste({ offset: 32 });
      graph.cleanSelection();
      graph.select(cells);
    }
    return false;
  });

  // undo redo
  graph.bindKey(["meta+z", "ctrl+z"], () => {
    if (graph.canUndo()) {
      graph.undo();
    }
    return false;
  });
  graph.bindKey(["meta+shift+z", "ctrl+shift+z"], () => {
    if (graph.canRedo()) {
      graph.redo();
    }
    return false;
  });

  // select all
  graph.bindKey(["meta+a", "ctrl+a"], () => {
    const nodes = graph.getNodes();
    if (nodes) {
      graph.select(nodes);
    }
  });

  // delete
  graph.bindKey("backspace", () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.removeCells(cells);
    }
  });

  // zoom
  graph.bindKey(["ctrl+1", "meta+1"], () => {
    const zoom = graph.zoom();
    if (zoom < 1.5) {
      graph.zoom(0.1);
    }
  });
  graph.bindKey(["ctrl+2", "meta+2"], () => {
    const zoom = graph.zoom();
    if (zoom > 0.5) {
      graph.zoom(-0.1);
    }
  });

  // 控制连接桩显示/隐藏
  const showPorts = (ports, show) => {
    for (let i = 0, len = ports.length; i < len; i += 1) {
      ports[i].style.visibility = show ? "visible" : "hidden";
    }
  };
  graph.on("node:mouseenter", () => {
    const container = document.getElementById("graph-container");
    const ports = container.querySelectorAll(".x6-port-body");
    showPorts(ports, true);
  });
  graph.on("node:dblclick", (data) => {
    let item = data.node?.getData();
    if (item.status == "rules") {
      console.log(item)
      setRules(item.id,item.label);
    }
    if (item.status == "keep") {
      setKeeps(item.id);
    }
  });
  graph.on("node:mouseleave", () => {
    const container = document.getElementById("graph-container");
    const ports = container.querySelectorAll(".x6-port-body");
    showPorts(ports, false);
  });
  graph.on("edge:mouseenter", ({ cell }) => {
    cell.addTools([
      {
        name: "source-arrowhead",
      },
      {
        name: "target-arrowhead",
        args: {
          attrs: {
            fill: "red",
          },
        },
      },
    ]);
  });
  graph.on("edge:mouseleave", ({ cell }) => {
    cell.removeTools();
  });
  // graph.centerContent()
  // #endregion

  // #region 初始化图形
  const ports = {
    groups: {
      top: {
        position: "top",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: "#5F95FF",
            strokeWidth: 1,
            fill: "#fff",
            style: {
              visibility: "hidden",
            },
          },
          img: {
            "xlink:href":
              "https://gw.alipayobjects.com/zos/antfincdn/FLrTNDvlna/antv.png",
            width: 16,
            height: 16,
            left: 20,
            x: 12,
            y: 12,
          },
        },
      },
      right: {
        position: "right",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: "#5F95FF",
            strokeWidth: 1,
            fill: "#fff",
            style: {
              visibility: "hidden",
            },
          },
        },
      },
      bottom: {
        position: "bottom",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: "#5F95FF",
            strokeWidth: 1,
            fill: "#fff",
            style: {
              visibility: "hidden",
            },
          },
        },
      },
      left: {
        position: "left",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: "#5F95FF",
            strokeWidth: 1,
            fill: "#fff",
            style: {
              visibility: "hidden",
            },
          },
        },
      },
    },
    items: [
      {
        group: "top",
      },
      {
        group: "right",
      },
      {
        group: "bottom",
      },
      {
        group: "left",
      },
    ],
  };

  //节点组一
  register({
    shape: "custom-rect",
    width: 150,
    height: 45,
    component: AlgoNode,
    ports: { ...ports },
  });

  Graph.registerNode(
    "custom-polygon",
    {
      inherit: "polygon",
      width: 100,
      height: 45,
      attrs: {
        body: {
          strokeWidth: 1,
          stroke: "#5F95FF",
          fill: "#fff",
        },
        text: {
          fontSize: 12,
          fill: "#262626",
        },
      },
      ports: {
        ...ports,
        items: [
          {
            group: "top",
          },
          {
            group: "bottom",
          },
        ],
      },
    },
    true
  );

  const r1 = graph.createNode({
    shape: "custom-rect",
    label: "开始",
    attrs: {
      body: {
        rx: 20,
        ry: 26,
      },
    },
    data: {
      status: "start",
      label: "开始",
      edgeStatus:"开始"
    },
  });
  const r2 = graph.createNode({
    shape: "custom-rect",
    label: "结束",
    attrs: {
      body: {
        rx: 20,
        ry: 26,
      },
    },
    data: {
      status: "end",
      label: "结束",
      edgeStatus:"结束"
    },
  });
  //获取分案规则
  let createRuleNode = [];
  rulesList.value.forEach((item, index) => {
    createRuleNode[index] = graph.createNode({
      shape: "custom-rect",
      label: item.ruleName,
      attrs: {
        body: {
          rx: 20,
          ry: 26,
          event: "node:rules",
        },
      },
      data: {
        id: item.id,
        status: "rules",
        label: item.ruleName,
        edgeStatus:"规则"
      },
    });
  });
  //渲染分案节点
  let createNodeist = [];
  nodeList.value.forEach((item, index) => {
    createNodeist[index] = graph.createNode({
      shape: "custom-rect",
      label: item.nodeName,
      attrs: {
        body: {
          rx: 20,
          ry: 26,
          event: "node:keep",
        },
      },
      data: {
        id: item.id,
        status: "keep",
        label: item.nodeName,
        edgeStatus:"节点"
      },
    });
  });
  stencil.load([r1, r2], "group1");
  stencil.load(createRuleNode, "group2");
  stencil.load(createNodeist, "group3");
  getMappingRelation()
}

//渲染css
function preWork() {
  // 这里协助演示的代码，在实际项目中根据实际情况进行调整
  const container = document.getElementById("container");
  const stencilContainer = document.createElement("div");
  stencilContainer.id = "stencil";
  const graphContainer = document.createElement("div");
  graphContainer.id = "graph-container";
  container.appendChild(stencilContainer);
  container.appendChild(graphContainer);

  insertCss(`
    #container {
      display: flex;
      border: 1px solid #dfe3e8;
    }
    #stencil {
      width: 320px;
      height: 100%;
      position: relative;
      border-right: 1px solid #dfe3e8;
    }
    #graph-container {
      width: calc(100% - 180px);
      height: 100%;
    }
    .x6-widget-stencil  {
      background-color: #fff;
    }
    .x6-widget-stencil-title {
      background-color: #fff;
    }
    .x6-widget-stencil-group-title {
      background-color: #fff !important;
    }
    .x6-widget-transform {
      margin: -1px 0 0 -1px;
      padding: 0px;
      border: 1px solid #239edd;
    }
    .x6-widget-transform > div {
      border: 1px solid #239edd;
    }
    .x6-widget-transform > div:hover {
      background-color: #3dafe4;
    }
    .x6-widget-transform-active-handle {
      background-color: #3dafe4;
    }
    .x6-widget-transform-resize {
      border-radius: 0;
    }
    .x6-widget-selection-inner {
      border: 1px solid #239edd;
    }
    .x6-widget-selection-box {
      opacity: 0;
    }
    .x6-node rect {
      fill: #fff;
      stroke: #409EFF;
      border: 1px solid rgba(0, 0, 0, 10%);
      box-shadow: 0 -1px 4px 0 rgba(209, 209, 209, 50%), 1px 1px 4px 0 rgba(217, 217, 217, 50%);
    }
    .x6-node foreignObject{
      width:150px;
      height:45px;
    }
    .x6-widget-stencil-group:last-of-type .x6-widget-stencil-group-content{
      margin-bottom:80px
    }
  `);
}

//设置规则
function setRules(id,name) {
  console.log(name)
  let reqData = {
    id:id
  }
  selectListWithRuleDetail(reqData).then((res) =>{
    let req = {
      id,
      ruleName:name,
      detailList:res.data
    }
    proxy.$refs["addCaseRulesRef"].opendialog(1, req);
  }).catch((error) =>{
    proxy.$modal.msgWarning("无法获取该规则详情！");
  })
}

//设置接收方
function setKeeps(id) {
  let reqData = {
    id,
  };
  selectListWithNodeDetail(reqData)
    .then((res) => {
      let req = JSON.parse(JSON.stringify(res.data));
      proxy.$refs["addCaseNodeRef"].opendialog(1, req);
    })
    .catch((error) => {
      proxy.$modal.msgWarning("无法获取该机构详情！");
    });
}

//提交信息
async function submit(){
  let ceils = graphObj.value.toJSON().cells
  let req = await vaildateCeils(ceils);
  if(req){
    req.id = route.params.id;
    console.log(req)
    saveStrategyApi(req)
      .then((res) => {
        proxy.$modal.msgSuccess("操作成功！");
        toBack();
      })
      .catch((error) => {
        console.log(error)
      });
  }
}

//检验数值，整理JSON数据
function vaildateCeils(ceils){
  return  new Promise((reslove) => { 
    if(ceils.length > 0){
      //判断是否含有开始结束节点
      let startFlag = true;
      let endFlag = true;
      for(let i = 0; i<ceils.length; i++){
        let item = ceils[i];
        if(item?.shape == 'edge') continue;
        if(item?.label == "开始"){
          startFlag = false
        }
        if(item?.label == "结束"){
          endFlag = false;
        }
      }
      if(startFlag){
        proxy.$modal.msgWarning("请设置开始节点！");
        reslove(false);
      }
      if(endFlag){
        proxy.$modal.msgWarning("请设置结束节点！");
        reslove(false);
      }
      //节点对应关系
      let mappingPojoList = []
      //验证节点
      ceils.forEach((item,index) =>{
        if(item?.shape !== "edge") return true;
        if(item?.shape == "edge"){
          let sourceId = item.source.cell;
          let targetId = item.target.cell;
          let sourceNode = ceils.find(v =>v.id == sourceId);
          let targetNode = ceils.find(v =>v.id == targetId);
          let nodeLink = `${sourceNode.data.edgeStatus}-${targetNode.data.edgeStatus}`;
          if(writePassList.value.includes(nodeLink)){
            if(sourceNode.data.edgeStatus == '规则' && targetNode.data.edgeStatus == "节点"){
              mappingPojoList.push({
                sort:sourceNode.position.y,
                rule:sourceNode.data.label,
                ruleId:sourceNode.data.id,
                node:targetNode.data.label,
                nodeId:targetNode.data.id,
              })
            }
          }else{
            proxy.$modal.msgWarning(`流程图不能设置${nodeLink}的连接关系`);
            reslove(false)
          }
        }
      })
      //验证一个规则和一个节点是否为对应关系
      let rulesNodeList = ceils.filter(item => item?.shape == 'custom-rect' && item?.data?.status == "rules");
      let keepNodeList = ceils.filter(item => item?.shape == 'custom-rect' && item?.data?.status == "keep");
      if(rulesNodeList.length !== keepNodeList.length){
        proxy.$modal.msgWarning(`规则和分案节点必须为一一对应关系`);
        reslove(false)
      }
      if(rulesNodeList.length > 20){
        proxy.$modal.msgWarning(`流程图的规则数量不能超过20个！`);
        reslove(false)
      }
      if(keepNodeList.length > 20){
        proxy.$modal.msgWarning(`流程图的分案节点数量不能超过20个！`);
        reslove(false)
      }
      // 不能存在两个一样的分案节点
      // const checkSameKeepNode = keepNodeList.some((item,index) =>{
      //   return (
      //     keepNodeList.findIndex((v,i) =>{
      //       return (i !== index && JSON.stringify(v.label) === JSON.stringify(item.label))
      //     }) !== -1
      //   )
      // })
      // if(checkSameKeepNode){
      //   proxy.$modal.msgWarning(`不能出现一样的分案节点！`);
      //   reslove(false)
      // }


      //验证节点，但是没有线
      let edgeCount = ceils.filter(item => item?.shape == 'edge')?.length;
      let nodeCount = ceils.filter(item => item?.shape == 'custom-rect')?.length;
      if(nodeCount < 4){
        proxy.$modal.msgWarning(`请把流程图补充完整！`);
        reslove(false)
      }else{
        //计算差值
        let differenceValue = (nodeCount -4)/2-1;
        if(nodeCount + differenceValue !== edgeCount){
          proxy.$modal.msgWarning(`请把流程图补充完整！`);
          reslove(false)
        }
      }
      reslove({
        mappingRelation:JSON.stringify(ceils),
        mappingPojoList:mappingPojoList,
      })
    }else{
      proxy.$modal.msgWarning(`请先设置分案决策图！`);
      reslove(false)
    }
  })
}

//初始化渲染
function getMappingRelation(){
  let req = {
    id:route.params.id
  }
  getMappingRelationJSON(req).then((res) =>{
    if(res.data && res.data?.length > 0){
      let cells = JSON.parse(res.data)
      graphObj.value.fromJSON(cells)
    }
  }).catch((error) =>{
    consolle.log('不存在ceils')
  })
}

//返回
const toBack = () => {
  store.dispatch("tagsView/delCachedView", { name: "DecisionService" });
  const obj = { path: "/workExamine/decisionService" };
  proxy.$tab.closeOpenPage(obj);
};

onMounted(() => {
  // 为了协助代码演示
  preWork();
  getMap();
});
</script>

<style lang="scss" scoped>
.submit-box {
  width: 100vw;
  height: 60px;
  line-height: 60px;
  background-color: white;
  box-shadow: 0px 0px 1px rgb(0 0 0 / 35%);
  position: fixed;
  text-align: left;
  padding-left: 50px;
  bottom: 0px;
}
</style>
