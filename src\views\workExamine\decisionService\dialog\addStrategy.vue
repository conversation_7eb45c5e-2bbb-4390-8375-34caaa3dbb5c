<template>
  <el-dialog :title="title" v-model="open" width="550px" :before-close="cancel" append-to-body>
    <el-form :model="form" class="mt10" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item class="mar0" label="分案决策名称" prop="decisionName">
        <el-input v-model="form.decisionName" style="width:240px" clearable
          :maxlength="30" 
          show-word-limit
          placeholder="请输入分案决策名称" />
      </el-form-item>
      <el-form-item class="mar0" label="优先级" prop="sort">
        <el-input type="text" v-model="form.sort" style="width:240px" onkeyup="value=value.replace(/[^0-9]/g, '')"
          oninput="value=value.replace(/[^0-9]/g, '')" placeholder="请输入优先级" />
      </el-form-item>
      <!-- <el-form-item label="分案时间" prop="divisionTime">
        <el-time-select v-model="form.divisionTime" style="width:240px" start="07:00" step="00:30" end="8:30"
          format="hh:mm:ss" placeholder="请选择分案时间" />
      </el-form-item> -->
      <el-form-item class="mar0" label="状态" prop="status">
        <el-switch class="myswitch" v-model="form.status" active-color="#409eff" :active-value="0" :inactive-value="1"
          active-text="开" inactive-text="关" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="addStrategy">
import { addStrategyApi, updateStrategyApi } from "@/api/workExamine/decisionService";
//全局属性
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
const open = ref(false);
const loading = ref(false);
const title = ref('新增策略')
//表单属性
const data = reactive({
  form: {
    decisionName: undefined,
    sort: undefined,
    status: 0,
  },
  rules: {
    decisionName: [
      { required: true, message: "请输入分案决策名称", trigger: "blur" },
      { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: '请输入文字/字母/数字', trigger: 'blur' }
    ],
    divisionTime: [{ required: true, message: "请选择分案时间", trigger: "blur" }],
    sort: [
      { required: true, message: "请输入优先级", trigger: "blur" },
      { pattern: /^([1-9][0-9]?|100)$/, message: '请输入 1 ~ 100 区间的数字', trigger: 'blur' }
    ],
    status: [{ required: true, message: "请选择状态", trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);
//开启弹窗
function opendialog(row) {
  open.value = true;
  if (row) {
    title.value = '修改策略'
    form.value = row;
  }
}
//取消
function cancel() {
  reset();
  open.value = false;
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let reqForm = JSON.parse(JSON.stringify(form.value));
      const api = reqForm.id ? updateStrategyApi : addStrategyApi
      api(reqForm).then((res) => {
        loading.value = false;
        proxy.$modal.msgSuccess("操作成功");
        cancel();
        emit("getList");
      }).catch(() => {
        loading.value = false;
      });
    } else {
      loading.value = false;
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    decisionName: undefined,
    sort: undefined,
    status: 0,
  };
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped></style>
