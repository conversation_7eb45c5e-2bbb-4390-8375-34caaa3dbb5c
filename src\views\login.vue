<template>
  <div class="login">
    <div class="login-head">
      <img class="head-logo" src="../assets/images/login-logo.png" />
      <div class="head-title">
        <div>为了更好的网站体验，建议您使用谷歌、火狐、360极速模式等高版本的浏览器</div>
        <div>COPYRIGHT © 2019 深圳债卫士科技信息有限公司，官方客服：400-000-7748</div>
      </div>
    </div>

    <div class="login-content">
      <el-form
        ref="loginRef"
        v-if="!finUsernameCheck"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <h3 class="title">— &nbsp;欢迎登录 M+资产管理中心&nbsp; —</h3>
        <el-form-item prop="username">
          <el-input
            v-model.trim="loginForm.username"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="账号"
          >
            <template #prefix>
              <svg-icon icon-class="login-user" class="el-input__icon input-icon" />
              <span class="line"></span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model.trim="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
            show-password
          >
            <template #prefix>
              <svg-icon icon-class="login-pwd" class="el-input__icon input-icon" />
              <span class="line"></span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaOnOff">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 57%"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon icon-class="login-yzm" class="el-input__icon input-icon" />
              <span class="line"></span>
            </template>
          </el-input>
          <div class="login-code">
            <div v-loading="codeloading" style="width: 100%; height: 100%">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </div>
        </el-form-item>
        <el-checkbox
          class="login-remember"
          v-model="loginForm.rememberMe"
          style="margin: 0px 0px 25px 0px"
        >记住密码</el-checkbox>
        <el-form-item style="width: 100%">
          <el-button
            class="login-btn"
            :loading="loading"
            size="large"
            style="width: 100%"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      <!-- 手机验证 -->
      <el-form
        v-if="openPhone == 0 && finUsernameCheck"
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <h3 class="title">安全验证</h3>
        <el-form-item prop="phonenumber " class="mt10">
          <el-input
            v-model="loginForm.phonenumber"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="手机号码"
            disabled
          >
            <template #prefix>
              <svg-icon icon-class="user" class="el-input__icon input-icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="smsCode" class="mt10">
          <el-input
            v-model="loginForm.smsCode"
            size="large"
            auto-complete="off"
            placeholder="短信验证码"
            style="width: 63%"
            @keyup.enter="handleLoginByphone"
          >
            <template #prefix>
              <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
            </template>
          </el-input>
          <div class="login-code">
            90
            <el-button
              class="ml10 phone-code-buttton"
              v-if="second == 0"
              :disabled="!loginForm.phonenumber ||
              (loginForm.phonenumber && loginForm.phonenumber.length == 0)
              "
              type="primary"
              @click="getSmsCode()"
            >获取验证码</el-button>
            <el-button class="ml10 phone-code-buttton" v-else type="info" disabled>{{ second }}秒后再获取</el-button>
          </div>
        </el-form-item>
        <el-form-item style="width: 100%; margin-top: 40px">
          <el-button
            :loading="phoneLoading"
            size="large"
            type="primary"
            style="width: 100%"
            @click.prevent="handleLoginByphone"
          >
            <span v-if="!phoneLoading">验证并登录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
        <el-form-item style="width: 100%; margin-top: 10px" @click="backUserName()">
          <el-icon :size="14" color="#999">
            <ArrowLeft />
          </el-icon>
          <span class="phone-back">返回账号登录页面</span>
        </el-form-item>
      </el-form>
    </div>
    <!-- 合规宣导 -->
    <Propaganda ref="propagandaRef" />
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import Propaganda from "@/components/Propaganda/index.vue";
import { getCodeImg, sendSmsCode } from "@/api/login";
import { verificationCode } from "@/api/authentication.js";
import { getLoginPersuade } from "@/api/system/legal/propagate";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { getFingerprint, getIpAddress, findPrivateIP } from "@/utils/common";

const store = useStore();
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  phonenumber: undefined,
  smsUid: undefined,
  smsCode: undefined,
  uid: undefined,
  code: "",
  uuid: "",
  internalIp: "", // 内网ip
  browserFingerprint: "" // 浏览器指纹
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
  phonenumber: [{ required: true, trigger: "change", message: "请输入验证码" }],
  smsCode: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const codeloading = ref(false);
const loading = ref(false);
// 验证码开关
const captchaOnOff = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);
const vLogin = ref("v2.2");
//短信开关
const openPhone = ref(1);
const second = ref(undefined);
const phoneLoading = ref(false);
const finUsernameCheck = ref(false);
//定时器
const cookieTimer = ref(undefined);

onMounted(() => {
  getFingerprint().then(res => {
    loginForm.value.browserFingerprint = res.murmur;
  });
  getIpAddress().then(res => {
    loginForm.value.internalIp = findPrivateIP(res);
  });
  const { ticket, phonenumber, smsUid } = route.query;
  loginForm.value = { ...loginForm.value, phonenumber, smsUid };
  openPhone.value = route.query?.openPhone ?? 1;
  finUsernameCheck.value = Boolean(route.query?.openPhone == 0);
  if (ticket && smsUid) {
    verificationCode({ smsUid })
      .then(res => {
        console.log(224, res);
        if (res.data == 0) {
          openPhone.value = 0;
          finUsernameCheck.value = true;
          setSendSmsCode({ smsUid });
        } else {
          openPhone.value = 1;
          finUsernameCheck.value = false;
        }
      })
      .catch(() => {
        openPhone.value = 1;
        finUsernameCheck.value = false;
      });
  }
});

// 判断用户是否已经登录
function isLogin() {
  return new Promise((resolve, reject) => {
    if (getToken()) {
      proxy.$modal.msgWarning(
        "同一浏览器同一系统只允许登录一个账号，请先退出账号再进行登录！"
      );
      reject(
        new Error(
          "同一浏览器同一系统只允许登录一个账号，请先退出账号再进行登录！"
        )
      );
    } else {
      resolve();
    }
  });
}

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      isLogin().then(() => {
        loading.value = true;
        // 勾选了需要记住密码设置在cookie中设置记住用户明和名命
        if (loginForm.value.rememberMe) {
          Cookies.set("username", loginForm.value.username, { expires: 30 });
          Cookies.set("password", encrypt(loginForm.value.password), {
            expires: 30
          });
          Cookies.set("rememberMe", loginForm.value.rememberMe, {
            expires: 30
          });
        } else {
          // 否则移除
          Cookies.remove("username");
          Cookies.remove("password");
          Cookies.remove("rememberMe");
        }
        // 调用action的登录方法
        let reqForm = JSON.parse(JSON.stringify(loginForm.value));
        reqForm.username = encrypt(reqForm.username);
        reqForm.password = encrypt(reqForm.password);
        store
          .dispatch("Login", reqForm)
          .then(data => {
            loginForm.value.phonenumber = data?.phonenumber;
            loginForm.value.smsUid = data?.smsUid;
            openPhone.value = data?.securityVerification ?? 1;
            if (openPhone.value == 0) {
              finUsernameCheck.value = true;
              loading.value = false;
            } else {
              getLoginPersuade()
                .then(res => {
                  proxy.$refs["propagandaRef"].opendialog(res.msg);
                })
                .catch(() => {
                  // 重新获取验证码
                  if (captchaOnOff.value) {
                    getCode();
                  }
                })
                .finally(() => {
                  loading.value = false;
                });
            }
            //router.push({ path: redirect.value || "/" });
          })
          .catch(() => {
            loading.value = false;
            // 重新获取验证码
            if (captchaOnOff.value) {
              getCode();
            }
          });
      });
    }
  });
}

//返回账号登陆
function backUserName() {
  loginForm.value.phonenumber = undefined;
  loginForm.value.smsCode = undefined;
  loginForm.value.smsUid = undefined;
  loginForm.value.uid = undefined;
  loginForm.value.code = "";
  proxy.resetForm("loginRef");
  finUsernameCheck.value = false;
  getCode();
}

//手机登录
function handleLoginByphone() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      isLogin().then(() => {
        phoneLoading.value = true;
        // 调用action的登录方法
        store
          .dispatch("SmsLogin", loginForm.value)
          .then(() => {
            phoneLoading.value = false;
            getLoginPersuade()
              .then(res => {
                proxy.$refs["propagandaRef"].opendialog(res.msg);
              })
              .catch(() => {
                phoneLoading.value = false;
              });
            //router.push({ path: redirect.value || "/" });
          })
          .catch(() => {
            phoneLoading.value = false;
            // 重新获取验证码
            if (captchaOnOff.value) {
              getCode();
            }
          })
          .catch(() => {
            phoneLoading.value = false;
          });
      });
    }
  });
}

//获取短信验证码
function getSmsCode() {
  let req = {
    smsUid: loginForm.value.smsUid
  };
  setSendSmsCode(req);
}
function setSendSmsCode(req) {
  sendSmsCode(req)
    .then(res => {
      loginForm.value.uid = res.data.uid;
      second.value = 60;
      reciprocalTime();
    })
    .catch(() => {
      second.value = 0;
    });
}
//60秒倒数
function reciprocalTime() {
  cookieTimer.value = setInterval(() => {
    if (second.value > 0) {
      second.value--;
      Cookies.set("loginSecond", second.value, { expires: 30 });
    } else {
      second.value == 0;
      Cookies.set("loginSecond", second.value, { expires: 30 });
      clearInterval(cookieTimer.value);
    }
  }, 1000);
}

//获取记录的时间
function getCookieSecond() {
  const count = Cookies.get("loginSecond");
  second.value = count > 0 ? count : 0;
  reciprocalTime();
}
getCookieSecond();

function getCode() {
  console.log(vLogin.value);
  codeloading.value = true;
  getCodeImg().then(res => {
    codeloading.value = false;
    captchaOnOff.value =
      res.captchaOnOff === undefined ? true : res.captchaOnOff;
    if (captchaOnOff.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

function redirectToBeian() {
  const beianURL = "https://beian.miit.gov.cn/#/Integrated/index";
  window.open(beianURL, "_blank");
}

getCode();
getCookie();

onBeforeUnmount(() => {
  Cookies.remove("loginSecond");
  clearInterval(cookieTimer.value);
});
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
  min-width: 100%;
  background: url("../assets/images/login-bg.jpg") 100% no-repeat;
  background-size: 100% 100%;
  // background: linear-gradient(180deg, #2C6FF4 0%, #00C4FF 100%);
  // background-size: cover;
  .login-head {
    width: 100%;
    padding: 0 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .head-logo {
      display: block;
      width: 220px;
    }
    .head-title {
      font-size: 14px;
      color: #ffffff;
      line-height: 21px;
      font-family: MicrosoftYaHei;
    }
  }
  .login-content {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: flex-end;
    .login-form {
      width: 391px;
      height: 443px;
      padding: 45px;
      background: url("../assets/images/login-form-bg.png") 100% no-repeat;
      background-size: 100% 100%;
      margin-right: 20%;
      margin-top: 85px;
      .title {
        margin: 0px auto 30px auto;
        text-align: left;
        color: #10f2fd;
        font-size: 20px;
      }
      .line {
        display: inline-block;
        width: 1px;
        height: 15px;
        background-color: #113682;
        margin: 0 15px 0 10px;
      }
      .el-input {
        height: 45px;
        :deep(.el-input__wrapper) {
          border-radius: 2px;
          input {
            height: 43px;
          }
        }
      }
      .input-icon {
        height: 21px;
        width: 18px;
      }
      .login-code {
        width: 40%;
        height: 45px;
        float: right;
        margin-left: 9px;
        .login-code-img {
          height: 45px;
          cursor: pointer;
          vertical-align: middle;
        }
      }
      .login-remember {
        :deep(.el-checkbox__label) {
          color: #ffffff;
        }
      }
      :deep(.el-button--large.login-btn) {
        height: 45px;
        border-radius: 0;
        background: url("../assets/images/login-bth-bg.png") 100% no-repeat;
        background-size: 100% 100%;
        border: unset;
        font-size: 16px;
        font-family: SourceHanSansCN-Regular;
        font-weight: normal;
        color: #ffffff;
      }
    }
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.phone-code-buttton {
  line-height: 40px;
  height: 40px;
  width: 120px;
}

.phone-back {
  color: #999;
  font-size: 12px;
  cursor: pointer;
}

.phone-back:hover {
  color: #b4b3b3;
}

//适配2560屏幕宽度
@media screen and (min-width: 2560px) {
  .login-img {
    height: 580px;
  }
}

//适配1440屏幕宽度
@media screen and (max-width: 1560px) {
  .login-img {
    scale: 0.8;
    top: 40px;
    left: 100px;
  }
}
</style>
