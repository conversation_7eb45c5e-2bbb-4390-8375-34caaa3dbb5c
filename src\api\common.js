import request from '@/utils/request'

// 查询审批列表 
export function zcQueryApproveList(data) {
    return request({
        url: '/caseManage/zws_zc_approve/zcQueryApproveList',
        method: 'post',
        data,
    })
}
// 通过 
export function zcApprovePass(data) {
    return request({
        url: '/caseManage/zws_zc_approve/zcApprovePass',
        method: 'post',
        data,
    })
}
// 不通过
export function zcApproveReject(data) {
    return request({
        url: '/caseManage/zws_zc_approve/zcApproveNotPass',
        method: 'post',
        data,
    })
}
