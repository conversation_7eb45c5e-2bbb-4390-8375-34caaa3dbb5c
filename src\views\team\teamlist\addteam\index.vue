<template>
  <div class="app-container">
    <div class="wcc-el-steps">
      <el-steps :active="stepActive" align-center>
        <el-step title="填写资料"></el-step>
        <el-step title="安全与设置"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="step-item">
      <team-info ref="teaminfoRef" v-show="stepActive === 0"></team-info>
      <!-- 机构资料 End -->

      <safe-set v-show="stepActive === 1"></safe-set>
      <!-- 安全设置 End -->
    </div>
    <div class="step-item step3" v-show="stepActive === 2">
      <div class="step-item-last">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>设置成功</h2>
        <!-- <p>系统账号和密码发送通知短信到手机</p> -->
        <p>系统账号：{{ submitForm.createUtils.account }}</p>
        <p>密码：zws123456</p>
        <!-- <p>发放手机：{{ submitForm.createUtils.contact }}</p> -->
      </div>
    </div>
    <div class="text-center">
      <el-button v-if="stepActive == 0" @click="toBack">取消</el-button>
      <el-button v-if="stepActive > 0 && stepActive < 2" @click="prevStep"
        >上一步</el-button
      >
      <el-button
        v-if="stepActive != 2"
        :loading="subloading"
        type="primary"
        @click="nextStep"
        >保存，下一步</el-button
      >
      <el-button v-else type="primary" @click="toBack"
        >委托机构设置完成，返回列表</el-button
      >
    </div>
  </div>
</template>

<script setup name="AddTeam">
import teamInfo from "./teaminfo.vue";
import safeSet from "./safeset.vue";

import { createTeam } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const route = useRoute()
const teaminfoRef = ref();

const router = useRouter();
const stepActive = ref(0);
const subloading = ref(false);
const submitForm = ref({
  createUtils: {},
  labels: [
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
    { labelContent: "未标签", stateLabel: 0 },
  ],
  white: {
    whitelistName: undefined,
    addressIp: undefined,
    note: undefined,
  },
  state: {
    whitelistStatus: 0,
    informationStatus: 0,
    restrictedState: 0,
    settingStatus: 0,
    authorizationStatus: 0,
    authenticationStatus: 0,
    exportSettingStatus:1
  },
  files: [],
  desensitization: [
    {
      filed: "dname",
      fieldname: "姓名1",
      status: 0,
      rules: "只展示姓名第一个字，例：李**",
    },
    {
      filed: "numbers",
      fieldname: "手机号码",
      status: 0,
      rules: "脱敏中间四位数字，例:134****4532",
    },
    {
      filed: "cardId",
      fieldname: "证件号码",
      status: 0,
      rules: "前6位和后4位不脱敏，中间脱敏，例:333344********7232",
    },
    {
      filed: "bankCard",
      fieldname: "银行卡号",
      status: 0,
      rules: "除前6位和后4位不脱敏，中间脱敏，例：632243******6543",
    },
    {
      filed: "qq",
      fieldname: "QQ",
      status: 0,
      rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
    },
    {
      filed: "weChat",
      fieldname: "微信",
      status: 0,
      rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
    },
    {
      filed: "households",
      fieldname: "户籍地址",
      status: 0,
      rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
    },
    {
      filed: "unitAddress",
      fieldname: "单位详细地址",
      status: 0,
      rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
    },
    {
      filed: "residentialAddress",
      fieldname: "居住地址",
      status: 0,
      rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
    },
    {
      filed: "homeAddress",
      fieldname: "家庭地址",
      status: 0,
      rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
    },
    {
      filed: "entityName",
      fieldname: "单位名称",
      status: 0,
      rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
    },
  ],
});
provide("labels", submitForm.value.labels);
provide("white", submitForm.value.white);
provide("state", submitForm.value.state);
provide("files", submitForm.value.files);
provide("desensitization", submitForm.value.desensitization);

//返回列表
function toBack() {
  const obj = { path: route.query.path};
  proxy.$tab.closeOpenPage(obj);
}

//上一步
function prevStep() {
  stepActive.value--;
}

//下一步
function nextStep() {
  if (stepActive.value === 0) {
    //第一步
    subloading.value = true;
    teaminfoRef.value
      .saveTeamInfo()
      .then((data) => {
        subloading.value = false;
        if (data) {
          submitForm.value.createUtils = data;
          stepActive.value++;
        }
      })
      .catch(() => {
        subloading.value = false;
      });
  }
  if (stepActive.value === 1) {
    //第二步
    let submitValue = JSON.parse(JSON.stringify(submitForm.value));
    //总开关未开不传数据
    if (!submitValue.state.whitelistStatus) {
      delete submitValue.white;
    }
    let arr = JSON.parse(JSON.stringify(submitValue.desensitization));
    let desensitization = {};
    if (!submitValue.state.informationStatus) {
      delete submitValue.desensitization;
    } else {
      // 信息脱敏数据处理
      for (let i = 0; i < arr.length; i++) {
        desensitization[arr[i].filed] = arr[i].status;
      }
      submitValue.desensitization = desensitization;
    }

    if (submitValue.files.length === 0) {
      delete submitValue.files;
    }
    subloading.value = true;
    createTeam(submitValue)
      .then((res) => {
        stepActive.value++;
        subloading.value = false;
      })
      .catch((err) => {
        subloading.value = false;
      });
  }
}
</script>

<style lang="scss" scoped>
.step-item {
  width: 70%;
  margin: 0 auto;
}
.step-item-last {
  text-align: center;
  margin: 32px auto 25px;
  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;
    .check-icon {
      font-size: 34px;
    }
  }
  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }
  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    color: #888888;
  }
}
</style>
