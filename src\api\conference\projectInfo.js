import request from "@/utils/request";
import dayjs from "dayjs";
import { getDict, getDictLabel } from "@/api/conference/biddings";

let startMethodOptions = [];
let sealTypeOption = []

getDict({ ruleName: 'start_method' }).then(res => {
  startMethodOptions = res.data;
});
getDict({ ruleName: 'seal_type' }).then(res => {
  sealTypeOption = res.data;
});

const isSealOption = [
  { code: 0, dictValue: false, dictLabel: "否" },
  { code: 1, dictValue: true, dictLabel: "是" },
];

export function dictData(data) {
  if (!Array.isArray(data)) return
  data.forEach((item) => {
    if (item.hasOwnProperty("isSeal")) {
      item.isSeal = getDictLabel(isSealOption, item.isSeal)
    }
    if (item.hasOwnProperty("initiatorType")) {
      item.initiatorType = getDictLabel(startMethodOptions, item.initiatorType)
    }
    if (item.hasOwnProperty("sealType")) {
      item.sealType = getDictLabel(sealTypeOption, item.sealType)
    }
  })
}

//所有time 的字段都会被格式化为 'YYYY-MM-DD HH:mm:ss'
export function formatAllDates(data) {
  if (Array.isArray(data)) {
    return data.map(formatAllDates);
  } else if (data && typeof data === "object") {
    const newObj = {};
    for (const key in data) {
      if (!data.hasOwnProperty(key)) continue;
      const value = data[key];
      // 判断字段名是否包含 date 或 time（不区分大小写），且值为字符串或 Date
      if (
        value &&
        (typeof value === "string" || value instanceof Date) &&
        /(date|time)$/i.test(key)
      ) {
        // 只格式化非空字符串
        newObj[key] = value
          ? dayjs(value).format("YYYY-MM-DD HH:mm:ss")
          : value;
      } else if (Array.isArray(value) || (value && typeof value === "object")) {
        newObj[key] = formatAllDates(value);
      } else {
        newObj[key] = value;
      }
    }
    return newObj;
  }

  return data;
}


//合同列表
export function getContractList(query) {
  return request({
    url: "/caseManage/acquisition/selectListContract",
    method: "get",
    params: query,
  });
}

//竞价列表
export function getBiddingList(query) {
  return request({
    url: "/caseManage/acquisition/selectListBiddingAcquisition",
    method: "get",
    params: query,
  });
}

//添加会议决策
export function addMeetingDecision(data) {
  return request({
    url: "/caseManage/acquisition/addMeetingDecision2",
    method: "post",
    data: data,
  });
}

//添加竞价收购、合同
export function addBiddingAcquisition(data) {
  return request({
    url: "/zws_zc_approve/zcAddApprove",
    method: "post",
    data: data,
  });
}

//决策文号下拉框
export function getDecisionNumberList() {
  return request({
    url: "/caseManage/acquisition/selectDecisionNumber",
    method: "get",
  });
}

//将多数组按照对应的字段映射到查询参数中
//   target,          // 响应式对象，如 queryParams
//   ranges,          // 日期数组对象，如 { createTime: ref([a, b]), reviewTime: ref([c, d]) }
//   fieldMap         // 映射表，如 { createTime: ['createStart', 'createEnd'], reviewTime: ['reviewStart', 'reviewEnd'] }
export function bindDateRangeToQueryParams({ targetRef, ranges, fieldMap }) {
  if (!targetRef?.value) return;

  Object.entries(fieldMap).forEach(([rangeKey, [startField, endField]]) => {
    const range = ranges[rangeKey]?.value;

    if (Array.isArray(range) && range.length === 2) {
      targetRef.value[startField] = range[0];
      targetRef.value[endField] = range[1];
    }
  });
}

//详情请求
export function getDetailList(url, id) {
  return request({
    url: `/caseManage/acquisition/${url}/${id}`,
    method: "get",
  });
}

//查询审批进度
export function approvalProcess(data,url) {
  return request({
        url: `/caseManage/acquisition/${url}`,
        method: 'post',
        data: data
    })
}

//项目流程状态查询
export function selectProjectStatus(projectId) {
  return request({
    url: `/caseManage/acquisition/selectProjectStatus/${projectId}`,
    method: "get",
  });
}

//详情页面tab切换的其他详情数据
export function selectProjectId(projectId) {
  return request({
    url: `/caseManage/acquisition/selectId/${projectId}`,
    method: "get",
  });
}