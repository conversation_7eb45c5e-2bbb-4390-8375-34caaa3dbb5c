<template>
  <el-table v-loading="loading" :data="entrustList">
    <el-table-column
      label="序号"
      type="index"
      align="center"
      width="50px"
      :index="tableIndex"
    ></el-table-column>
    <el-table-column
      label="策略名称"
      prop="strategyName"
      align="center"
    ></el-table-column>
    <el-table-column label="更新时间" prop="updateTime" align="center"></el-table-column>
    <el-table-column label="状态" prop="state" align="center">
       <template #default="{row}">
        <el-switch
          class="myswitch"
          v-model="row.state"
          active-color="#2ECC71"
          active-value="0"
          inactive-value="1"
          active-text="开"
          inactive-text="关"
          @change="handleState(row)"
          v-if="checkPermi(['tactics:entrust:status'])"
        ></el-switch>
        <span v-else>{{row.state == 0?'开启':'关闭'}}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作">
       <template #default="{row}">
        <el-button type="primary" v-hasPermi="['tactics:entrust:edit']" link @click="editList(row)">修改</el-button>
        <el-button type="primary" v-hasPermi="['tactics:entrust:del']" link @click="removeList(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    :title="title"
    v-model="open"
    width="995px"
    append-to-body
    :before-close="cancel"
  >
    <el-form :model="form" :rules="rules" :inline="true" ref="entrustRef">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略名称" prop="strategyName">
            <el-input v-model="form.strategyName" placeholder="请输入策略名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state">
            <el-switch
              class="myswitch"
              v-model="form.state"
              active-color="#2ECC71"
              active-value="0"
              inactive-value="1"
              active-text="开"
              inactive-text="关"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="合规利率公式："></el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label=" ">
            债权本金 + 债权本金 * ( 当天日期 - 逾期日期（末次） ）/&nbsp;
            <el-form-item prop="formulaPercent1">
              <el-input
                v-model="form.formulaPercent1"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input
              >&nbsp;
            </el-form-item>
            * &nbsp;
            <el-form-item prop="formulaPercent2">
              <el-input
                v-model="form.formulaPercent2"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input
              >&nbsp;%
            </el-form-item>
            + 剩余债权费用 &nbsp;
            <el-form-item prop="formulaPercent3">
              <el-input
                v-model="form.formulaPercent3"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input
              >&nbsp;%
            </el-form-item>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getentrustList,
  addEntrust,
  editEntrust,
  deleteEntrust,
} from "@/api/system/tactic";
import { checkPermi } from "@/utils/permission";
const props = defineProps({
  queryParams: {
    type: Object,
  },
});
const { proxy } = getCurrentInstance();

/** 添加编辑策略表单验证 */
//数字（小数和整数）
const validateNumber = (rule, value, callback) => {
  let reg = /^(([^0][0-9]+|0)\.([0-9]{1,2})$)|^(([^0][0-9]+|0)$)|^(([1-9]+)\.([0-9]{1,2})$)|^(([1-9]+)$)/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入"));
  } else if (!reg.test(value)) {
    callback(new Error("只能输入正数，保留两位小数"));
  } else {
    callback();
  }
};

const total = inject("total");
const $getList = inject("getList");

const loading = ref(false);
const subloading = ref(false);
const entrustList = ref([]);

const title = ref("");
const open = ref(false);
const data = reactive({
  form: {
    strategyName: undefined,
    state: "0",
    formulaPercent1: undefined,
    formulaPercent2: undefined,
    formulaPercent3: undefined,
  },
  rules: {
    strategyName: [{ required: true, message: "策略名称不能为空", trigger: "blur" }],
    formulaPercent1: [{ validator: validateNumber, trigger: "blur" }],
    formulaPercent2: [{ validator: validateNumber, trigger: "blur" }],
    formulaPercent3: [{ validator: validateNumber, trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

function getList(query) {
  loading.value = true;
  getentrustList(query)
    .then((res) => {
      total.value = res.total;
      entrustList.value = res.rows;
      loading.value = false;
    })
    .then(() => {
      loading.value = false;
    });
}

//打开编辑策略弹框
function editList(row) {
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
  title.value = "编辑策略";
}

//删除
function removeList(row) {
  proxy.$modal
    .confirm('是否确认删除策略名称为"' + row.strategyName + '"的数据项？')
    .then(function () {
      loading.value = true;
      return deleteEntrust(row.id);
    })
    .then(() => {
      $getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {loading.value = false;})
}

//状态
function handleState(row) {
  editEntrust(row)
    .then((res) => {
    })
    .catch(() => {
      $getList();
    });
}

//列表序号递增
function tableIndex(index) {
  return (props.queryParams.pageNum - 1) * props.queryParams.pageSize + index + 1;
}

//打开新建/编辑策略弹框
function opendialog(tit) {
  title.value = tit;
  open.value = true;
}

//重置表单
function reset() {
  proxy.resetForm("entrustRef");
  form.value = {
    strategyName: undefined,
    state: "0",
    formulaPercent1: undefined,
    formulaPercent2: undefined,
    formulaPercent3: undefined,
  };
}

//取消添加/编辑
function cancel() {
  open.value = false;
  reset();
}

//提交表单
function submitForm() {
  proxy.$refs["entrustRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      if (form.value.hasOwnProperty("id")) {
        editEntrust(form.value).then((res) => {
          subloading.value = false;
          proxy.$modal.msgSuccess("修改成功！");
          cancel();
          $getList();
        });
      } else {
        addEntrust(form.value)
          .then((res) => {
            subloading.value = false;
            proxy.$modal.msgSuccess("添加成功！");
            cancel();
            $getList();
          })
          .catch(() => {
            subloading.value = false;
          });
      }
    }
  });
}

defineExpose({
  getList,
  opendialog,
});
</script>

<style scoped></style>
