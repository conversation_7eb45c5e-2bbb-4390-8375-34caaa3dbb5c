<template>
  <div style="height: calc(100vh - 203px); overflow: auto">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
      <el-form-item label="机构名称" prop="outsourcingTeamId">
        <el-select
          v-model="queryParams.outsourcingTeamId"
          placeholder="请选择机构名称"
          @visible-change="getTeamOptions"
          :loading="selectloading"
          filterable
          :reserve-keyword="false"
          clearable
          style="width: 240px"
        >
          <el-option-group
            v-for="group in teamOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.children"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.time"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="催收状态" prop="urgeState">
        <el-select
          v-model="queryParams.urgeState"
          placeholder="请输入或选择催收状态"
          clearable
          filterable
          :reserve-keyword="false"
          :loading="selectloading"
          @visible-change="UrgeState"
          style="width: 240px"
        >
          <el-option
            v-for="item in backs"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <!-- 每日联系情况 -->
    <dailyInfoVue class="mb20" :queryParams="query" />

    <!-- 催收状态分布--委外机构 -->
    <urgeStateTeam class="mb20" :queryParams="query" />

    <!-- 催收状态分布-联络结果 -->
    <urgeStateResult :queryParams="query" />
  </div>
</template>

<script setup>
import dailyInfoVue from "./collectionForm/dailyInfo.vue";
import urgeStateTeam from "./collectionForm/urgeStateTeam.vue";
import urgeStateResult from "./collectionForm/urgeStateResult.vue";
import { getUrgeState } from "@/api/assets/casemanage.js";
import { getTeamTree } from "@/api/team/team";

const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    outsourcingTeamId: undefined,
    time: [],
    urgeState: undefined,
  },
});
const { queryParams } = toRefs(data);
const rangFileds = ["time"];

const selectloading = ref(false);
const teamOptions = ref([]);
const backs = ref([]);

const query = ref({});

//获取机构名称
function getTeamOptions(val) {
  if (!val) return;
  selectloading.value = true;
  getTeamTree()
    .then((res) => {
      teamOptions.value = res.data;
    })
    .finally(() => {
      selectloading.value = false;
    });
}

//催收状态
function UrgeState(val) {
  if (!val) return;
  selectloading.value = true;
  getUrgeState()
    .then((res) => {
      backs.value = res.data;
    })
    .finally(() => {
      selectloading.value = false;
    });
}

//搜索
function handleQuery() {
  query.value = proxy.addFieldsRange(queryParams.value, rangFileds);
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    outsourcingTeamId: undefined,
    time: [],
    urgeState: undefined,
  };
  query.value = proxy.addFieldsRange(queryParams.value, rangFileds);
}
</script>

<style lang="scss" scoped>
:deep(.warp .title) {
  margin: 20px 0 10px;
  color: #888888;
  background-color: #f9f9f9;
  line-height: 38px;
  padding-left: 20px;
}
</style>
