<template>
  <div class="app-container">
    <el-form
      inline
      label-width="100px"
      ref="queryRef"
      :model="queryParams"
    >
      <el-form-item prop="projectId" label="项目ID">
        <el-select
          style="width: 320px"
          v-model="queryParams.projectId"
          placeholder="请输入或选择项目ID"
          multiple
          filterable
          :reserve-keyword="false"
        >
          <el-option
            v-for="item in projectIdOptions"
            :key="Math.random()"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <el-select
          style="width: 320px"
          v-model="queryParams.projectName"
          placeholder="请输入或选择项目名称"
          multiple
          filterable
          :reserve-keyword="false"
        >
          <el-option
            v-for="item in projectNameOptions"
            :key="Math.random()"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select
          v-model="queryParams.productType"
          filterable
          multiple
          :reserve-keyword="false"
          placeholder="请输入或选择产品方名称"
          style="width: 320px"
        >
          <el-option
            v-for="item in options"
            :key="Math.random()"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="transferorId" label="资产转让方">
        <el-select
          v-model="queryParams.transferorId"
          collapse-tags
          collapse-tags-tooltip
          multiple
          placeholder="请输入或选择资产转让方"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 320px"
        >
          <el-option
            v-for="item in transferorOptions"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <template v-if="showSearch">
        <el-form-item prop="meetingDate" label="决策时间">
          <el-date-picker
            v-model="meetingDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            clearable
            unlink-panels
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 320px"
          />
        </el-form-item>
        <el-form-item prop="decisionNumber" label="决策文号">
          <el-select
            v-model="queryParams.decisionNumber"
            collapse-tags
            collapse-tags-tooltip
            multiple
            placeholder="请输入或选择决策文号"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in decisionNumberOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="createById" label="创建人">
          <el-select
            v-model="queryParams.createById"
            collapse-tags
            collapse-tags-tooltip
            multiple
            placeholder="请输入或选择创建人"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in createByOptions"
              :key="item.code"
              :label="item.info"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="createTime" label="创建时间">
          <el-date-picker
            v-model="createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            clearable
            unlink-panels
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 320px"
          />
        </el-form-item>
      </template>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(tabChangeList)">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :label="item.info"
        :name="index"
      >
        <div class="table-box">
          <el-table :data="dataList" :loading="loading">
            <el-table-column
              label="项目ID"
              v-if="columns[0].visible"
              align="center"
              prop="projectId"
            />
            <el-table-column
              label="项目名称"
              v-if="columns[1].visible"
              align="center"
              prop="projectName"
            >
            <template #default="{ row }">
              <el-tooltip
                effect="dark"
                :content="row.projectName"
                placement="top"
                :disabled="!(row.projectName && row.projectName.length > 20)"
              >
                <el-link
                  type="primary"
                  :underline="false"
                  @click="handleDetails(row)"
                >
                  {{
                    row.projectName
                      ? row.projectName.length > 20
                        ? row.projectName.slice(0, 20) + '...'
                        : row.projectName
                      : ''
                  }}
                </el-link>
              </el-tooltip>
            </template>
            </el-table-column>
            <el-table-column
              label="会议决策"
              v-if="columns[2].visible"
              align="center"
              prop="decisionStatus"
            />
            <el-table-column
              label="资产转让方"
              v-if="columns[3].visible"
              align="center"
              prop="transferor"
            />
            <el-table-column
              label="产品类型"
              v-if="columns[4].visible"
              align="center"
              prop="productType"
            />
            <el-table-column
              label="会议标题"
              v-if="columns[5].visible"
              align="center"
              prop="meetingTitle"
            >
            <template #default="{ row }">
              <span v-if="row.meetingTitle && row.meetingTitle.length > 20">
                <el-tooltip effect="dark" :content="row.meetingTitle" placement="top">
                  <span>
                    {{ row.meetingTitle.slice(0, 20) + '...' }}
                  </span>
                </el-tooltip>
              </span>
              <span v-else>
                {{ row.meetingTitle}}
              </span>
            </template>
            </el-table-column>

            <el-table-column
              label="决策时间"
              v-if="columns[6].visible"
              align="center"
            >
              <template #default="{ row }">
                {{
                  row.meetingDate
                    ? dayjs(row.meetingDate).format("YYYY-MM-DD HH:mm:ss")
                    : "--"
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="决策文号"
              v-if="columns[7].visible"
              align="center"
              prop="decisionNumber"
            >
              <template #default="{ row }">
                <span v-if="row.decisionNumber && row.decisionNumber.length > 20">
                <el-tooltip effect="dark" :content="row.decisionNumber" placement="top">
                  <span>
                    {{ row.decisionNumber.slice(0, 20) + '...' }}
                  </span>
                </el-tooltip>
              </span>
              <span v-else>
                {{ row.decisionNumber}}
              </span>
              </template>
            </el-table-column>
            <el-table-column
              label="附件"
              v-if="columns[8].visible"
              align="center"
            >
              <template #default="{ row }">
                <div>
                  <el-button  v-if="row.isFile" type="text" @click="handleCheck(row)"
                    >查看</el-button
                  >
                  <span v-else>--</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="决策结果"
              v-if="columns[9].visible"
              align="center"
              prop="decisionResult"
            />
            <el-table-column
              label="创建人"
              v-if="columns[10].visible"
              align="center"
              prop="createBy"
            />
            <el-table-column
              label="创建时间"
              v-if="columns[11].visible"
              align="center"
            >
              <template #default="{ row }">
                {{
                  row.createTime
                    ? dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")
                    : "--"
                }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" width="180" label="操作">
              <template #default="{ row }">
                <div>
                  <el-button
                    type="text"
                    v-if="row.decisionStatus == '待添加'"
                    @click="check(row)"
                  >添加会议决策</el-button>
                  <el-button type="text" @click="handleDetails(row)" v-else>详情</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <check-file ref="checkFileRef" @cancel="handleDialogCancel" />
  </div>
</template>

<script setup name="StartNapeList">
import { getOwners } from "@/api/assets/casemanage";
import {
  listAndQuery,
  getProjectIdList,
  getCheckFile,
  getProjectNameList,
  getProductTypeList,
  getTransferorList,
  getDecisionNumberList,
  getCreateByList,
  getCountStatus,
} from "@/api/conference/conference";
import { status } from "nprogress";
import {fillEmptyToDash} from "@/api/conference/utils"
import dayjs from "dayjs";
import { watch } from "vue";
import checkFile from "../../page/checkFile.vue";
const checkFileRef = ref(null);
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const options = ref([]); 
const projectIdOptions = ref([]);
const projectNameOptions = ref([]);
const transferorOptions = ref([]);
const decisionNumberOptions = ref([]);
const createByOptions = ref([]);
const ownerOption = ref([]);

const tabList = ref([
  { code: "", info: "待添加" },
  { code: "", info: "决策未通过" },
  { code: "", info: "决策通过" },
  { code: "", info: "全部" },
]);

const createTime = ref([]);
const meetingDate = ref([]);
const activeName = ref(3);
const total = ref(1);
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const countStatus = ref([]);
const columns = ref([
  { key: 0, label: "项目ID", visible: true },
  { key: 1, label: "项目名称", visible: true },
  { key: 2, label: "会议决策", visible: true },
  { key: 3, label: "资产转让方", visible: true },
  { key: 4, label: "产品类型", visible: true },
  { key: 5, label: "会议标题", visible: true },
  { key: 6, label: "决策时间", visible: true },
  { key: 7, label: "决策文号", visible: true },
  { key: 8, label: "附件", visible: true },
  { key: 9, label: "决策结果", visible: true },
  { key: 10, label: "创建人", visible: true },
  { key: 11, label: "创建时间", visible: true },
]);

const tableProps = [
  'projectId',
  'projectName',
  'decisionStatus',
  'transferor',
  'productType',
  'meetingTitle',
  'decisionNumber',
  'isFile',
  'decisionResult',
  'createBy',
];

// 获取转让方
getOwners().then((res) => {
  ownerOption.value = res.data;
});


//获取表格数据
listAndQuery({ pageNum: 1, pageSize: 10 }).then((res) => {
  dataList.value = fillEmptyToDash(res.rows,tableProps);
  total.value = res.total;
});

//获取项目id下拉框数据
getProjectIdList().then((res) => {
  projectIdOptions.value = res.data;
});
//获取项目名称下拉框数据
getProjectNameList().then((res) => {
  projectNameOptions.value = res.data;
});

//获取产品类型下拉框数据
getProductTypeList().then((res) => {
  options.value = res.data;
});

//获取资产转让方下拉框数据
getTransferorList().then((res) => {
  transferorOptions.value = res.data;
});

//获取决策文号下拉框数据
getDecisionNumberList().then((res) => {
  decisionNumberOptions.value = res.data;
});

//获取创建人下拉框数据
getCreateByList().then((res) => {
  createByOptions.value = res.data;
});

//获取分组统计状态数量
getCountStatus().then((res) => {
  countStatus.value = res.data;
});

//监听日期选择
watch(meetingDate, (val) => {
  if (val && val.length > 0) {
    queryParams.value.createTimeStart = val[0];
    queryParams.value.createTimeEnd = val[1];
  } else {
    delete queryParams.value.createTimeStart;
    delete queryParams.value.createTimeEnd;
  }
});

watch(createTime, (val) => {
  if (val && val.length > 0) {
    queryParams.value.decisionTimeStart = val[0];
    queryParams.value.decisionTimeEnd = val[1];
  } else {
    delete queryParams.value.decisionTimeStart;
    delete queryParams.value.decisionTimeEnd;
  }
});

//设置tab label数量显示
watch(
  countStatus,
  (newVal) => {
    tabList.value.forEach((tab) => {
      const baseName = tab.info.split("(")[0].trim();
      if (baseName === "全部") {
        const total = newVal.reduce((sum, item) => sum + (item.count || 0), 0);
        tab.info = `全部 (${total})`;
      } else {
        const found = newVal.find((item) => item.decisionStatus === baseName);
        tab.info = found ? `${baseName} (${found.count})` : `${baseName} (0)`;
      }
    });
  },
  { immediate: true }
);

// 显示查询附件弹框
function handleCheck(row) {
  getCheckFile(row.id).then((res) => {
    checkFileRef.value.openDialog(res.data);
  });
}
//关闭弹窗事件
function handleDialogCancel() {}

//传参获取表格数据
function getList() {
  listAndQuery(queryParams.value)
    .then((res) => {
      dataList.value = fillEmptyToDash(res.rows,tableProps);
      total.value = res.total;
    })
    .catch((err) => {
      console.error("获取表格数据失败:", err);
    });
}
//tab切换数据变换
function tabChangeList() {
  queryParams.value.status = activeName.value !== 3 ? activeName.value : "";
  getList();
}
//重置表单
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  };
  meetingDate.value = [];
  createTime.value = [];
  getList();
}
//搜索查询
function handleQuery() {
  queryParams.value.status = activeName.value !== 3 ? activeName.value : "";
  getList();
}

//点击添加会议
function check(row){
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}
  
const query = {
    path: route.path,
    pageType: "conference",
    progressStatus: 2,
    isDetails: 0,
    activeTab: "0",
    rowData: JSON.stringify(rowData),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

function handleOpenDailog(refName) {
  proxy.$refs[refName].openDialog();
}
async function handleDetails(row) {
  const data = await getCheckFile(row.id)
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}

  const query = {
    path: route.path,
    pageType: "conference",
    progressStatus: 2,
    rowData: JSON.stringify(rowData),
    isDetails: 1,
    activeTab: "0",
    
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}
</script>

<style lang="scss" scoped>

</style>
