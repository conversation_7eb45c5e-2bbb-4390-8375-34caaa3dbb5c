<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="550px"
    :before-close="cancel"
    append-to-body
    :key="title"
  >
    <div class="boxTootip">
      <div>
        <el-icon :size="20" color="#409EFF"><Warning /></el-icon>
      </div>
      <div style="display: flex; flex-direction: column; gap: 5px">
        <span>{{ title }}</span>
        <span>该操作将{{ title }}当前所选申请，是否继续。</span>
      </div>
    </div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
      <el-form-item label="备注" prop="reason">
        <el-input
          type="textarea"
          v-model="form.reason"
          placeholder="请输入"
          maxlength="50"
          rows="4"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="Pass">
//全局配置
const { proxy } = getCurrentInstance();
const loading = ref(false);
const open = ref(false);
const emit = defineEmits(["getList"]);
const title = ref("通过");
//提交数据
const data = reactive({
  form: {
    ids: [],
    reason: "同意",
    allQuery: undefined,
    queryParams: {},
  },
  rules: {
    reason: [{ required: true, message: "请输入备注", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

//打开窗口
function opendialog(row, type) {
  title.value = type == 0 ? "通过" : "不通过";
  open.value = true;
  form.value = row;
  form.value.type = type;
  form.value.reason = "";

  // 动态设置 reason 校验规则
  if (type == 0) {
    rules.value.reason = [];
  } else {
    rules.value.reason = [{ required: true, message: "请输入备注", trigger: "blur" }];
  }

}
function close() {
  open.value = false;
  loading.value = false;
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = JSON.parse(JSON.stringify(form.value));
      emit("submit", req);
    }
  });
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    ids: [],
    reason: undefined,
    allQuery: undefined,
    queryParams: {},
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
  close,
});
</script>
<style lang="scss" scoped>
.boxTootip {
  width: 100%;
  display: flex;
  padding: 10px;
  width: 350px;
  gap: 30px;
  margin: 20px auto;
  background-color: rgba(230, 247, 255, 1);
  border: 1px solid rgba(145, 213, 255, 1);
  border-radius: 5px;
}
</style>
