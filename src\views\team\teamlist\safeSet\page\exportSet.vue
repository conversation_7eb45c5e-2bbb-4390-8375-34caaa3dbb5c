<template>
  <div class="title mb20">
    <span>导出相关列表数据</span>
    <el-switch class="myswitch ml20" v-model="state.exportSettingStatus" active-color="#2ECC71" :active-value="1"
      :inactive-value="0" active-text="开" inactive-text="关" @change="change"></el-switch>
  </div>
  <el-row class="mt10" v-if="state.exportSettingStatus == 1">
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList" class="mt20">
      <el-table-column label="菜单" align="center" key="menuName" prop="menuName" />
      <el-table-column label="功能按钮" align="center" key="buttonName" prop="buttonName" />
      <el-table-column label="状态" align="center">
        <template #default="{ row }">
          <el-switch class="myswitch" v-model="row.exportStatus" :active-value="1" :inactive-value="0"
            active-color="#2ECC71" active-text="开" inactive-text="关" @change="stateChange(row)" />
        </template>
      </el-table-column>
      <el-table-column label="说明" align="center" key="remark" prop="remark" />
    </el-table>
  </el-row>
</template>

<script setup>
import { changeSafeStatus, editMenuStatus, getTeamExportList } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);
const route = useRoute();
const dataList = ref([]);
const loading = ref(false);
//获取导出信息
function getList() {
  loading.value = true
  const reqForm = { teamId: route.params.teamId }
  getTeamExportList(reqForm).then(res => {
    dataList.value = res.rows
  }).finally(() => loading.value = false)
}

function change() {
  changeSafeStatus(state.value)
    .then(() => {
      if (state.value.exportSettingStatus == 1) {
        getList()
      }
    })
    .catch(() => {
      getTeamSafe();
    });
}


//状态修改
function stateChange(row) {
  loading.value = true;
  let req = { id: row.id, exportStatus: row.exportStatus }
  editMenuStatus(req).then((res) => {
    loading.value = false;
    proxy.$modal.msgSuccess("修改成功");
  }).catch(() => {
    loading.value = false;
    row.exportStatus = row.exportStatus == 1 ? 0 : 1
  });
}

onMounted(() => {
  if (state.value.exportSettingStatus == 1) {
    getList()
  }
})
</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
