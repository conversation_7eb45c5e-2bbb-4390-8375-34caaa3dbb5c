<template>
    <el-dialog v-model="open" title="管理材料" append-to-body width="50vw" :before-close="cancel">
        <div class="df-jc-sb mb10">
            <span>案件ID：ANID12321321455</span>
            <el-button type="primary" @click="handleDownload">下载全部文件</el-button>
        </div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="文件名称" align="center" prop="fileName" min-width="120" />
            <el-table-column label="文件类型" align="center" prop="fileType" min-width="120" />
            <el-table-column label="文件地址" align="center" prop="fileAddress" min-width="160">
                <template #default="{ row }">
                    <el-button type="text" @click="handleDownload(row)">{{ row.fileAddress }}</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" width="160" label="操作">
                <template #default="{ row }">
                    <el-button type="text" @click="handle(row)">上传</el-button>
                    <el-button type="text" @click="handle(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <FileUpload drag autoUpload class="mt20" v-model:fileList="fileList" uploadFileUrl="/file/upload">
            <template #drag-tip-msg>
                <span>支持扩展名：.rar .zip .doc .docx .pdf .jpg...</span>
            </template>
        </FileUpload>
    </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance()

const loading = ref(false)
const open = ref(false)
const fileList = ref([])
const dataList = ref([
    { fileName: '借款合同0920.pdf', fileType: '借款合同', fileAddress: 'https://zhaiweishi.com/doc/309c9afj' },
    { fileName: '代付凭证0912.pdf', fileType: '代付凭证', fileAddress: 'https://zhaiweishi.com/doc/309cgfs' },
    { fileName: '身份证正面.png', fileType: '身份证', fileAddress: 'https://zhaiweishi.com/doc/309c923' },
])

function handle(row) {

}

function handleDownload() {
    const fileName = `${+new Date()}.xlsx`
    exportFile('https://assest.amcmj.com/resource/preview/2025/06/18/d07d05ec0b6f40b99cff7fb833c46a0c_错误的案件.xlsx', fileName)
}

function openDialog(data) {
    open.value = true
}
function cancel() {
    open.value = false
}
function exportFile(data, fileName) {
    // 地址不存在时，禁止操作
    if (!data) return;
    proxy.$modal.notify('正在下载中...')
    // 下载文件并保存到本地
    const callback = (data) => {
        // 创建a标签，使用 html5 download 属性下载，
        const link = document.createElement('a');
        // 创建url对象
        const objectUrl = window.URL.createObjectURL(new Blob([data]));
        link.style.display = 'none';
        link.href = objectUrl;
        // 自定义文件名称， fileName
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        // 适当释放url
        window.URL.revokeObjectURL(objectUrl);
    };
    // 把接口返回的url地址转换为 blob
    const xhr = new XMLHttpRequest();
    xhr.open('get', data, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
        // 返回文件流，进行下载处理
        callback(xhr.response);
        proxy.$modal.msgSuccess('操作成功！')
    };
    xhr.send(); // 不要忘记发送
};
defineExpose({ openDialog })
</script>

<style lang="scss" scoped></style>