import request from "@/utils/request";

//列表查询
export function listAndQuery(query) {
  console.trace()
  return request({
    url: "/caseManage/acquisition/selectListBiddingAcquisition",
    method: "get",
    params: query,
  });
}

//项目id下拉框
export function getProjectIdList() {
  return request({
    url: "/caseManage/acquisition/selectProjectIdBidding",
    method: "get",
  });
}

//项目名称下拉框
export function getProjectNameList() {
  return request({
    url: "/caseManage/acquisition/selectProjectNameBidding",
    method: "get",
  });
}

//产品类型下拉框
export function getProductTypeList() {
  return request({
    url: "/caseManage/acquisition/selectProductTypeBidding",
    method: "get",
  });
}

//资产转让方下拉框
export function getTransferorList() {
  return request({
    url: "/caseManage/acquisition/selectTransferorBidding",
    method: "get",
  });
}

// //决策文号下拉框
// export function getDecisionNumberList() {
//   return request({
//     url: '/caseManage/acquisition/selectDecisionNumber',
//     method: 'get',
//   })
// }

//申请人下拉框
export function getCreateByList() {
  return request({
    url: "/caseManage/acquisition/selectCreateByBidding",
    method: "get",
  });
}

//获取审批人下拉框
export function getApproveList() {
  return request({
    url: "/caseManage/acquisition/selectApproveByBidding",
    method: "get",
  });
}

//获取数据字典
export function getDict(query) {
  return request({
    url: "/caseManage/acquisition/selectRule",
    method: "get",
    params: query,
  });
}

//转换字典值
export function getDictLabel(dictList, value) {
  const item = dictList.find((i) => i.dictValue === value);
  return item ? item.dictLabel : value;
}

//查看附件
export function getCheckFile(id) {
  return request({
    url: `/caseManage/acquisition/selectMeetingDecisionFile/${id}`,
    method: "get",
  });
}

//分组统计状态数量
export function getCountStatus() {
  return request({
    url: "/caseManage/acquisition/groupBiddingCount",
    method: "get",
  });
}

//统计全部数量
export function getAllCountStatus() {
  return request({
    url: "/caseManage/acquisition/selectProjectCountBidding",
    method: "get",
  });
}

//撤销项目审批
export function revokeProjectApproval(data) {
  return request({
    url: "/caseManage/zws_zc_approve/zcApproveRevoke",
    method: "post",
    data:data
  });
}

//查看附件
export function getCheckFileBidding(id) {
  return request({
    url: `/caseManage/acquisition/selectBiddingFile/${id}`,
    method: "get",
  });
}

//搜索结果全选撤销
export function revokeFormAll(data) {
  return request({
    url: "/caseManage/acquisition/revokeBidding",
    method: "post",
    data:data
  });
}