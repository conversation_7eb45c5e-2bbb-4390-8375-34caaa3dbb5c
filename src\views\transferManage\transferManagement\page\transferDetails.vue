<template>
    <div class="content-container " v-loading="loading">
        <ProgressInfo :progressList="progressList" :active="activeStep" title="资产包转让流程" />
        <TransferInfo :activeStep="activeStep" :state="progressList[activeStep - 1]?.value" :prevStep="prevStep"
            :nextStep="nextStep" />
        <div class="tab-area">
            <el-radio-group class="pl20 mb10" v-model="tabActive">
                <el-radio-button v-for="v in tabList" :key="v" :label="v.label" :name="v.label">
                    {{ v.label }}
                </el-radio-button>
            </el-radio-group>
            <ProcessDocument v-if="tabActive == '过程文档'" />
            <ProcessRecord v-if="tabActive == '过程记录'" />
            <RepaymentRecord v-if="tabActive == '还款记录'" />
            <reductionRecord v-if="tabActive == '减免记录'" />
        </div>
    </div>
</template>

<script setup>

import ProcessDocument from '../tabCom/processDocument.vue'
import ProcessRecord from '../tabCom/processRecord.vue'
import RepaymentRecord from '../tabCom/repaymentRecord.vue'
import reductionRecord from '../tabCom/reductionRecord.vue'
import TransferInfo from '../components/transferInfo.vue'
import ProgressInfo from '../components/progressInfo.vue'
const route = useRoute()
const activeStep = ref(1)
const loading = ref(false)
const tabActive = ref('过程文档')
const tabList = ref([
    { label: '过程文档' },
    { label: '过程记录' },
    { label: '还款记录' },
    { label: '减免记录' },
])
const progressList = ref([
    { label: '已预登记', value: '尽调评估' },
    { label: '正式登记', value: '正式登记' },
    { label: '竞拍中', value: '竞拍中' },
    { label: '待协议签署', value: '待协议签署' },
    { label: '待付款', value: '待付款' },
    { label: '待交割', value: '待交割' },
    { label: '交割中', value: '交割中' },
    { label: '交割完成', value: '已转让' },
])
function nextStep(step = 1) {
    activeStep.value = +activeStep.value + (+step)
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
function prevStep() {
    activeStep.value--
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
onMounted(() => {
    activeStep.value = route.query.activeStep
})
</script>

<style lang="scss" scoped>
.content-container {
    padding: 20px;
    min-height: calc(100vh - 88px);
    background-color: #F2F6FC;

    .tab-area {
        padding-top: 20px;
        margin-top: 20px;
        background-color: #fff;
    }
}
</style>