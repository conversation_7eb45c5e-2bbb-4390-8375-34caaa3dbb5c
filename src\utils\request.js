import axios from 'axios'
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import store from '@/store'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from '@/utils/ruoyi'
import { saveAs } from 'file-saver'

import mymessage from '../plugins/modal'

let downloadLoadingInstance;

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 300000
})

// request拦截器
service.interceptors.request.use(config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  if (getToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(res => {
  // 未设置状态码则默认成功状态
  const code = res.data?.code;
  // 获取错误信息
  const msg = errorCode[code] || res.data.msg || errorCode['default']
  // 二进制数据则直接返回
  if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
    return res.data
  }
  if (code === 401) {
    ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
    ).finally(() => {
      store.dispatch('LogOut').then(() => {
        location.href = '/index';
      })
    })
    return true;
    // return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
  } else if (code === 413) {
    mymessage.msgError({
      message: '查询内容太多，请减少查询内容或分多次查询！',
      //type: 'error'
    })
    return Promise.reject(new Error('查询内容太多，请减少查询内容或分多次查询！'))
  } else if (code === 414) {
    mymessage.msgError({
      message: '查询内容太多，请减少查询内容或分多次查询！',
      //type: 'error'
    })
    return Promise.reject(new Error('查询内容太多，请减少查询内容或分多次查询！'))
  } else if (code === 500) {
    mymessage.msgError({
      message: msg,
      //type: 'error'
    })
    return Promise.reject(new Error(msg))
  } else if (code === 9000) {
    mymessage.msgWarning({
      message: msg
    })
    return Promise.reject(new Error(msg))
  } else if (code !== 200) {
    mymessage.msgWarning({
      message: msg
    })
    return Promise.reject('error')
  } else {
    return Promise.resolve(res.data)
  }
},
  error => {
    console.log('err' + error)
    let { message } = error;
    if (message == "Network Error") {
      message = "网络异常，请检测网络设置！";
    }
    else if (message.includes('504')) {
      message = '查询内容太多，请减少查询内容或分多次查询！'
    }
    else if (message.includes("timeout")) {
      message = "您的网络太慢，建议切换网络后操作！";
      mymessage.msgWarning({
        message: message,
        //type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(error)
    }
    else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }
    mymessage.msgError({
      message: message,
      //type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename) {
  downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    timeout: 30 * 60 * 1000
  }).then(async (data) => {
    const isLogin = await blobValidate(data);
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      if (rspObj.code != 200) {
        mymessage.msgWarning(errMsg)
      }
    }
    downloadLoadingInstance.close();
    return data; // 显式返回结果
  }).catch((r) => {
    console.error(r)
    mymessage.msgWarning('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

//下载方法，json传参
export function downloadforjson(url, data, filename) {
  downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, data, {
    transformRequest: [(data) => { return JSON.stringify(data) }],
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
    responseType: 'blob',
    timeout: 30 * 60 * 1000
  }).then(async (res) => {
    const isLogin = await blobValidate(res);
    if (isLogin) {
      const blob = new Blob([res])
      saveAs(blob, filename)
    } else {
      const resText = await res.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      if (rspObj.code != 200) {
        mymessage.msgWarning(errMsg)
      }
    }
    downloadLoadingInstance.close();
    return res; // 显式返回结果
  }).catch((r) => {
    console.error(r)
    mymessage.msgWarning('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

//post传递form-data数据
export function myAxios(url, data) {
  var req = new FormData();
  Object.keys(data).forEach((item) => {
    if (data[item] === 0) {
      req.append(item, data[item]);
    } else if (data[item] && data[item] != '') {
      req.append(item, data[item]);
    }
  });
  return service.post(url, req, {
    data: req,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

export default service
