import request from '@/utils/request'

// 项目ID下拉
export function getProjectId() {
    return request({
        url: '/caseManage/projInitiation/getProjectIdDropdown',
        method: 'get'
    })
}

// 项目名称下拉
export function getProjectName() {
    return request({
        url: '/caseManage/projInitiation/getProjectNameDropdown',
        method: 'get'
    })
}
// 产品类型下拉
export function getProductType() {
    return request({
        url: '/caseManage/projInitiation/getProductDataDropdown',
        method: 'get'
    })
}
// 用户信息下拉
export function getUserInfo() {
    return request({
        url: '/caseManage/projInitiation/getUserDataDropdown',
        method: 'get'
    })
}
// 获取当前登录用户id
export function getCurrentUserId() {
    return request({
        url: '/caseManage/projInitiation/getUserId',
        method: 'get'
    })
}
//立项列表的新增立项
export function addNapeList(data) {
    return request({
        url: '/caseManage/projInitiation/insertInitiation',
        method: 'post',
        data: data
    })
}

// 投标方式下拉选项
export function getTenderType(query) {
    return request({
        url: '/caseManage/acquisition/selectRule',
        method: 'get',
        params: query
    })
}

// 立项列表查询
export function getNapeList(query) {
    return request({
        url: '/caseManage/projInitiation/list',
        method: 'get',
        params: query
    })
}

// 立项列表删除
export function deleteNapeList(data) {
    return request({
        url: '/caseManage/projInitiation/deleteInitiation',
        method: 'post',
        data: data
    })
}

// 提交立项
export function submitNapeList(data) {
    return request({
        url: '/caseManage/projInitiation/submitInitiation',
        method: 'post',
        data: data
    })
}

// 根据id查询立项（单条数据（详情））
export function getNapeListById(query) {
    return request({
        url: '/caseManage/projInitiation/selectInitiationById',
        method: 'get',
        params: query
    })
}

// 根据id以及类型查询（单条数据的（资产评估表、立项报告、其他附件））
export function getNapeListByIdAndType(query) {
    return request({
        url: '/caseManage/projInitiation/selectInitiationFileById',
        method: 'get',
        params: query
    })
}

// 统计项目量
export function getProjectCount(query) {
    return request({
        url: '/caseManage/projInitiation/selectCount',
        method: 'get',
        params: query
    })
}

// 分组统计各立项状态数量-(立项)
export function getProjectStateCount(query) {
    return request({
        url: '/caseManage/projInitiation/groupProjectStateCount',
        method: 'get',
        params: query
    })
}

// 获取审批流程
export function getApprovalProcess(data) {
    return request({
        url: '/caseManage/projInitiation/selectApprovalProcess',
        method: 'post',
        data: data
    })
}

// 编辑立项
export function updateInitiation(data) {
    return request({
        url: '/caseManage/projInitiation/updateInitiation',
        method: 'post',
        data: data
    })
}
