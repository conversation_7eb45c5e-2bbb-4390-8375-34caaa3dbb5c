import request from '@/utils/request'

//列表查询
export function listAndQuery(query) {
  return request({
    url: '/caseManage/acquisition/selectListContract',
    method: 'get',
    params: query
  })
}

//项目id下拉框
export function getProjectIdList() {
  return request({
    url: '/caseManage/acquisition/selectProjectIdContract',
    method: 'get',
  })
}


//项目名称下拉框
export function getProjectNameList() {
  return request({
    url: '/caseManage/acquisition/selectProjectNameContract',
    method: 'get',
  })
}


//产品类型下拉框
export function getProductTypeList() {
  return request({
    url: '/caseManage/acquisition/selectProductTypeContract',
    method: 'get',
  })
}

//资产转让方下拉框
export function getTransferorList() {
  return request({
    url: '/caseManage/acquisition/selectTransferorContract',
    method: 'get',
  })
}

//合同编号下拉框
export function getContractNumberList() {
  return request({
    url: '/caseManage/acquisition/selectContractNumber',
    method: 'get',
  })
}

//申请人下拉框
export function getCreateByList() {
  return request({
    url: '/caseManage/acquisition/selectCreateByContract',
    method: 'get',
  })
}

//分组统计状态数量
export function getCountStatus() {
  return request({
    url: "/caseManage/acquisition/groupContractCount",
    method: "get",
  });
}

//撤销项目审批
export function revokeProjectApproval(data) {
  return request({
    url: "/caseManage/zws_zc_approve/zcApproveRevoke",
    method: "post",
    data:data
  });
}

//搜索结果全选撤销
export function revokeFormAll(data) {
  return request({
    url: "/caseManage/acquisition/revokeContract",
    method: "post",
    data:data
  });
}

//查看附件
export function getCheckFileContract(data) {
  return request({
    url: `/caseManage/acquisition/selectContractFile`,
    method: "get",
    params:data
  });
}


