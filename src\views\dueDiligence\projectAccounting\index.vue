<template>
  <div class="app-container">
    <!-- 筛选表达 -->
    <el-form
      inline
      label-width="125px"
      :class="{ 'form-h50': !showSearch }"
      ref="queryRef"
      class="search-form"
    >
      <el-form-item prop="id" label="项目ID">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.id"
          :options="searchOptions.id"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.projectName"
          :options="searchOptions.projectName"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item prop="transferor" label="资产转让方">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.transferor"
          :options="searchOptions.transferor"
          placeholder="资产转让方"
        />
      </el-form-item>
      <el-form-item prop="productName" label="产品类型">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.productName"
          :options="searchOptions.productName"
          placeholder="产品类型"
        />
      </el-form-item>
      <el-form-item prop="paymentStatus" label="付款单状态">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.paymentStatus"
          :options="searchOptions.paymentStatus"
          placeholder="付款单状态"
        />
      </el-form-item>
      <el-form-item prop="totalDebt" label="付款单申请流水号">
        <div class="range-area">
          <el-input
            style="width: 320px"
            v-model="queryParams.totalDebt1"
            placeholder="付款单申请流水号"
          />
        </div>
      </el-form-item>
      <el-form-item prop="paymentStatus" label="付款类型">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.paymentType"
          :options="searchOptions.paymentType"
          placeholder="付款类型"
        />
      </el-form-item>
      <el-form-item prop="createById" label="申请人">
        <MultiSelect
          style="width: 320px"
          v-model="queryParams.createById"
          :options="searchOptions.createById"
          placeholder="申请人"
        />
      </el-form-item>
      <el-form-item prop="applyTime" label="申请时间">
        <el-date-picker
          style="width: 320px"
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item prop="accountNumber" label="收款账号">
        <div class="range-area">
          <el-input
            style="width: 320px"
            v-model="queryParams.accountNumber"
            placeholder="请输入收款账号"
          />
        </div>
      </el-form-item>
      <el-form-item prop="accountName" label="收款账户名">
        <el-input
          style="width: 320px"
          v-model="queryParams.accountName"
          placeholder="请输入账户名"
        />
      </el-form-item>
      <el-form-item prop="receiver" label="收款人">
        <el-input
          style="width: 320px"
          v-model="queryParams.receiver"
          placeholder="请输入收款人"
        />
      </el-form-item>
      <!-- <el-form-item prop="receiver" label="账号名"   style="width:320px">
            <el-input
              v-model="queryParams.receiver"
              placeholder="请输入账号名"
            />
          </el-form-item> -->
    </el-form>
    <!-- 过滤按钮 -->
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <!-- 新增按钮 -->
    <div class="operation-revealing-area mb20">
      <el-button v-if="tabActive === 1 || tabActive === 6" type="primary" plain @click="handleRevoke(row)">撤销</el-button>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <!-- 全选功能 -->
    <SelectedAll
      :dataList="dataList"
      v-model:selectedArr="selectedArr"
      v-model:allQuery="queryParams.allQuery"
    >
      <template #content>
        <div class="ml20">
          <span
            >项目量（件）：<i class="danger">{{ total }}</i></span
          >
        </div>
      </template>
    </SelectedAll>
    <!-- 切换标签 -->
    <el-tabs v-model="tabActive" @tab-change="tabChange()">
      <el-tab-pane
        v-for="v in tabList"
        :key="v"
        :label="`${v.label}(${v.count || 0})`"
        :name="v.value"
      />
    </el-tabs>
    <div class="table-box">
      <!-- 表单 -->
      <el-table
        v-loading="loading"
        ref="multipleTableRef"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :selectable="selectable"
          width="30px"
          align="right"
        />
        <el-table-column
          label="项目ID"
          v-if="columns[0].visible"
          width="160"
          align="center"
          prop="projectId"
        />
        <el-table-column
          label="项目名称"
          v-if="columns[1].visible"
          width="200"
          align="center"
          prop="projectName"
        >
          <template #default="{ row }">
            <el-button type="text" @click="handleDetails(row)">{{
              row.projectName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="资产转让方"
          v-if="columns[2].visible"
          width="120"
          align="center"
          prop="transferor"
        />
        <el-table-column
          label="产品类型"
          v-if="columns[3].visible"
          width="120"
          align="center"
          prop="productName"
        />
        <el-table-column
          label="建账状态"
          v-if="columns[4].visible"
          width="120"
          align="center"
          prop="accountingStatus"
        >
          <template #default="{ row }">
            <el-popover
              placement="bottom"
              width="400"
              trigger="click"
              :show-arrow="true"
            >
              <template #reference>
                <span
                  style="cursor: pointer; color: #409eff"
                  @click="changeStatus(row)"
                >
                  {{ row.accountingStatus }}
                </span>
              </template>
              <ProjectStatus :rowData="row" :formData="formData" />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="付款单申请流水号"
          v-if="columns[5].visible"
          width="120"
          align="center"
          prop="paymentSerialNumber"
        />
        <el-table-column
          label="付款单状态"
          v-if="columns[6].visible"
          width="120"
          align="center"
          prop="paymentStatus"
        />
        <el-table-column
          label="付款类型"
          v-if="columns[7].visible"
          width="120"
          align="center"
          prop="paymentType"
        />
        <el-table-column
          label="收款账号"
          v-if="columns[8].visible"
          width="120"
          align="center"
          prop="accountNumber"
        />
        <el-table-column
          label="账户名"
          v-if="columns[9].visible"
          width="120"
          align="center"
          prop="accountName"
        />
        <el-table-column
          label="收款人"
          v-if="columns[10].visible"
          width="120"
          align="center"
          prop="receiver"
        />
        <el-table-column
          label="开户行"
          v-if="columns[11].visible"
          width="120"
          align="center"
          prop="bankName"
        />
        <el-table-column
          label="合同金额"
          v-if="columns[12].visible"
          width="120"
          align="center"
          prop="contractAmount"
        />
        <el-table-column
          label="申请金额"
          v-if="columns[13].visible"
          width="120"
          align="center"
          prop="applyAmount"
        />
        <el-table-column
          label="备注"
          v-if="columns[14].visible"
          width="120"
          align="center"
          prop="remark"
        />
        <el-table-column
          label="申请人"
          v-if="columns[15].visible"
          width="120"
          align="center"
          prop="applicant"
        />
        <el-table-column
          label="申请时间"
          v-if="columns[16].visible"
          width="120"
          align="center"
          prop="applyDate"
        >
          <template #default="{ row }">
            {{ formatTime(row.applyDate) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="220" label="操作">
          <template #default="{ row }">
            <div>
              <el-button
                v-if="
                  ['待项目建账', '建账失败', '已撤销'].includes(
                    row.accountingStatus
                  )
                "
                type="text"
                @click="handleAdd(row)"
                >新增项目建账</el-button
              >
              <el-button
                v-if="row.accountingStatus !== '待项目建账'"
                type="text"
                @click="handleDetails(row)"
                >详情</el-button
              >
              <el-button
                v-if="row.accountingStatus === '建账待审批'"
                type="text"
                @click="handleRevoke(row)"
                >撤销</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <perviewFile ref="perviewFileRef" />
    <CheckFileDialog ref="checkFileDialogRef" />
  </div>
</template>

<script setup name="StartNapeList">
import perviewFile from "../startNapeList copy/dialog/perviewFile";
import {
  getNapeListByIdAndType,
  getProjectId,
  getProjectName,
  getProductType,
  getUserInfo,
} from "@/api/dueDiligence/startNapeList";
import {
  selectHsListProjectsAccounting,
  groupAccountingStatus,
  zcApproveRevoke,
  getAccountingApprovalProcess,
} from "@/api/dueDiligence/projectAccounting";
import { assetOwnerTree } from "@/api/assets/assetside";
import { getPayMethodOption } from "@/api/dueDiligence/common";
import { ArraysToStrings } from "@/utils/index";
import CheckFileDialog from "../page/checkFile.vue";
import ProjectStatus from "../page/projectStatus.vue";
import { formatTime } from "@/utils/common";
import { fillEmptyToDash } from "@/api/conference/utils";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const formData = ref({});

// 搜索参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
  id: "", // 项目ID
  title: "", // 流程标题
  paymentSerialNumber: "", // 付款单申请流水号
  paymentStatus: "", // 付款单状态
  paymentType: "", // 付款类型
  accountNumber: "", // 收款账号
  accountName: "", // 收款账户名称
  receiver: "", // 收款人
  remark: "", // 备注
  applyTime: "", // 申请时间
  productName: "", // 产品类型
  transferor: "", // 资产转让方
  projectName: "", // 项目名称
});

// 选项数据
const searchOptions = reactive({
  id: [],
  projectName: [],
  productName: [],
  transferor: [],
  accountingStatus: [],
  paymentStatus: [],
  paymentType: [],
  createBy: [],
});

const total = ref(1);

// 表单数据
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const selectedArr = ref([]);
// 表单列数据
const columns = ref([
  { key: 0, label: "项目建账ID", visible: true },
  { key: 1, label: "审批ID", visible: true },
  { key: 2, label: "项目ID", visible: true },
  { key: 3, label: "流程标题", visible: true },
  { key: 4, label: "付款单申请流水号", visible: true },
  { key: 5, label: "建账状态", visible: true },
  { key: 6, label: "付款单状态", visible: true },
  { key: 7, label: "付款类型", visible: true },
  { key: 8, label: "收款账号", visible: true },
  { key: 9, label: "收款账户名称", visible: true },
  { key: 10, label: "收款人", visible: true },
  { key: 11, label: "开户行", visible: true },
  { key: 12, label: "合同金额（元）", visible: true },
  { key: 13, label: "申请金额（元）", visible: true },
  { key: 14, label: "备注", visible: true },
  { key: 15, label: "附件", visible: true },
  { key: 16, label: "创建时间", visible: true },
  { key: 17, label: "创建人", visible: true },
  { key: 18, label: "更新时间", visible: true },
  { key: 19, label: "更新人", visible: true },
  { key: 20, label: "产品类型", visible: true },
  { key: 21, label: "资产转让方", visible: true },
  { key: 22, label: "项目名称", visible: true },
]);

// 表格主要字段，用于空值替换
const tableProps = [
  'projectId',
  'projectName',
  'transferor',
  'productName',
  'accountingStatus',
  'paymentSerialNumber',
  'paymentStatus',
  'paymentType',
  'accountNumber',
  'accountName',
  'receiver',
  'bankName',
  'contractAmount',
  'applyAmount',
  'remark',
  'applicant',
  'applyDate',
];

// 处理查询参数
function handlingQueries(queryParams) {
  let params = ArraysToStrings(queryParams);

  params = {
    ...params,
    applyTimeStart: queryParams.applyTime[0],
    applyTimeEnd: queryParams.applyTime[1],
  };

  // 删除空值字段
  Object.keys(params).forEach((key) => {
    const value = params[key];
    if (!value || value === "" || value === '""' || value === "''") {
      delete params[key];
    }
  });
  return params;
}

// 获取列表数据
function getList() {
  loading.value = true;
  let params = handlingQueries(queryParams);
  const countParams = { ...params };
  delete countParams.accountingStatus;
  Promise.allSettled([
    groupAccountingStatus(countParams),
    selectHsListProjectsAccounting(params),
  ])
    .then(([countRes, listRes]) => {
      loading.value = false;
      total.value = listRes.value.total;
      dataList.value = fillEmptyToDash(listRes.value.rows, tableProps);
      updateTabCounts(countRes.value.data);
      if (countRes.value.code !== 200) console.error("统计数据接口异常");
      if (listRes.value.code !== 200) console.error("列表数据接口异常");
    })
    .catch((error) => {
      proxy.$modal.msgError("获取数据失败");
      loading.value = false;
      console.error("获取数据失败:", error);
    });
}

// 重置查询条件
function resetQuery() {
  // 重置所有查询参数
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    id: null,
    approveId: null,
    id: "",
    title: "",
    paymentSerialNumber: "",
    accountingStatus: "",
    paymentStatus: "",
    paymentType: "",
    accountNumber: "",
    accountName: "",
    receiver: "",
    bankName: "",
    contractAmount: "",
    applyAmount: "",
    remark: "",
    attachments: "",
    applyTime: "",
    createBy: "",
    updateTime: "",
    updateBy: "",
    delFlag: "",
    productName: "",
    transferor: "",
    projectName: "",
  });

  // 重新获取数据
  getList();
}

const selectedRows = ref([]);

// 表格选择事件
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 搜索查询
function handleQuery() {
  // 重置页码到第一页
  queryParams.pageNum = 1;
  getList();
}

// 撤销
function handleRevoke(row = null) {
  proxy.$modal
    .confirm("此操作将撤销选中的申请，是否继续？？", "撤销提示")
    .then(function () {
      loading.value = true;
      if (!row && selectedRows.value.length == 0) {
        proxy.$modal.msgWarning("请选择要撤销的项目");
        loading.value = false;
        return;
      }
      let params = buildRevokeParams(queryParams);
      params.approveIds = row
        ? [row.approveId]
        : selectedRows.value.map((item) => item.approveId);
      zcApproveRevoke(params)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("撤销成功");
          getList();
        })
        .catch((error) => {
          loading.value = false;
          proxy.$modal.msgError("撤销失败");
          console.error("撤销失败:", error);
        });
    });
}

function buildRevokeParams(queryParams) {
  let params = ArraysToStrings(queryParams);
  return {
    approveCode: "accounting",
    allQuery: params.allQuery,
    approvePageRequest: {
      pageSize: params.pageSize,
      pageNum: params.pageNum,
      approveData: params,
      applicant: params.createBy,
      applyDate1: params.applyTime[0],
      applyDate2: params.applyTime[1],
    },
  };
}

// function selectable() {
//   return !queryParams.allQuery;
// }
function handleAdd(row) {
  const query = {
    path: route.path,
    pageType: "projectAccounting",
    progressStatus: 3,
    isDetails: 0,
    rowData: JSON.stringify(row),
    projectName: row.projectName,
    accountingStatus: row.accountingStatus,
    projectId: row.projectId,
    createTime: row.createTime,
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

function handleDetails(row) {
  const query = {
    path: route.path,
    pageType: "projectAccounting",
    progressStatus: 3,
    isDetails: 1,
    rowData: JSON.stringify(row),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

// 查看
function handleCheck(row, fileType) {
  const query = {
    id: row.id,
    fileType: fileType,
  };
  getNapeListByIdAndType(query).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      proxy.$refs["checkFileDialogRef"].openDialog(res.data);
    } else {
      proxy.$modal.msgWarning("未上传文件");
    }
  });
}

// 切换tab
const tabActive = ref(6);

const tabList = ref([
  { label: "待项目建账", count: 0, value: 0 },
  { label: "建账待审批", count: 0, value: 1 },
  { label: "建账审批中", count: 0, value: 2 },
  { label: "建账成功", count: 0, value: 3 },
  { label: "建账失败", count: 0, value: 4 },
  { label: "已撤销", count: 0, value: 5 },
  { label: "全部", count: 0, value: 6 },
]);

function updateTabCounts(resData) {
  tabList.value.forEach((tab) => {
    tab.count = 0;
  });
  const stateMap = {};
  resData.forEach((item) => {
    stateMap[item.accountingStatus] = item.number;
  });

  tabList.value.forEach((tab) => {
    if (tab.label !== "全部") {
      tab.count = stateMap[tab.label] || 0;
    }
  });
  tabList.value.find((tab) => tab.label === "全部").count = tabList.value
    .filter((tab) => tab.label !== "全部")
    .reduce((sum, tab) => sum + tab.count, 0);
}

//切换
function tabChange(tab) {
  const tabObj = tabList.value.find(item => item.value === tabActive.value);
  if (tabActive.value === 6) {
    queryParams.accountingStatus = undefined;
  } else {
    queryParams.accountingStatus = tabObj ? tabObj.label : undefined;
  }
  getList();
}

// 数据请求

// 项目ID下拉
function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      searchOptions.id = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

// 项目名称下拉
function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      searchOptions.projectName = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

// 产品类型下拉
function getProductTypeFun() {
  getProductType().then((res) => {
    searchOptions.productName = res.data.map((item) => ({
      // value: `${item.code} - ${item.info}` || "",
      value: item.code,
      label: item.info,
    }));
  });
}

// 付款单状态
function getVoucherStatusFun() {
  getPayMethodOption({ ruleName: "voucher_status" })
    .then((res) => {
      searchOptions.paymentStatus = res.data.map((item) => ({
        value: item.dictValue || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}

// 资产转让方下拉
function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      searchOptions.transferor = res.data.map((item) => ({
        // value: String(String(item.id).replace(/[^0-9]/g, '')) + '-' + item.label,
        value: item.id,
        label: item.label,
      }));
    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}

// 立项人下拉
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      searchOptions.createById = res.data.map((item) => ({
        // value: `${item.code} - ${item.info}` || "",
        value: item.code,
        label: item.info,
      }));
    })
    .catch((error) => {
      console.error("获取立项人失败:", error);
    });
}

// 付款类型
function getPaymentType() {
  getPayMethodOption({ ruleName: "payment_type" })
    .then((res) => {
      searchOptions.paymentType = res.data.map((item) => ({
        value: item.dictValue || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}

// 建账状态
function changeStatus(row) {
  formData.value = {};
  getAccountingApprovalProcess({ approveId: row.approveId }).then((res) => {
    formData.value = res.data;
  });
}

// 数据初始化
onMounted(() => {
  getProjectIdFun();
  getProjectNameFun();
  getProductTypeFun();
  getVoucherStatusFun();
  getTreeselectFun();
  getUserInfoFun();
  getList();
  getPaymentType();
});
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-bottom: 32px;
  }
  :deep(.el-form.form-h50) {
    height: 62px;
  }
</style>