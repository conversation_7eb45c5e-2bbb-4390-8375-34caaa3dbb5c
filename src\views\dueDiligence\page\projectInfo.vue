<template>
  <div class="app-container" v-loading="loading">
    <!-- 项目信息卡片，当页面类型不是 startNapeList 时显示 -->
    <el-card class="mb20" v-if="!['startNapeList'].includes(pageType)">
      <div class="title">
        项目名称：{{ topShow.projectName || route.query.projectName }}
        <el-tag v-if="topShow.projectStatus || approveStateMap[form.examineState || route.query.examineState]"
          type="primary">{{
            topShow.projectStatus
              ? topShow.projectStatus
              : approveStateMap[form.examineState || route.query.examineState]
          }}</el-tag>
      </div>
      <div class="sub-title-list">
        <div class="sub-title-item">
          项目ID：{{ topShow.projectId || route.query.projectId }}
        </div>
        <div class="sub-title-item">
          申请时间： {{topShow.submitTime }}
        </div>
      </div>
    </el-card> 
    <!-- 项目进展卡片 -->
    <el-card class="mb20">
      <div class="title">项目进展</div>
      <div class="progress-list">
        <div :class="`progress-item ${i == progressStatus && 'active'}`" v-for="(v, i) in progressList"
          @click="gotoStartNapeList(v.label)" :style="{
            cursor:
              ((pageType == 'schemeApproval' && v.label == '立项') || (pageType == 'startNapeList' && v.label == '项目方案审批')) ? 'pointer' : ''
          }" :key="v">
          {{ v.label }}
        </div>
      </div>
    </el-card>

    <!-- 左侧主要内容区域 -->
    <div class="pay-info">
      <el-card>
        <!-- 标签页切换 -->
        <el-tabs v-model="activeTab" @tab-change="handleChangeTab">
          <el-tab-pane v-for="v in showFormInfo.tabList" :key="v" :name="v.value" :disabled="isTabDisabled(v.value)">
            <template #label>
              {{ v.label }}
              <span v-if="pageType === 'projectAccounting'" style="color: #999999; font-size: 12px;">（同时进行资产交割）</span>
            </template>
          </el-tab-pane>
        </el-tabs>
        <!-- 标题和操作按钮区域 -->
        <div class="title-area">
          <div class="title">
            {{
              isDetails == 1 ? showFormInfo.detailsTitle : showFormInfo.title
            }}
          </div>
          <el-button type="primary" v-if="
            showFormInfo.btnName && isDetails == 1 && !route.query.isAppStatus
          " @click="handAdd()">
            {{ showFormInfo.btnName }}
          </el-button>
        </div>

        <!-- 表单区域 -->
        <el-form :key="activeTab" inline label-width="auto" label-position="left" ref="formRef" :model="form"
          :rules="isDetails == 0 ? rules[pageType] : {}">
          <el-row :gutter="24">
            <el-col v-for="v in showFormListFun(showFormInfo.formList || [])" :key="v" :span="v.span">
              <!-- <el-form-item :label="v.label + '：'" :prop="v.prop"> -->
                <el-form-item
                v-if="v.prop !== 'sealType' || form.isSeal === 1"
                :label="v.label + '：'"
                :prop="v.prop"
              >
                <div>
                  <div v-if="isDetails == 1" class="word-break">

                    <span v-if="v.label == '是否需要保证金'">
                      {{ ["否", "是"][form[v.prop]] }}
                    </span>
                    <span v-else>
                      {{
                        v.isNum ? proxy.numFilter(form[v.prop]) : form[v.prop]
                      }}
                    </span>
                  </div>
                  <span v-if="isDetails == 0">
                    <el-input v-model="form[v.prop]" v-if="v.type == 'input'" :maxlength="v.maxlength" clearable
                      filterable style="width: 22vw" :placeholder="v.disabled ? v.disabledPlaceholder : `请输入${v.label}`
                        " :disabled="v.disabled === true" show-word-limit>
                    </el-input>
                    <el-select v-model="form[v.prop]" v-if="v.type == 'select'" clearable filterable
                      :placeholder="`请选择${v.label}`" style="width: 22vw">
                      <el-option v-for="(v1, i1) in optionInfo[v.prop] || []" :key="i1" :label="v1.label"
                        :value="v1.value" />
                    </el-select>
                    <el-radio-group v-model="form[v.prop]" v-if="v.type == 'radio'">
                      <el-radio v-for="v1 in optionInfo[v.prop] || []" :key="v1.value" :label="v1.value">{{ v1.label }}
                      </el-radio>
                    </el-radio-group>
                    <!-- <el-radio-group
                          v-model="form[v.prop]"
                          v-if="v.type == 'radio'"
                        >
                          <el-radio v-for="v in isNoEnum" :label="v" :name="v">{{
                            v
                          }}</el-radio>
                        </el-radio-group> -->
                    <el-date-picker v-model="form[v.prop]" v-if="v.type == 'date'" type="date" placeholder="请选择日期时间"
                      style="width: 22vw" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                    <el-input v-model="form[v.prop]" v-if="v.type == 'textarea'" type="textarea" :rows="2"  style="width: 22vw"
                      :placeholder="`请输入${v.label}`" :maxlength="v.maxlength " show-word-limit/>
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <!-- 文件展示-->
            <template v-if="isDetails == 1">
              <el-col :span="24" v-for="v in fileInfo[pageType] || []" :key="v.prop">
                <el-form-item :label="v.label" :prop="v.prop">
                  <div class="file-list">
                    <div
                      class="file-item"
                      v-for="v in form[v.prop] || []"
                      :key="v"
                    >
                      <div class="file-item-name" style="cursor:pointer;" @click="openFile(v.fileUrl)">{{ v.firstName }}</div>
                      <el-link 
                        v-if ="isImageFile(v.modifyName)"
                        type="primary"
                        underline="never"
                        :href="v.fileUrl"
                        @click.prevent="handleDownload(v.fileUrl)"

                        >下载</el-link
                      >
                      <el-link 
                         v-else
                        type="primary"
                        underline="never"
                        :href="v.fileUrl"
                        >下载</el-link
                      >
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </template>
            <!-- 文件上传区域 -->
            <template v-if="isDetails == 0">
              <el-col :span="24" v-for="v in fileInfo[pageType] || []" :key="v.prop">
                <el-form-item :label="v.label" :prop="v.prop">
                  <div class="file-upload-container">
                    <div class="upload-component">
                      <FileUpload :ref="v.prop + 'Upload'" v-model:fileList="form[v.prop]" uploadFileUrl="/file/upload"
                        :fileType="fileType" :limit="10" :fileSize="10"  @before-remove="onBeforeRemoveFile" />
                    </div>
                    <!-- 文件上传提示信息 -->
                    <div class="upload-tips">
                      <div class="report-tig">
                        (1)上传凭证格式：支持上传JPG，png,pdf,xlsx,docx,doc格式。
                      </div>
                      <div class="report-tig">
                        (2)文件数量：单次最多支持上传10个文件。
                      </div>
                      <div class="report-tig">
                        (3)文件大小：单个文件限制10M以内。
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
        <!-- 数据表格区域 -->
        <div v-if="isDetails == 1">
          <div class="title">{{ showFormInfo.tableTitle }}</div>
          <el-table :data="filteredDataList" v-loading="loading" v-if="showFormInfo.tableList">
            <el-table-column v-for="v in showFormInfo.tableList" :key="v.prop" :label="v.label" :prop="v.prop"
              align="center" :min-width="v.width">
              <template #default="{ row }">
                <div v-if="v.prop.includes('attachment') || v.prop.includes('file')">
                  <el-button type="text" @click="handleCheckFile(row)">查看</el-button>
                </div>
                <!-- <template
                  v-else-if="(v.prop.includes('meetingTitle') || v.prop.includes('decisionNumber')) && pageType === 'conference'">
                  <el-tooltip effect="dark" :content="row[v.prop]" placement="top">
                    <span>
                      {{ typeof row[v.prop] }}-{{ row[v.prop] ? row[v.prop].slice(0, 20) + (row[v.prop].length > 20 ?
                      '...' : '') : '' }}
                    </span>
                  </el-tooltip>
                </template> -->
                <template
                  v-else-if="['conference', 'contract', 'bidding'].includes(pageType)">
                  <el-tooltip
                    effect="dark"
                    :content="row[v.prop]"
                    placement="top"
                    :disabled="!(row[v.prop] && row[v.prop].length > 20)"
                  >
                    <span>
                      {{
                        row[v.prop]
                          ? row[v.prop].length > 20
                            ? row[v.prop].slice(0, 20) + '...'
                            : row[v.prop]
                          : ''
                      }}
                    </span>
                  </el-tooltip>
                </template>
                <span v-else>{{ row[v.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 操作按钮区域 -->
        <div class="text-center mt20">
          <template v-if="pageType === 'startNapeList' && isDetails == 1 && form.projectStatus === '新增立项'">
            <el-button class="ml15" @click="enableEdit">编辑</el-button>
            <el-button class="ml15" type="primary" @click="submit(2)" v-if="!isEdit">提交</el-button>
          </template>
          <Fragment v-if="isDetails == 0">
            <el-button v-if="!['contract', 'bidding', 'conference','projectAccounting'].includes(pageType)" @click="submit(1)" > 保存</el-button>
            <el-button @click="submit(2)" type="primary">{{['contract', 'bidding', 'conference'].includes(pageType)?"确认添加":"提交"}}</el-button>
          </Fragment>
          <el-button class="ml15" @click="toBack()">{{ backOrCancelText }}</el-button>
        </div>
      </el-card>
      <!-- 右侧审批进度卡片- -->
    
        <el-card
          class="right-card"
          v-if="isShowPress && isDetails == 1"
        >
          <div class="sub-title-list">
            <div class="sub-title-item">审批进度</div>
            <img :src="statusImg" alt="审批进度" class="status-img" />
          </div>
          <div class="content-list">
            <div class="content-item" style="display: flex;align-items: flex-start;">
              <p class="label">流程名称：</p>
              <p class="flex-content word-break">{{
                ['contract', 'bidding', 'conference'].includes(pageType)
                  ? proessForm.title
                  : form[formInfo[pageType].titleProp]
              }}</p>
            </div>
            <div class="content-item">
              审批单号：
              {{
                ['contract', 'bidding', 'conference'].includes(pageType)
                  ? proessForm.approveNo?proessForm.approveNo:"--"
                  : form.approveNo
              }}
            </div>
          </div>
          <el-timeline style="max-width: 600px">
          <el-timeline-item
            v-for="v in (['contract', 'bidding', 'conference'].includes(pageType) ? proessForm.progressVoList : form.progressVoList)"
            :key="['contract', 'bidding', 'conference'].includes(pageType) ? (v.nodeSort || v.sort || v.approveStart || v) : v"
            type="primary"
            hollow
          >
            <div>
              <div class="timeline-title">
                {{
                  ['contract', 'bidding', 'conference'].includes(pageType)
                    ? approveEnum[v.approveStart]
                    : approvalStateMap[v.approveStart]
                }}
              </div>
              <div class="timeline-content mb5">
                {{
                  v.approveObjectName
                }}&nbsp;{{
                  v.approveTime
                }}
              </div>
              <div class="timeline-content">
                {{
                  v.reason !== undefined && v.reason !== null
                    ? v.reason
                    :  null
                }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
    <check-file ref="checkFileRef" @cancel="handleDialogCancel" />
  </div>
</template>

<script setup>
import checkFile from "./checkFile.vue";
import {approveEnum,examineStateEnum} from "@/utils/enum";
// import virtualData from "./virtualData";
// import { isNoEnum } from "@/utils/enum";
import FileUpload from "@/components/FileUpload";
import { getDict, getCheckFileBidding } from "@/api/conference/biddings";
import { addMeetingDecision, getDetailList, formatAllDates, dictData, approvalProcess,selectProjectStatus } from "@/api/conference/projectInfo";
import {
  getUserInfo,
  getCurrentUserId,
  getTenderType,
  addNapeList,
  getNapeListByIdAndType,
  getApprovalProcess,
  getNapeListById,
} from "@/api/dueDiligence/startNapeList";
import { assetOwnerTree } from "@/api/assets/assetside";
import { getPayMethodOption } from "@/api/dueDiligence/common";
import {
  zcAddApprove,
  selectHsListProjectsAccounting,
  selectInitiationFileById,
  getAccountingApprovalProcess
} from "@/api/dueDiligence/projectAccounting";
import { nextTick, watch, computed } from "vue";
import CheckFileDialog from "../page/checkFile.vue";
import waitingForSubmission from "@/assets/images/waitingForSubmission.png"
import waitingForApproval from "@/assets/images/waitingForApproval.png";
import approving from "@/assets/images/approving.png";
import notApproved from "@/assets/images/notApproved.png";
import approved from "@/assets/images/approved.png";
import revoked from "@/assets/images/revoked.png";
import { getCheckFile } from "@/api/conference/conference"
import { getCheckFileContract } from "@/api/conference/contract"

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const dataList = ref([]);
const rowData = reactive({});
const proessForm= ref({}); 
//顶部区域展示数据
const topShow = ref({})
const optionInfo = ref({
  transferor: [],
  roductName: [],
  biddingMethod: [],
  projectHostName: [],
  decisionResult: [],
  projectOrganizeName: [],
  paymentStatus: [],
  paymentType: [],
  decisionStatus: [
    { label: "是", value: "是" },
    { label: "否", value: "否" },
  ],
  isMargin: [
    { label: "是", value: 1 },
    { label: "否", value: 0 },
  ],
  isSeal: [
    { label: "是", value: 1 },
    { label: "否", value: 0 },
  ],
});
let initialTab = null;
const paymentTypeList = ref([]);
const paymentStatusList = ref([]);
const activeTab = ref("0");

const progressStatus = ref("0");
const isDetails = ref(1);
const pageType = ref("conference");
const data = reactive({
  form: {
    isMargin: 1,
    decisionStatus: "是",
    isSeal: 1,
  },
  rules: {
    projectAccounting: {
      title: [
        { required: true, message: "请输入流程标题", trigger: "blur" },
        { max: 50, message: "最多50个字符", trigger: "blur" },
      ],
      paymentSerialNumber: [
        { required: true, message: "请输入付款单申请流水号", trigger: "blur" },
        { max: 30, message: "最多30个字符", trigger: "blur" },
      ],
      paymentStatus: [
        { required: true, message: "请选择付款单状态", trigger: "change" },
      ],
      paymentType: [
        { required: true, message: "请选择付款类型", trigger: "change" },
      ],
      accountNumber: [
        { required: true, message: "请输入收款账号", trigger: "blur" },
        { max: 30, message: "最多30个字符", trigger: "blur" },
      ],
      accountName: [
        { required: true, message: "请输入账户名", trigger: "blur" },
        { max: 30, message: "最多30个字符", trigger: "blur" },
      ],
      receiver: [
        { required: true, message: "请输入收款人", trigger: "blur" },
        { max: 30, message: "最多30个字符", trigger: "blur" },
      ],
      bankName: [
        { required: true, message: "请输入开户行", trigger: "blur" },
        { max: 30, message: "最多30个字符", trigger: "blur" },
      ],
      contractAmount: [
        { required: true, message: "请输入合同金额", trigger: "blur" },
        {pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,message: "请输入正数，最多两位小数",trigger: "blur"},
      ],
      applyAmount: [
        { required: true, message: "请输入申请金额", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入正数，最多两位小数",
          trigger: "blur",
        },
      ],
      remark: [{ max: 1000, message: "最多1000个字符", trigger: "blur" }],
    },
    startNapeList: {
      title: [{ required: true, message: "请输入流程标题", trigger: "blur" }],
      projectName: [
        { required: true, message: "请输入项目名称", trigger: "blur" },
      ],
      transferor: [
        { required: true, message: "请选择资产转让方", trigger: "change" },
      ],
      productName: [
        { required: true, message: "请选择产品类型", trigger: "change" },
      ],
      totalDebt: [
        { required: true, message: "请输入债权总金额", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      principal: [
        { required: true, message: "请输入债权本金", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      principalInterest: [
        { required: true, message: "请输入债权本息", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      householdCount: [
        { required: true, message: "请输入户数", trigger: "blur" },
        { pattern: /^([1-9]\d*)$/, message: "请输入正整数", trigger: "blur" },
      ],
      isMargin: [
        { required: true, message: "请选择是否需要保证金", trigger: "change" },
      ],
      marginAmount: [
        { required: true, message: "请输入保证金额", trigger: "blur" },
        {pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,message: "请输入正数，最多两位小数",trigger: "blur"},
      ],
      baseDate: [
        { required: true, message: "请选择基准日", trigger: "change" },
      ],
      planBiddingDate: [
        { required: true, message: "请选择预计竞价日期", trigger: "change" },
      ],
      biddingMethod: [
        { required: true, message: "请选择投标方式", trigger: "change" },
      ],
      priceLimit: [
        { required: true, message: "请输入报价上限", trigger: "blur" },
        {pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,message: "请输入正数，最多两位小数",trigger: "blur"},
      ],
      projectHostName: [
        { required: true, message: "请选择项目主办", trigger: "blur" },
      ],
      projectOrganizeName: [
        { required: true, message: "请选择项目承办", trigger: "change" },
      ],
      projProposalList: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error("请上传立项报告"));
            } else {
              callback();
            }
          },
          trigger: ["change", "blur"],
        },
      ],
    },
    schemeApproval: {
      title: [{ required: true, message: "请输入流程标题", trigger: "blur" }],
      projectName: [
        { required: true, message: "请输入项目名称", trigger: "blur" },
      ],
      approveId: [{ required: true, message: "请输入项目Id", trigger: "blur" }],
      transferor: [
        { required: true, message: "请选择资产转让方", trigger: "change" },
      ],
      productName: [
        { required: true, message: "请选择产品类型", trigger: "change" },
      ],
      totalDebt: [
        { required: true, message: "请输入债权总金额", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      principal: [
        { required: true, message: "请输入债权本金", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      principalInterest: [
        { required: true, message: "请输入债权本息", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入数字，最多两位小数",
          trigger: "blur",
        },
      ],
      householdCount: [
        { required: true, message: "请输入户数", trigger: "blur" },
        { pattern: /^([1-9]\d*)$/, message: "请输入正整数", trigger: "blur" },
      ],
      isMargin: [
        { required: true, message: "请选择是否需要保证金", trigger: "change" },
      ],
      marginAmount: [
        { required: true, message: "请输入保证金额", trigger: "blur" },
        {pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,message: "请输入正数，最多两位小数",trigger: "blur"},
      ],
      baseDate: [
        { required: true, message: "请选择基准日", trigger: "change" },
      ],
      planBiddingDate: [
        { required: true, message: "请选择预计竞价日期", trigger: "change" },
      ],
      biddingMethod: [
        { required: true, message: "请选择投标方式", trigger: "change" },
      ],
      priceLimit: [
        { required: true, message: "请输入报价上限", trigger: "blur" },
        {pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,message: "请输入正数，最多两位小数",trigger: "blur"},
      ],
      projectHostName: [
        { required: true, message: "请选择项目主办", trigger: "blur" },
      ],
      projectOrganizeName: [
        { required: true, message: "请选择项目承办", trigger: "change" },
      ],
      projProposalList: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error("请上传立项报告"));
            } else {
              callback();
            }
          },
          trigger: ["change", "blur"],
        },
      ],
    },
    contract: {
      title: [
        { required: true, message: "请输入流程标题", trigger: "blur" },
        { max: 50, message: "最多输入50个字符", trigger: "blur" },
      ],
      contractName: [
        { required: true, message: "请输入合同名称", trigger: "blur" },
        { max: 30, message: "最多输入30个字符", trigger: "blur" },
      ],
      contractNumber: [
        { required: true, message: "请输入合同编号", trigger: "blur" },
        { max: 50, message: "最多输入50个字符", trigger: "blur" },
      ],
      contractType: [
        { required: true, message: "请输入合同类型", trigger: "blur" },
        { max: 20, message: "最多输入20个字符", trigger: "blur" },
      ],
      contractStatus: [
        { required: true, message: "请选择合同状态", trigger: "change" },
      ],
      initiatorType: [
        { required: true, message: "请选择发起方式", trigger: "change" },
      ],
      signCount: [
        { required: true, message: "请输入签署份数", trigger: "blur" },
        { pattern: /^([1-9]\d*)$/, message: "请输入正整数", trigger: "blur" },
      ],
      sealType: [
        { required: true, message: "请选择印章类型", trigger: "change" },
      ],
    },
    conference: {
      projectId: [{ required: true, message: "请输入项目ID", trigger: "blur" }],
      decisionStatus: [
        { required: true, message: "请选择是否通过", trigger: "change" },
      ],
      meetingTitle: [
        { required: true, message: "请输入会议标题", trigger: "blur" },
        { max: 50, message: "最多输入50个字符", trigger: "blur" },
      ],
      meetingDate: [
        { required: true, message: "请输入决策时间", trigger: "blur" },
      ],
      decisionNumber: [
        { required: true, message: "请输入决策文号", trigger: "blur" },
        { max: 30, message: "最多输入30个字符", trigger: "blur" },
      ],
      decisionResult: [
        { required: true, message: "请输入决策结果", trigger: "blur" },
        { max: 20, message: "最多输入20个字符", trigger: "blur" },
      ],
      remark: [{ max: 1000, message: "最多输入1000个字符", trigger: "blur" }],
    },
    bidding: {
      title: [
        { required: true, message: "请输入流程标题", trigger: "blur" },
        { max: 50, message: "最多输入50个字符", trigger: "blur" },
      ],
      biddingMethod: [
        { required: true, message: "请选择投标方式", trigger: "blur" },
      ],
      price: [
        { required: true, message: "请输入报价金额", trigger: "blur" },
        {
          pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
          message: "请输入正数，最多两位小数",
          trigger: "blur",
        },
      ],
      isSeal: [
        { required: true, message: "请选择是否用印", trigger: "blur" },
      ],
      sealType: [
        {
          required: true,
          message: "请选择印章类型",
          validator: (rule, value, callback) => {
            // 只有当isSealStr为"是"时才必填
            if (form.value.isSeal === 1 && (!value || value === "")) {
              callback(new Error("请选择印章类型"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      remark: [{ max: 1000, message: "最多输入1000个字符", trigger: "blur" }],
    },
  },
});
const progressList = ref([
  { label: "立项", value: "" },
  { label: "项目方案审批", value: "" },
  { label: "收购中", value: "" },
  { label: "项目建账", value: "" },
]);

const formInfo = ref({
  projectAccounting: {
    title: "新增支付建账申请",
    detailsTitle: "支付建账详情",
    btnName: "新增建账",
    tabList: [{ label: "支付建账", value: "0", prop: "0" }],
    titleProp: "title",
    formList: [
      { label: "流程标题", prop: "title", span: 12, type: "input" },
      {
        label: "付款单申请流水号",
        prop: "paymentSerialNumber",
        span: 12,
        type: "input",
      },
      { label: "付款单状态", prop: "paymentStatus", span: 12, type: "select" },
      { label: "付款类型", prop: "paymentType", span: 12, type: "select" },
      { label: "收款账号", prop: "accountNumber", span: 12, type: "input" },
      { label: "收款账户名称", prop: "accountName", span: 12, type: "input" },
      { label: "收款人", prop: "receiver", span: 12, type: "input" },
      { label: "开户行", prop: "bankName", span: 12, type: "input" },
      {
        label: "合同金额（元）",
        prop: "contractAmount",
        span: 12,
        type: "input",
      },
      { label: "申请金额（元）", prop: "applyAmount", span: 12, type: "input" },
      { label: "备注", prop: "remark", span: 24, type: "textarea" },
    ],
    tableTitle: "支付建账列表",
    tableList: [
      { label: "流程标题", prop: "title", width: 120 },
      { label: "建账状态", prop: "accountingStatus", width: 120 },
      { label: "付款单申请流水号", prop: "paymentSerialNumber", width: 120 },
      { label: "付款单状态", prop: "paymentStatus", width: 120 },
      { label: "付款类型", prop: "paymentType", width: 120 },
      { label: "收款账号", prop: "accountNumber", width: 120 },
      { label: "账户名", prop: "accountName", width: 120 },
      { label: "收款人", prop: "receiver", width: 120 },
      { label: "开户行", prop: "bankName", width: 120 },
      { label: "合同金额", prop: "contractAmount", width: 120 },
      { label: "申请金额", prop: "applyAmount", width: 120 },
      { label: "备注", prop: "remark", width: 120 },
      { label: "申请人", prop: "applicant", width: 120 },
      { label: "申请时间", prop: "applyDate", width: 120 },
      { label: "附件", prop: "attachments", width: 120 },
    ],
  },
  contract: {
    title: "新增合同",
    detailsTitle: "合同详情",
    btnName: "新增合同",
    tabList: [
      { label: "会议决策", value: "0", prop: "conference" },
      { label: "竞价收购", value: "1", prop: "bidding" },
      { label: "合同发起", value: "2", prop: "contract" },
    ],
    titleProp: "contract_processTitle",
    formList: [
      {
        label: "流程标题",
        prop: "title",
        span: 12,
        type: "input",
        maxlength: 50
      },
      {
        label: "合同名称",
        prop: "contractName",
        span: 12,
        type: "input",
        maxlength: 30
      },
      {
        label: "合同编号",
        prop: "contractNumber",
        span: 12,
        type: "input",
        maxlength: 50
      },
      {
        label: "合同类型",
        prop: "contractType",
        span: 12,
        type: "input",
        maxlength: 20
      },
      {
        label: "合同状态",
        prop: "contractStatus",
        span: 12,
        type: "select",
      },
      {
        label: "发起方式",
        prop: "initiatorType",
        span: 12,
        type: "select",
      },
      { label: "签署份数", prop: "signCount", span: 12, type: "input" },
      { label: "印章类型", prop: "sealType", span: 12, type: "select" },
      // { label: '备注', prop: 'remark', span: 24, type: 'textarea' },
    ],
    tableTitle: "合同列表",
    tableList: [
      { label: "合同名称", prop: "projectName", width: 120 },
      { label: "合同编号", prop: "contractNumber", width: 120 },
      { label: "合同类型", prop: "contractType", width: 120 },
      { label: "合同状态", prop: "contractApproveStatus", width: 120 },
      { label: "发起方式", prop: "initiatorType", width: 120 },
      { label: "签署份数", prop: "signCount", width: 120 },
      { label: "印章类型", prop: "sealType", width: 120 },
      { label: "备注", prop: "remark", width: 120 },
      { label: "创建人", prop: "createBy", width: 120 },
      { label: "创建时间", prop: "createTime", width: 120 },
      { label: "附件", prop: "attachment", width: 120 },
    ],
  },
  conference: {
    title: "新增会议决策",
    detailsTitle: "会议决策",
    btnName: "添加会议决策",
    tabList: [
      { label: "会议决策", value: "0", prop: "conference" },
      { label: "竞价收购", value: "1", prop: "bidding" },
      { label: "合同发起", value: "2", prop: "contract" },
    ],
    titleProp: "conference_processTitle",
    formList: [
      {
        label: "是否通过",
        prop: "conference_decisionStatus",
        span: 12,
        type: "radio",
      },
      // { label: '流程标题', prop: 'conference_processTitle', span: 12, type: 'input' },
      {
        label: "会议标题",
        prop: "conference_meetingTitle",
        span: 12,
        type: "input",
        maxlength: 50
      },
      {
        label: "决策时间",
        prop: "conference_meetingDate",
        span: 12,
        type: "date",
        format: "YYYY-MM-DD HH:mm:ss",
        valueFormat: "YYYY-MM-DD HH:mm:ss",
      },
      {
        label: "决策文号",
        prop: "conference_decisionNumber",
        span: 12,
        type: "input",
        maxlength: 30
      },
      {
        label: "决策结果",
        prop: "conference_decisionResult",
        span: 12,
        type: "select",
      },
      {
        label: "备注",
        prop: "conference_remark",
        maxlength: 1000,
        span: 24,
        type: "textarea",
      },
    ],
    tableTitle: "会议列表",
    tableList: [
      { label: "会议标题", prop: "meetingTitle", width: 120 },
      { label: "决策时间", prop: "meetingDate", width: 120 },
      { label: "决策文号", prop: "decisionNumber", width: 120 },
      { label: "决策结果", prop: "decisionResult", width: 120 },
      { label: "创建人", prop: "createBy", width: 120 },
      { label: "创建时间", prop: "createTime", width: 120 },
      { label: "是否通过", prop: "decisionStatus", width: 120 },
      { label: "备注", prop: "remark", width: 120 },
      { label: "附件", prop: "attachment", width: 120 },
    ],
  },
  bidding: {
    title: '新增竞价申请',
    detailsTitle: "竞价详情",
    btnName: "新增竞价申请",
    tabList: [
      { label: "会议决策", value: "0", prop: "conference" },
      { label: "竞价收购", value: "1", prop: "bidding" },
      { label: "合同发起", value: "2", prop: "contract" },
    ],
    titleProp: "bidding_processTitle",
    formList: [
      {
        label: "流程标题",
        prop: "title",
        span: 12,
        type: "input",
        maxlength: 50
      },
      {
        label: "投标方式",
        prop: "biddingMethod",
        span: 12,
        type: "select",
      },
      {
        label: "报价金额",
        prop: "price",
        span: 12,
        type: "input",
      },
      {
        label: "是否用印",
        prop: "isSeal",
        span: 12,
        type: "radio",
      },
      { label: "印章类型", prop: "sealType", span: 12, type: "select" },
      { label: "备注", prop: "remark", span: 24, type: "textarea", maxlength: 500 },
    ],
    tableTitle: "竞价列表",
    tableList: [
      { label: "流程标题", prop: "title", width: 120 },
      { label: "项目名称", prop: "projectName", width: 120 },
      { label: "竞价状态", prop: "biddingStatus", width: 120 },
      { label: "产品类型", prop: "productType", width: 120 },
      { label: "投标方式", prop: "biddingMethod", width: 120 },
      { label: "报价金额", prop: "price", width: 120 },
      { label: "是否用印", prop: "isSeal", width: 120 },
      { label: "印章类型", prop: "sealType", width: 120 },
      { label: "备注", prop: "remark", width: 120 },
      { label: "申请人", prop: "createBy", width: 120 },
      { label: "申请部门", prop: "createByDepartment", width: 120 },
      { label: "申请时间", prop: "createTime", width: 120 },
      { label: "附件", prop: "attachment", width: 120 },
    ],
  },
  startNapeList: {
    title: "基本信息",
    detailsTitle: "基本信息",
    tabList: [{ label: "项目立项", value: "0", prop: "0" }],
    titleProp: "title",
    formList: [
      {
        label: "流程标题",
        prop: "title",
        span: 24,
        type: "input",
        maxlength: 50,
      },
      {
        label: "项目名称",
        prop: "projectName",
        span: 12,
        type: "input",
        maxlength: 50,
      },
      {
        label: "项目ID",
        prop: "projectId",
        span: 12,
        disabled: true,
        type: "input",
        disabled: true,
        disabledPlaceholder: "系统自动生成，不可修改",
      },
      { label: "资产转让方", prop: "transferor", span: 12, type: "select" },
      { label: "产品类型", prop: "productName", span: 12, type: "select" },
      {
        label: "债权总金额",
        prop: "totalDebt",
        span: 12,
        type: "input",
        isNum: true,
      },
      {
        label: "债权本金（元）",
        prop: "principal",
        span: 12,
        type: "input",
        isNum: true,
        instruct: true,
      },
      {
        label: "债权本息（元）",
        prop: "principalInterest",
        span: 12,
        type: "input",
        isNum: true,
      },
      { label: "户数", prop: "householdCount", span: 12, type: "input" },
      { label: "是否需要保证金", prop: "isMargin", span: 12, type: "radio" },
      {
        label: "保证金额（元）",
        prop: "marginAmount",
        span: 12,
        type: "input",
        show: (form) => form.isMargin === 1,
        isNum: true,
      },
      { label: "基准日", prop: "baseDate", span: 12, type: "date" },
      {
        label: "预计竞价日期",
        prop: "planBiddingDate",
        span: 12,
        type: "date",
      },
      { label: "投标方式", prop: "biddingMethod", span: 12, type: "select" },
      {
        label: "报价上限（元）",
        prop: "priceLimit",
        span: 12,
        type: "input",
        isNum: true,
      },
      { label: "项目主办", prop: "projectHostName", span: 12, type: "select" },
      {
        label: "项目承办",
        prop: "projectOrganizeName",
        span: 12,
        type: "select",
      },
      {
        label: "申请人",
        prop: "createBy",
        span: 12,
        show: (form) => form.isDetails == 1 && form.projectStatus !== '新增立项',
      },
      {
        label: "申请时间",
        prop: "createTime",
        span: 12,
        show: (form) => form.isDetails == 1 && form.projectStatus !== '新增立项',
      },
    ],
  },
  schemeApproval: {
    title: "基本信息",
    detailsTitle: "基本信息",
    tabList: [{ label: "项目方案审批", value: "0", prop: "0" }],
    titleProp: "title",
    formList: [
      {
        label: "流程标题",
        prop: "title",
        span: 24,
        type: "input",
        maxlength: 50,
      },
      {
        label: "项目名称",
        prop: "projectName",
        span: 12,
        type: "input",
        maxlength: 50,
      },
      {
        label: "项目ID",
        prop: "projectId",
        span: 12,
        disabled: true,
        type: "input",
        disabled: true,
        disabledPlaceholder: "系统自动生成，不可修改",
      },
      { label: "资产转让方", prop: "transferor", span: 12, type: "select" },
      { label: "产品类型", prop: "productName", span: 12, type: "select" },
      {
        label: "债权总金额",
        prop: "totalDebt",
        span: 12,
        type: "input",
        isNum: true,
      },
      {
        label: "债权本金（元）",
        prop: "principal",
        span: 12,
        type: "input",
        isNum: true,
        instruct: true,
      },
      {
        label: "债权本息（元）",
        prop: "principalInterest",
        span: 12,
        type: "input",
        isNum: true,
      },
      { label: "户数", prop: "householdCount", span: 12, type: "input" },
      { label: "是否需要保证金", prop: "isMargin", span: 12, type: "radio" },
      {
        label: "保证金额（元）",
        prop: "marginAmount",
        span: 12,
        type: "input",
        show: (form) => form.isMargin === 1,
        isNum: true,
      },
      { label: "基准日", prop: "baseDate", span: 12, type: "date" },
      {
        label: "预计竞价日期",
        prop: "planBiddingDate",
        span: 12,
        type: "date",
      },
      { label: "投标方式", prop: "biddingMethod", span: 12, type: "select" },
      {
        label: "报价上限（元）",
        prop: "priceLimit",
        span: 12,
        type: "input",
        isNum: true,
      },
      { label: "项目主办", prop: "projectHostName", span: 12, type: "select" },
      {
        label: "项目承办",
        prop: "projectOrganizeName",
        span: 12,
        type: "select",
      },
      {
        label: "申请人",
        prop: "createBy",
        span: 12,
        type: "input",
        isDetails: 1,
      },
      {
        label: "申请时间",
        prop: "createTime",
        span: 12,
        type: "input",
        isDetails: 1,
      },
    ],
  },
});
const dictList = [
  {
    ruleName: "bidding_method",
    targetKey: "biddingMethod",
  },
  {
    ruleName: "seal_type",
    targetKey: "sealType",
  },
  {
    ruleName: 'contract_status',
    targetKey: "contractStatus"
  },
  {
    ruleName: 'start_method',
    targetKey: "initiatorType"
  },
];

dictList.forEach((item) => {
  getDict({ ruleName: item.ruleName })
    .then((res) => {
      optionInfo.value[item.targetKey] = (res.data || []).map(i => ({
        label: i.dictLabel,
        value: i.dictLabel,
      }));
    })
    .catch((error) => {
      console.error(`获取 ${item.ruleName} 字典数据失败:`, error);
    });
});

dictList.forEach((item) => {
  getDict({ ruleName: item.ruleName })
    .then((res) => {
      optionInfo.value[item.targetKey] = (res.data || []).map(i => ({
        label: i.dictLabel,
        value: i.dictLabel,
      }));
    })
    .catch((error) => {
      console.error(`获取 ${item.ruleName} 字典数据失败:`, error);
    });
});

const { form, rules } = toRefs(data);

const fileInfo = ref({
  schemeApproval: [
    { label: "立项报告", prop: "schemeApproval_feasibilityReport" },
    { label: "资产评估表", prop: "schemeApproval_fileList" },
    { label: "其他附件", prop: "schemeApproval_ohterFileList" },
  ],
  contract: [
    { label: "合同附件:", prop: "conProposalList" },
    { label: "其他附件:", prop: "projProposalList" },
  ],
  bidding: [{ label: "附件:", prop: "projProposalList" }],
  conference: [{ label: "附件:", prop: "projProposalList" }],
  projectAccounting: [{ label: "其他附件", prop: "projProposalList" }],
  startNapeList: [
    { label: "立项报告", prop: "projProposalList" },
    { label: "资产评估表", prop: "assetEvaluationList" },
    { label: "其他附件", prop: "otherAccessoryList" },
  ],
});

const checkFileRef = ref(null);

watch(
  () => form.value.projProposalList,
  (newVal) => {
    if (isDetails.value == 0) {
      proxy.$refs["formRef"]?.validateField("projProposalList");
    }
  }
);

// 切换tab
function handleChangeTab(val) {
  // 切换tab
  const tabInfo = showFormInfo.value.tabList.find((v) => v.value == val) || {};
  statusImg.value='';
  pageType.value = tabInfo.prop;
  if (val === initialTab && isDetails.value) {
    DetailData(rowData)
    processRequest()

  } else {
    // 初始化为该tab需要的字段，全部置空
    form.value = {};
    proessForm.value ={};
    topShow.value = {};
    const fields = (formInfo.value[pageType.value]?.formList || []).map(item =>
      item.prop.includes('_') ? item.prop.split('_')[1] : item.prop
    );
    fields.forEach(key => form.value[key] = '');
    dataList.value = []
  }
  // // 只在非详情模式下赋默认值
  if (isDetails.value == 0) {
    form.value.decisionStatus = "是";
    form.value.isSeal = 1;
  }
}
//禁用tab
function isTabDisabled(tabName) {
  
  if (initialTab == '0') {
    // 只能点 0
    return tabName !== '0';
  }
  if ( initialTab== '1') {
    // 只能点 0、1
    return tabName === '2';
  }
  // activeTab 为 2 时，全部可点
  return false;
}


// 新增
function handAdd(btnName) {
  isDetails.value = 0;
  getOptionList()
  form.value = {};
  const fields = (formInfo.value[pageType.value]?.formList || []).map(item =>
    item.prop.includes('_') ? item.prop.split('_')[1] : item.prop
  );
  fields.forEach(key => form.value[key] = '');

  // 只在新增时赋默认值
  form.value.decisionStatus = "是";
  form.value.isSeal = 1;
}
// 新增立项提交

//取isSubmit
function isSubmitFlag() {
  return localStorage.getItem('isSubmit') === '1';
}
//存isSubmit判断是否是点击了新增模式详情
function setIsSubmit(val) {
  localStorage.setItem('isSubmit', val ? '1' : '0');
}
function submit(operationType) {
  setIsSubmit(true);
  nextTick(() => {
    proxy.$refs["formRef"].validate((valid) => {
      loading.value = true;
      if (valid) {
        switch (pageType.value) {
          case "projectAccounting":
            submitProjectAccounting();
            break;
          case "startNapeList":
            submitStartNapeList(operationType); // 传递参数
            break;
          case "contract":
            submitContract();
            break;
          case "conference":
            submitConference();
            break;
          case "bidding":
            submitBidding();
            break;
          default:
            break;
        }
      } else {
        loading.value = false;
      }
    });
  });
}
function submitProjectAccounting() {
  const submitData = PASubmitData();
  zcAddApprove(submitData)
    .then((res) => {
      loading.value = false;
      proxy.$modal.msgSuccess("新增成功！");
      proxy.$refs["formRef"].resetFields();
      router.push({ path: `/dueDiligence/projectAccounting` });
      router.push({ path: `/dueDiligence/projectAccounting` });
    })
    .catch((err) => {
      let msg = '';
      if (err) {
        msg = err.message || String(err);
        msg = msg.replace(/^Error:\s*/, '').replace(/,+$/, '').trim();
      } else {
        msg = '操作失败';
      }
      proxy.$modal.msgError(msg);
      if (err) {
        msg = err.message || String(err);
        msg = msg.replace(/^Error:\s*/, '').replace(/,+$/, '').trim();
      } else {
        msg = '操作失败';
      }
      proxy.$modal.msgError(msg);
      loading.value = false;
    });
}

function submitStartNapeList(operationType) {
  // 处理参数格式
  const submitData = SNSubmitData(form.value, operationType);
  // 新增立项
  addNapeList(submitData)
    .then((res) => {
      loading.value = false;
      if (operationType === 1) {
        getNapeListById({ id: res.data }).then((res) => {
          initDetailData(res.data)
          isDetails.value = 1;
          getStatusImg()
          proxy.$modal.msgSuccess("保存成功");
        })
      } else {
        proxy.$modal.msgSuccess("新增成功！");
        proxy.$refs["formRef"].resetFields();
        router.push({ path: `/dueDiligence/startNapeList` });
      }
    })
    .catch((err) => {
      proxy.$modal.msgError("新增失败");
      loading.value = false;
    });
}

function submitContract() {
  const submitData = CSubmitData();
  zcAddApprove(submitData)
  .then((res) => {
    proxy.$modal.msgSuccess("新增成功！");
    isDetails.value = 1;
    DetailData(rowData)
  })
  .catch((err) => {
    proxy.$modal.msgError(err.msg);
  })
  .finally(() => {
    loading.value = false;
  });
}

function submitBidding() {
  const submitData = BSubmitData();
  zcAddApprove(submitData)
  .then((res) => {
    proxy.$modal.msgSuccess("新增成功！");
    isDetails.value = 1;
    DetailData(rowData)
  })
  .catch((err) => {
    proxy.$modal.msgError(err.msg);
  })
  .finally(() => {
    loading.value = false;
  });
}

//添加会议
function submitConference() {
  const submitData = CONsubmitData(form.value);
  addMeetingDecision(submitData)
    .then((res) => {
      proxy.$modal.msgSuccess("新增成功！");
      proxy.$refs["formRef"].resetFields();
      isDetails.value = 1;
      DetailData(rowData)
    })
    .catch((err) => {
      proxy.$modal.msgError(err.msg);
    })
    .finally(() => {
      loading.value = false;
    });;
}
//处理会议决策的数据
function CONsubmitData(form) {
  const newForm = {
    ...form,
    ...(form.meetingDate && { meetingDate: form.meetingDate + " 00:00:00" }),
    projProposalList: handleFileList(form.projProposalList),
    id: rowData.id,
    projectId: rowData.projectId,
  };
  delete newForm.conference_fileList;
  return newForm;
}

// 处理文件参数数据
function handleFileList(list) {
  return (list || []).map((f) => ({
    firstName: f.name || f.originalFilename || "",
    modifyName: f.response?.data?.name || f.modifyName || "",
    fileUrl: f.url || f.fileUrl || (f.response && f.response.data && f.response.data.url) || "",
  }));
}

// 处理新增立项请求参数
function SNSubmitData(form, operationType) {
  let transferorId = 0,
    transferor = "";
  if (form.transferor && form.transferor.includes("-")) {
    const arr = form.transferor.split("-");
    transferorId = Number(arr[0].replace(/[^\d]/g, ""));
    transferor = arr.slice(1).join("-").trim();
  } else {
    transferorId = Number(form.transferorId) || 0;
    transferor = (form.transferor || "").trim();
  }

  // 2. 处理 productName
  let productId = 0,
    productName = "";
  if (form.productName && form.productName.includes("-")) {
    const match = form.productName.match(/(\d+)\s*-\s*(.+)/);
    if (match) {
      productId = Number(match[1]);
      productName = match[2].trim();
    } else {
      const arr = form.productName.split("-");
      productId = Number(arr[0].replace(/[^\d]/g, ""));
      productName = arr.slice(1).join("-").trim();
    }
  } else {
    productId = Number(form.productId) || 0;
    productName = (form.productName || "").trim();
  }

  // 3. 处理 projectHostName
  let projectHostId = 0,
    projectHostName = "";
  if (form.projectHostName && form.projectHostName.includes("-")) {
    const arr = form.projectHostName.split("-");
    projectHostId = Number(arr[0].replace(/[^\d]/g, ""));
    projectHostName = arr.slice(1).join("-").trim();
  } else {
    projectHostId = Number(form.projectHostId) || 0;
    projectHostName = (form.projectHostName || "").trim();
  }

  // 4. 处理 projectOrganizeName
  let projectOrganizeId = 0,
    projectOrganizeName = "";
  if (form.projectOrganizeName && form.projectOrganizeName.includes("-")) {
    const arr = form.projectOrganizeName.split("-");
    projectOrganizeId = Number(arr[0].replace(/[^\d]/g, ""));
    projectOrganizeName = arr.slice(1).join("-").trim();
  } else {
    projectOrganizeId = Number(form.projectOrganizeId) || 0;
    projectOrganizeName = (form.projectOrganizeName || "").trim();
  }

  // 6. 处理 isMargin
  let isMargin = 0;
  if (form.isMargin === "是" || form.isMargin === 1 || form.isMargin === "1")
    isMargin = 1;

  // 处理 createTime
  let createTime = '';
  if (Array.isArray(form.createTime)) {
    createTime = form.createTime.join(' - ');
  } else if (typeof form.createTime === 'string') {
    createTime = form.createTime.trim();
  }

  // 处理 baseDate
  let baseDate = '';
  if (Array.isArray(form.baseDate)) {
    baseDate = form.baseDate.join(' - ');
  } else if (typeof form.baseDate === 'string') {
    baseDate = form.baseDate.trim();
  }

  // 处理 planBiddingDate
  let planBiddingDate = '';
  if (Array.isArray(form.planBiddingDate)) {
    planBiddingDate = form.planBiddingDate.join(' - ');
  } else if (typeof form.planBiddingDate === 'string') {
    planBiddingDate = form.planBiddingDate.trim();
  }

  // 7. 组装最终对象  
  return {
    title: (form.title || "").trim(),
    projectName: (form.projectName || "").trim(),
    transferor: transferor,
    transferorId: transferorId,
    totalDebt: Number(form.totalDebt) || 0,
    principal: Number(form.principal) || 0,
    principalInterest: Number(form.principalInterest) || 0,
    householdCount: Number(form.householdCount) || 0,
    projectHostId: projectHostId,
    projectHostName: projectHostName,
    projectOrganizeId: projectOrganizeId,
    projectOrganizeName: projectOrganizeName,
    productId: productId,
    productName: productName,
    baseDate,
    planBiddingDate,
    biddingMethod: (form.biddingMethod || "").trim(),
    priceLimit: Number(form.priceLimit) || 0,
    isMargin: Number(isMargin),
    marginAmount: Number(form.marginAmount) || 0,
    projProposalList: handleFileList(form.projProposalList),
    otherAccessoryList: handleFileList(form.otherAccessoryList),
    assetEvaluationList: handleFileList(form.assetEvaluationList),
    operationType: operationType, // 新增
    createTime,
  };
}

// 处理新增建账参数
function PASubmitData() {
  Object.keys(form.value).forEach((key) => {
    if (["approveId", "id", "contractAmount", "applyAmount"].includes(key)) {
      const val = form.value[key];
      if (
        val !== undefined &&
        val !== null &&
        val !== "" &&
        typeof val !== "number"
      ) {
        const num = Number(val);
        form.value[key] = isNaN(num) ? val : num;
      }
    }
  });
  return {
    approveCode: "accounting",
    approveData: {
      id: rowData.id,
      ...form.value,
      projProposalList: handleFileList(form.value.projProposalList),
    },
  };
}

function CSubmitData() {
  Object.keys(form.value).forEach((key) => {
    if (["signCount"].includes(key)) {
      const val = form.value[key];
      if (
        val !== undefined &&
        val !== null &&
        val !== "" &&
        typeof val !== "number"
      ) {
        const num = Number(val);
        form.value[key] = isNaN(num) ? val : num;
      }
    }
  });
  return {
    approveCode: "contract",
    approveData: {
      approveId:rowData.approveId,
      id: rowData.id,
      projectId: rowData.projectId,
      ...form.value,
      conProposalList: handleFileList(form.value.conProposalList),
      projProposalList: handleFileList(form.value.projProposalList),
    },
  };
}

function BSubmitData() {
  Object.keys(form.value).forEach((key) => {
    if (["price"].includes(key)) {
      const val = form.value[key];
      if (
        val !== undefined &&
        val !== null &&
        val !== "" &&
        typeof val !== "number"
      ) {
        const num = Number(val);
        form.value[key] = isNaN(num) ? val : num;
      }
    }
  });
  return {
    approveCode: "bidding",
    approveData: {
      approveId:rowData.approveId,
      id: rowData.id,
      projectId: rowData.projectId,
      ...form.value,
      projProposalList: handleFileList(form.value.projProposalList),
    },
  };
}

function toBack() {
  const obj = { path: route.query.path };
  proxy.$tab.closeOpenPage(obj);
}

// function showFormListFun(formList) {
//   const showList = formList.filter(
//     (v) =>
//       (v.isDetails == isDetails.value && isDetails.value == 1) || !v.isDetails
//   );s
//   return showList;
// }
function showFormListFun(formList) {
  const filteredList = formList.filter(
    (v) =>
      ((v.isDetails == isDetails.value && isDetails.value == 1) ||
        !v.isDetails) &&
      (typeof v.show !== "function" || v.show(form.value))
  );

  const processedList = filteredList.map((item) => {
    let newProp = item.prop;

    // 如果包含下划线 '_'，则取后面的部分
    if (item.prop.includes("_")) {
      newProp = item.prop.split("_")[1];
    }

    return {
      ...item,
      prop: newProp,
    };
  });

  return processedList;
}

const showFormInfo = computed(() => {
  // console.log('formInfo.value[pageType.value]', formInfo.value[pageType.value]);
  // console.log('formInfo.value', formInfo.value);
  // console.log('pageType.value', pageType.value);
  return formInfo.value[pageType.value] || [];
});
const isShowPress = computed(() => !["conference"].includes(pageType.value));

// 过滤处理表单数据
const filteredDataList = computed(() => {
  // 当前表格需要的字段
  const props = (showFormInfo.value.tableList || []).map((item) => item.prop);
  // 过滤掉所有相关字段都为空/不存在的对象
  return dataList.value.filter((row) =>
    props.some(
      (prop) =>
        row[prop] !== undefined && row[prop] !== null && row[prop] !== ""
    )
  );
});

// 文件上传
const fileType = ref(["jpg", "png", "xlsx", "docx", "doc", "pdf"]);

// 监听资产转让方选择变化，动态设置产品类型下拉选项
watch(
  () => form.value.transferor,
  (newVal) => {
    const transferor = (optionInfo.value.transferor || []).find(
      (item) => item.value === newVal
    );
    const children =
      transferor && Array.isArray(transferor.children)
        ? transferor.children
        : [];
    optionInfo.value.productName = children.map((item) => ({
      value: `${item.id} - ${item.label}` || "",
      label: item.label,
    }));
  }
);

// 接口请求
// 资产转让方选项
function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("assetOwnerTree返回数据格式错误:", res.data);
        return;
      }

      const transferorOptions = res.data.map((item) => ({
        value:
          String(String(item.id).replace(/[^0-9]/g, "")) + "-" + item.label,
        label: item.label,
        children: item.children || [],
      }));
      optionInfo.value.transferor = transferorOptions;
    })
    .catch((error) => {
      console.error("获取资产转让方数据失败:", error);
      console.error("错误详情:", error.message, error.stack);
    });
}

// 项目主办和承办选项下拉
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("接口返回数据格式错误:", res.data);
        return;
      }
      const userOptions = res.data.map((item) => ({
        value: `${item.code} - ${item.info}` || "",
        label: item.info,
      }));

      optionInfo.value.projectOrganizeName = userOptions;
      optionInfo.value.projectHostName = userOptions;
      setCurrentUserAsDefault(userOptions);
    })
    .catch((error) => {
      console.error("获取用户信息失败:", error);
    });
}
function setCurrentUserAsDefault(userOptions) {
  getCurrentUserId()
    .then((currentUserRes) => {
      if (!currentUserRes.data) {
        console.error("获取当前用户ID失败:", currentUserRes.data);
        return;
      }
      const currentUserId = currentUserRes.data;
      const currentUserOption = userOptions.find((option) =>
        option.value.includes(currentUserId)
      );
      if (currentUserOption) {
        form.value.projectHostName = currentUserOption.value;
      } else {
        form.value.projectHostName = "";
      }
    })
    .catch((error) => {
      console.error("获取当前用户ID失败:", error);
    });
}

function initDetailData(rowData) {
  if (!rowData) return;
  // 1. 基本信息赋值
  Object.assign(form.value, rowData);
  // 2. 审批流程
  if (rowData.approveId) {
    getApprovalProcess({ approveId: rowData.approveId })
      .then((res) => {
        if (res.data) Object.assign(form.value, res.data);
        getStatusImg();
      })
      .catch((err) => {
        console.error("获取审批流程失败:", err);
      });
  }
  getStatusImg();

  // 3. 获取文件
  const fileTypeMap = {
    0: "projProposalList",
    1: "assetEvaluationList",
    2: "otherAccessoryList",
  };
  Object.entries(fileTypeMap).forEach(([fileType, key]) => {
    getNapeListByIdAndType({ id: rowData.id, fileType: Number(fileType) })
      .then((res) => {
        if (
          res.code === 200 &&
          Array.isArray(res.data) &&
          res.data.length > 0
        ) {
          form.value[key] = res.data;
        }
      })
      .catch((err) => {
        console.error(`获取文件类型${fileType}失败:`, err);
      });
  });

}
isDetails.value = route.query.isDetails;
initialTab = route.query?.activeTab || "0";

//顶部流程数据请求
function processRequest(){
  if (route.query.rowData) {
    let rowData = {};
    try {
      rowData = JSON.parse(route.query.rowData);
    } catch (e) {
      rowData = {};
    }
    if (rowData.projectId) {
      // 流程请求
      selectProjectStatus(rowData.projectId).then((res) => {
        topShow.value = res.data;
      }).catch((err) => {
        console.error("获取项目状态失败:", err);
      });
    }
  }
}

function getList() {
  pageType.value = route.query.pageType;
  progressStatus.value = route.query.progressStatus;
  getOptionList();
  if (route.query?.rowData) {
    try {
      activeTab.value = route.query?.activeTab || "0";
      Object.assign(rowData, JSON.parse(route.query.rowData));
      DetailData(rowData);

    } catch (error) {
      console.error("解析 rowData 失败:", error);
    }
  }
}

onMounted(() => {
  getList();
  processRequest()
});

watch(
  () => route.query,
  () => {
    if (route.query.path) {
      getList();
    }
  },
  { deep: true }
);


// 获取选项
function getOptionList() {
  if (isDetails.value != 0) return;
  // 配置项映射
  const configMap = {
    projectAccounting: [
      { rule: "voucher_status", key: "paymentStatus" },
      { rule: "payment_type", key: "paymentType" },
    ],
    contract: [
      { rule: "contract_status", key: "contractStatus" },
      { rule: "start_method", key: "initiatorType" },
      { rule: "seal_type", key: "sealType" },
    ],
    bidding: [
      { rule: "seal_type", key: "sealType" },
      { rule: "bidding_method", key: "biddingMethod" },
    ],
    startNapeList: [
      { rule: "bidding_method", key: "biddingMethod" }
    ]
  };
  // 通过数字字典获取选项
  if (["projectAccounting", "contract", "bidding", 'startNapeList'].includes(pageType.value)) {
    const configArr = configMap[pageType.value];
    configArr.forEach(({ rule, key }) => {
      getPayMethodOption({ ruleName: rule }).then((res) => {
        optionInfo.value[key] = (res.data || []).map((item) => ({
          value: item.dictLabel,
          label: item.dictLabel,
        }));
      });
    });
    if (pageType.value === "startNapeList") {
      getTreeselectFun();
      getUserInfoFun();
    }
  }
}

//请求审批进度数据
function approval (url,approveId=rowData.approveId){
  approvalProcess({ approveId }, url)
      .then((res) => {
        // if (res.data) Object.assign(form.value, res.data);
        proessForm.value = res.data;
        form.value.progressVoList = res.data.progressVoList || [];
        getStatusImg();
      })
      .catch((err) => {
        console.error("获取审批流程失败:", err);
      });
}
//请求详情数据
function getDetailListUrl(url) {
  if (!rowData) return;
  if (url == "selectBiddingAcquisition" && rowData.approveId && isDetails.value==1 && !isSubmitFlag()) {
    //审批请求
    approval("selectApprovalProcessBidding")
  }
  if (url == "selectContract" && rowData.approveId && isDetails.value==1 && !isSubmitFlag()) {
    approval("selectApprovalProcessContract")
  }

  if (rowData.id) {
    getDetailList(url, rowData.id)
      .then((res) => {
            if (res.data) {
            const formatted = formatAllDates(res.data);
            // 转换 isSeal 字段：boolean -> string
            if (formatted.isSeal !== undefined || formatted.isSeal !== null) {
              formatted.isSeal = formatted.isSeal ? "是" : "否";
            }
            if (isDetails.value == 1) {
              // 详情模式，全部赋值
              Object.assign(form.value, formatted);
              //判断是否是新增详情模式，是 获取的审批数据需要从详情里的approveId请求最新的数据
              if(isSubmitFlag() && url == "selectBiddingAcquisition"){
                approval("selectApprovalProcessBidding",res.data.approveId)
              }
              if(isSubmitFlag() && url == "selectContract"){
                approval("selectApprovalProcessContract",res.data.approveId)
              }
            } 
          if (formatted.historyApplyList) {
            if (url == "selectMeetingDecision") {
              getDict({ ruleName: 'decision_result' })
            }
            dictData(formatted.historyApplyList)
            dataList.value = Array.isArray(formatted.historyApplyList)
              ? formatted.historyApplyList
              : [formatted.historyApplyList];
          }
        }
      })
      .catch((err) => {
        console.error("获取失败:", err);
      });
  }

}
// 处理详情页数据
function DetailData(rowData) {
  form.value.isDetails = isDetails.value;
  if (isDetails.value == 1) {
    if (pageType.value == "projectAccounting") {
      Object.assign(form.value, rowData);
      Promise.allSettled([
        selectInitiationFileById({ id: rowData.hsId, fileCode: 4, fileType: 2 }),
        selectHsListProjectsAccounting({ projectId: rowData.projectId }),
      ]).then(([fileRes, listRes]) => {
        // 查询建账附件
        if (fileRes.status == "fulfilled" && fileRes.value?.data) {
          form.value.projProposalList = fileRes.value.data || [];
        }
        // 获取项目建账-列表查询
        if (listRes.status == "fulfilled" && listRes.value?.rows) {
          dataList.value = listRes.value.rows;
        }
        if (rowData.approveId) {
          getAccountingApprovalProcess({ approveId: rowData.approveId })
            .then((res) => {
              if (res.data) Object.assign(form.value, res.data);
              getStatusImg();
            })
            .catch((err) => {
              console.error("获取审批流程失败:", err);
            });
        }
      });
    }
  }
  if (["startNapeList", "schemeApproval"].includes(pageType.value)) {
    initDetailData(rowData);
  } else if (pageType.value == "conference") {
    getDetailListUrl("selectMeetingDecision", rowData.id)
  } else if (pageType.value == "bidding") {
    getDetailListUrl("selectBiddingAcquisition", rowData.id)
    Object.assign(form.value, rowData);
  } else if (pageType.value == "bidding") {
    Object.assign(form.value, rowData);
  } else if (pageType.value == "contract") {
    getDetailListUrl("selectContract", rowData.id)
  }
}

// 审批进度卡片

const approvalStateMap = {
  0: "待审核",
  1: "审核中",
  2: "已通过",
  3: "未通过",
};

const statusMap = {
  0: "waitingForApproval",    
  1: "approving",   
  2: "approved",        
  3: "notApproved",        
  4: "revoked",           
};

const statusImgMap = {
  waitingForSubmission,
  waitingForApproval,
  approving,
  approved,
  notApproved,
  revoked,
};

// 获取流程图片
let statusImg = ref("");

function getStatusImg() {  let key="waitingForSubmission";
  // 会议、竞价、合同，走 proessForm + examineStateEnum，默认 toBeSubmitted
  if (['conference', 'bidding', 'contract'].includes(pageType.value)) {
    const approveStart = proessForm.value.examineState;
    if (approveStart !== undefined && approveStart !== null) {
      key = statusMap[approveStart] || "waitingForSubmission";
    }
    statusImg.value = statusImgMap[key] || statusImgMap["waitingForSubmission"];
    return;
  }
  // 其他情况，走 form + statusMap，默认 waitingForApproval
  key = "waitingForSubmission";
  if (form.value.approveId) {
    const approveStart = Number(form.value.examineState);
    key = statusMap[approveStart] || "waitingForSubmission";
  }
  statusImg.value = statusImgMap[key] ;
}


// 处理文件查看
function handleCheckFile(row, prop) {

  if (pageType.value === 'contract') {
    getCheckFileContract({id:row.id}).then((res) => {
      if (res.code == 200 && res.data.length > 0) {
        checkFileRef.value.openDialog(res.data);
      } else {
        proxy.$modal.msgWarning("未上传文件");
      }
    });
    return;
  }
  if (pageType.value === 'bidding') {
    getCheckFileBidding(row.id).then((res) => {
      if (res.code == 200 && res.data.length > 0) {
        checkFileRef.value.openDialog(res.data);
      } else {
        proxy.$modal.msgWarning("未上传文件");
      }
    });
    return;
  }
  if (pageType.value === 'conference') {
    getCheckFile(row.id).then((res) => {
      if (res.code == 200 && res.data.length > 0) {
        checkFileRef.value.openDialog(res.data);
      } else {
        proxy.$modal.msgWarning("未上传文件");
      }
    });
    return;
  }
  const query = {
    id: row.id,
    fileCode: 4,
    fileType: 2,
  };
  selectInitiationFileById(query).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      proxy.$refs["checkFileDialogRef"].openDialog(res.data);
    } else {
      proxy.$modal.msgWarning("未上传文件");
    }
  });
}

//项目进展点击切换
function gotoStartNapeList(title) {
  const pageConfig = {
    立项: {
      currentPage: "schemeApproval",
      targetPath: "/dueDiligence/startNapeList",
      targetType: "startNapeList",
      status: 0
    },
    项目方案审批: {
      currentPage: "startNapeList",
      targetPath: "/dueDiligence/schemeApproval",
      targetType: "schemeApproval",
      status: 1
    }
  };

  const config = pageConfig[title];
  if (config && pageType.value === config.currentPage) {
    const query = {
      path: config.targetPath,
      pageType: config.targetType,
      progressStatus: config.status,
      isDetails: 1,
      rowData: route.query.rowData,
    };
    const obj = { path: "/dueDiligence/projectInfo", query };
    proxy.$tab.closeOpenPage(obj);
  }
}
//临时方案 没有找到为什么startNapeList进入页面会立即触发校验规则原因
setTimeout(()=>{
  proxy.$refs.formRef && proxy.$refs.formRef.resetFields();
},0)

//获取决策结果字典数据
getDict({ ruleName: "decision_result" })
  .then((res) => {
    optionInfo.value.decisionResult = res.data.map((item) => ({
      label: item.dictLabel,
      value: item.dictLabel,
    }));
  })
  .catch((err) => {
    console.error("获取决策结果字典数据失败", err);
  });

const approveStateMap = {
  0: "待方案审批",
  1: "方案审批中",
  2: "方案审批成功",
  3: "方案审批失败",
  4: "已撤销",
  5: "已作废",
  6: "已退案关闭",
};

// 打开附件
function openFile(url) {
  window.open(url, '_blank');
}

const backOrCancelText = computed(() => {
  let text = '返回'
  if(isDetails.value == 0 ){
      if(pageType.value === 'projectAccounting'){
        text = '取消'
      }
  }
  return text
});

const isEdit = ref(false);

// 编辑按钮逻辑：允许编辑当前内容
function enableEdit() {
  isEdit.value = true;
  isDetails.value = 0;
  getOptionList();
  // 1. 文件字段处理
  const uploadFields = (fileInfo.value[pageType.value] || []).map(item => item.prop);
  uploadFields.forEach(key => {
    let files = form.value[key];
    if (!Array.isArray(files)) {
      files = [];
    }
    form.value[key] = files.map(f => {
      if (f.url || f.response) return f;
      return {
        ...f,
        name: f.firstName || f.name || f.originalFilename || '',
        url: f.fileUrl || f.url || '',
        // 可加其他字段
      };
    });
  });
}

// 使用fetch下载文件
function handleDownload(url) {
  const fileName = String(url).substring(url.lastIndexOf("/") + 1);
  fetch(url)
    .then((res) => {
      if (!res.ok) {
        throw new Error("Network response was not ok");
      }
      return res.blob();
    })
    .then((blob) => {
      const link = document.createElement("a");
      const blobUrl = window.URL.createObjectURL(blob);
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(blobUrl);
      document.body.removeChild(link);
    })
    .catch((error) => {
      console.error("Download error:", error);
      proxy.$modal.msgError("下载失败，请稍后重试");
    });
}


function isImageFile(fileName) {
  if (!fileName) return false;
  const imgExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp'];
  return imgExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
}

function onBeforeRemoveFile(file) {

}

</script>

<style lang="scss" scoped>
.status-img {
  height: 100px;
  margin-left: 10px;
}

.title-area {
  display: flex;
  justify-content: space-between;
}

.content-list {
  font-size: 14px;
  font-weight: bold;

  .content-item {
    margin-bottom: 10px;

    &:last-of-type {
      margin-bottom: 20px;
    }
  }
}

.file-list {
  .file-item {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .file-item-name {
      color: #409eff;
      padding: 5px 10px;
      border: 1px solid #666;
    }

    .el-button {
      margin-left: 15px;
    }
  }
}

.title {
  font-size: 20px;
  color: #000;
  font-weight: bold;
  margin-bottom: 10px;
}

.progress-list {
  display: flex;
  width: 90%;
  margin: 20px auto 0;
  border-radius: 20px;
  overflow: hidden;
  background-color: #f2f2f2;

  &>div {
    flex: 1;
    height: 38px;
    color: #409eff;
    line-height: 38px;
    text-align: center;
  }

  .active {
    border-radius: 20px;
    color: #ffff !important;
    background-color: #409eff !important;
  }
}

.sub-title-list {
  display: flex;
}

.sub-title-item {
  color: #000;
  font-size: 14px;
  font-weight: bold;
  margin-right: 20px;
  margin-bottom: 10px;
}

.pay-info {
  display: flex;
  gap: 20px;

  .el-card {
    flex: 1;
  }

  .right-card {
    flex: none;
    width: 280px;
  }
}

.timeline-title {
  font-weight: bold;
}

.timeline-content {
  font-size: 12px;
  color: #999;
}

.file-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;

  .upload-component {
    width: 100%;
    margin-bottom: 12px;
  }

  .upload-tips {
    width: 100%;

    .report-tig {
      display: block;
      font-size: 12px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 4px;
      text-align: left;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.table-select-all-disabled .el-table__header .el-checkbox {
  pointer-events: none;
  opacity: 0.5;
}

.word-break {
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}


.label {
  white-space: nowrap;      /* 不换行 */
  flex-shrink: 0;           /* 不被压缩 */
}

.flex-content {
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  flex: 1;               
  min-width: 0;        
}
</style>
