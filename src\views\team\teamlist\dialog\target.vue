<template>
  <el-dialog title="设置月度目标" v-model="open" width="600px" append-to-body>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="92px">
      <el-form-item label="月回款目标" prop="collectionTargets">
        <el-input
          v-model="form.collectionTargets"
          placeholder="请输入月回款目标"
          style="width: 240px"
        />
        元
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateTargets } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const open = ref(false);
const subloading = ref(false);
const data = reactive({
  form: {},
  rules: {
    collectionTargets: [
      {
        required: true,
        pattern: /^([1-9]\d*|0)(\.\d{1,2})?$/,
        message: "请输入正确金额，最多保留两位小数！",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const { form, rules } = toRefs(data);
const getList = inject("getList", Function, true);
//重置表单
function reset() {
  form.value = {
    collectionTargets: undefined,
  };
  proxy.resetForm("targetRef");
}

//目标设置
function openSetTarget(obj) {
  form.value = obj;
  open.value = true;
}

//提交表单
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      updateTargets(form.value)
        .then((res) => {
          let { code, msg } = res;
          if (code === 200) {
            proxy.$modal.msgSuccess(msg);
            cancel();
            subloading.value = false;
            getList();
          }
        })
        .catch((err) => {
          subloading.value = false;
        });
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  openSetTarget,
});
</script>

<style scoped></style>
