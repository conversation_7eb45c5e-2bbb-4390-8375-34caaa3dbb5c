<template>
  <div class="strategy-details">
    <div class="title">策略属性</div>
    <div class="details-box">
      <el-row :gutter="24" v-if="['1','2','3'].includes(route.params.id)">
        <el-col :span="8" v-for="(item, index) in detailsList" :key="index">
          <div class="details-span mt10">
            <div class="label">{{ item.label }}</div>
            <div class="text mt10">{{ item.text }}</div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-if="['4','5'].includes(route.params.id)">
        <el-col :span="8" v-for="(item, index) in ruleseList" :key="index">
          <div class="details-span mt10">
            <div class="label">{{ item.label }}</div>
            <div class="text mt10">{{ item.text }}</div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-if="['6'].includes(route.params.id)">
        <el-col :span="8" v-for="(item, index) in deteleList" :key="index">
          <div class="details-span mt10">
            <div class="label">{{ item.label }}</div>
            <div class="text mt10">{{ item.text }}</div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-if="['7'].includes(route.params.id)">
        <el-col :span="8" v-for="(item, index) in assestList" :key="index">
          <div class="details-span mt10">
            <div class="label">{{ item.label }}</div>
            <div class="text mt10">{{ item.text }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="title mt40 mb20" v-if="route.params.id <= 3">自动分案</div>
    <div class="details-box" v-if="route.params.id <= 3">
      <el-row :gutter="24">
        <el-form :model="form" ref="formRef" style="margin-left:-10px" label-width="80px">
          <el-form-item label="分案时间" prop="sendTime">
            <el-date-picker
              v-model="form.sendTime"
              clearable
              type="datetime"
              filterable
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 360px"
              placeholder="请选择分案时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitTime">确定</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
  </div>
</template>

<script setup name="strategyDetails">
const route = useRoute();
const { proxy } = getCurrentInstance();
const detailsList = ref([
  { label: "策略名称:", text: "V16.0" },
  { label: "状态:", text: "测试分案" },
  { label: "类别:", text: "分案逻辑" },
  { label: "策略说明:", text: "测试分案" },
  { label: "策略详情:", text: "--" },
]);
const ruleseList = ref([
  { label: "策略名称:", text: "V16.0" },
  { label: "状态:", text: "待上线" },
  { label: "类别:", text: "减免审批" },
  { label: "策略说明:", text: "减免审批" },
  { label: "策略详情:", text: "--" },
]);
const deteleList = ref([
  { label: "策略名称:", text: "V16.0" },
  { label: "状态:", text: "待上线" },
  { label: "类别:", text: "冲减规则" },
  { label: "策略说明:", text: "冲减规则" },
  { label: "策略详情:", text: "--" },
]);
const assestList = ref([
  { label: "策略名称:", text: "V16.0" },
  { label: "状态:", text: "待上线" },
  { label: "类别:", text: "资产评估" },
  { label: "策略说明:", text: "资产评估" },
  { label: "策略详情:", text: "--" },
]);
const form = ref({
    sendTime:"2024-03-01 00:00:00"
})

//提交设置时间
function submitTime(){
    proxy.$modal.msgSuccess("操作成功！");
    form.value.sendTime = undefined
}
</script>

<style lang="scss" scoped>
.strategy-details {
  border-radius: 6px;
  border: 1px solid #f3f3f3;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  .title {
    line-height: 20px;
    border-left: 4px solid #409eff;
    padding-left: 10px;
  }
  .details-box {
    .details-span {
      height: 69px;
      padding: 14px 12px;
      box-sizing: border-box;
      background: linear-gradient(270deg, #fdfeff, #fafcfe);
      border-radius: 4px;
      border: 1px solid #f1f5f9;
      .label {
        font-size: 14px;
        color: #333;
        font-weight: 400;
        line-height: 18px;
      }
      .text {
        color: #626262;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }
}
</style>
