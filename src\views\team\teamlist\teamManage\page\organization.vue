<template>
  <div v-if="isshowhint">该团体暂未设置组织架构，请提醒机构设置！</div>
  <el-row v-else :gutter="20">
    <el-col :span="5" :xs="24">
      <el-tree
        :data="deptOptions"
        :props="{ label: 'name', children: 'children' }"
        :expand-on-click-node="false"
        ref="deptTreeRef"
        default-expand-all
        @node-click="handleNodeClick"
      >
        <template #default="{ node }">
          <el-tooltip effect="light" :content="node.label" placement="top-start">
            <span class="el-tree-node__label">{{ node.label }}</span>
          </el-tooltip>
        </template>
      </el-tree>
    </el-col>
    <el-col :span="19" :xs="24">
      <el-form :model="queryParams" :inline="true" ref="queryRef">
        <el-input v-show="false" />
        <el-form-item prop="value">
          <el-input
            v-model="queryParams.value"
            placeholder="请输入搜索关键词"
            @keyup.enter="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="dataList" v-loading="loading">
        <el-table-column label="姓名" prop="employeeName" align="center" />
        <el-table-column label="部门" prop="departments" align="center" />
        <el-table-column label="职务" prop="theRole" align="center" />
        <el-table-column
          label="账号状态"
          prop="accountStatus"
          :formatter="accountStatusChange"
          align="center"
        />
        <el-table-column label="手机" prop="phoneNumber" align="center" />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-col>
  </el-row>
</template>

<script setup>
import { selectDeptTreeType, selectEmployees } from "@/api/team/teamManage";

const route = useRoute();
const { proxy } = getCurrentInstance();
const isshowhint = ref(false);
const deptOptions = ref([]);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createId: route.params.teamId,
    value: undefined,
    departmentId: undefined,
  },
});
const { queryParams } = toRefs(data);

//获取列表
function getList() {
  loading.value = true;
  selectEmployees(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//获取组织架构
function getDeptTree() {
  selectDeptTreeType(route.params.teamId).then((res) => {
    deptOptions.value = res.data;
    if (deptOptions.value[0].children && deptOptions.value[0].children.length > 0) {
      isshowhint.value = false;
    } else {
      isshowhint.value = true;
    }
  });
}
getDeptTree();

/** 节点单击事件 */
function handleNodeClick(data) {
  let arr = data.id.split(":");
  if (arr[0] === "Dept") {
    queryParams.value.departmentId = arr[1];
  } else {
    queryParams.value.departmentId = undefined;
  }
  getList();
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    createId: route.params.teamId,
    value: undefined,
    departmentId: queryParams.value.departmentId || undefined,
  };
  getList();
}

//账号状态 0:启用,1:禁用
function accountStatusChange(row) {
  return ["启用", "禁用"][row.accountStatus] || "--";
}
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__label) {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
