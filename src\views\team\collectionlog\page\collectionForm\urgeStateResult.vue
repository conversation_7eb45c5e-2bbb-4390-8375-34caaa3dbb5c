<template>
  <div class="warp">
    <div class="title">催收状态分布-联络结果</div>
    <el-table v-loading="loading" border :data="dataList">
      <el-table-column align="center">
        <el-table-column label="催收状态" prop="followUpState" align="center" />
      </el-table-column>
      <el-table-column label="L" align="center">
        <el-table-column label="关机" prop="closeDown" align="center" />
        <el-table-column label="号码转让" prop="numberTransfer" align="center" />
        <el-table-column label="空号" prop="emptyNumber" align="center" />
        <el-table-column label="停机" prop="shutDown" align="center" />
        <el-table-column label="无法接通" prop="notAvailable" align="center" />
        <el-table-column label="小计" prop="subtotalL" align="center" />
      </el-table-column>
      <el-table-column label="M" align="center">
        <el-table-column label="对方挂断" prop="otherPartyHangUp" align="center" />
        <el-table-column label="无此人" prop="noSuchPerson" align="center" />
        <el-table-column label="通话中" prop="calling" align="center" />
        <el-table-column label="正忙" prop="phoneIsBusy" align="center" />
        <el-table-column label="无人接听" prop="noOneHeard" align="center" />
        <el-table-column label="小计" prop="subtotalM" align="center" />
      </el-table-column>
      <el-table-column label="H" align="center">
        <el-table-column label="已转告" prop="convey" align="center" />
        <el-table-column label="正常接听" prop="answerNormally" align="center" />
        <el-table-column label="小计" prop="subtotalH" align="center" />
      </el-table-column>
      <el-table-column align="center">
        <el-table-column label="总计" prop="total" align="center" />
      </el-table-column>
    </el-table>
    <div class="echartDiv">
      <div id="resultOne"></div>
      <div id="resultTwo"></div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { urgeStateDistributionContact } from "@/api/team/collectionlog";
const { proxy } = getCurrentInstance();
const props = defineProps({
  queryParams: {
    type: Object,
  },
});

const loading = ref(false);
const dataList = ref([]);
const histogramData1 = ref({});
const histogramData2 = ref({});
const optionsOne = ref({
  title: {
    text: "催收状态--联络结果-1",
    x: "center",
    textStyle: {
      textAlign: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999",
      },
    },
  },
  toolbox: {
    feature: {
      magicType: { show: true, type: ["line", "bar"] },
      restore: { show: true },
      saveAsImage: { show: true },
    },
    padding: [0, 20, 0, 0],
  },
  legend: {
    orient: "vertical",
    left: "right",
    top: "center",
    align: "left",
    padding: [0, 40, 0, 0],
    data: [],
  },
  grid: {
    left: "3%",
    right: "30%",
    bottom: "90",
    containLabel: true,
  },
  xAxis: [
    {
      type: "category",
      data: [],
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        interval: 0,
        rotate: -30,
      },
    },
  ],
  yAxis: [
    {
      type: "value",
    },
  ],
  series: [],
});

const optionsTwo = ref({
  title: {
    text: "催收状态--联络结果-2",
    x: "center",
    textStyle: {
      textAlign: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999",
      },
    },
  },
  toolbox: {
    feature: {
      magicType: { show: true, type: ["line", "bar"] },
      restore: { show: true },
      saveAsImage: { show: true },
    },
    padding: [0, 20, 0, 0],
  },
  legend: {
    orient: "vertical",
    left: "right",
    top: "center",
    align: "left",
    padding: [0, 40, 0, 0],
    data: [],
  },
  grid: {
    left: "3%",
    right: "30%",
    bottom: "90",
    containLabel: true,
  },
  xAxis: [
    {
      type: "category",
      data: [],
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        interval: 0,
        rotate: -30,
      },
    },
  ],
  yAxis: [
    {
      type: "value",
    },
  ],
  series: [],
});
watch(
  () => props.queryParams,
  (newVal) => {
    geturgeStateDistributionContact(newVal);
  }
);

watch(
  () => histogramData1.value,
  (newval) => {
    let series = [];
    let legend = [];
    for (let i = 0; i < newval.length; i++) {
      const element = newval[i];
      if (element.label == "name") {
        optionsOne.value.xAxis[0].data = element.value;
      } else {
        legend.push(element.label);
        series.push({
          name: element.label,
          type: "bar",
          stack: "wtf",
          emphasis: {
            focus: "series",
          },
          data: element.value || [],
        });
      }
    }
    optionsOne.value.series = series;
    optionsOne.value.legend.data = legend;
    nextTick(() => {
      ecResultOneInit();
    });
  }
);

watch(
  () => histogramData2.value,
  (newval) => {
    let series = [];
    let legend = [];
    for (let i = 0; i < newval.length; i++) {
      const element = newval[i];
      if (element.label == "followUpState") {
        optionsTwo.value.xAxis[0].data = element.value;
      } else {
        legend.push(element.label);
        series.push({
          name: element.label,
          type: "bar",
          stack: "wtf",
          emphasis: {
            focus: "series",
          },
          data: element.value || [],
        });
      }
    }
    optionsTwo.value.series = series;
    optionsTwo.value.legend.data = legend;
    nextTick(() => {
      ecResultTwoInit();
    });
  }
);

//初始化echarts
function ecResultOneInit() {
  var chartDom = document.getElementById("resultOne");
  var myChart = echarts.init(chartDom);
  myChart.setOption(optionsOne.value);
  window.onresize = function () {
    myChart.resize();
  };
}

function ecResultTwoInit() {
  var chartDom = document.getElementById("resultTwo");
  var myChart = echarts.init(chartDom);
  myChart.setOption(optionsTwo.value);
  window.onresize = function () {
    myChart.resize();
  };
}

onMounted(() => {
  geturgeStateDistributionContact({});
});

//催收状态分布--联络结果
function geturgeStateDistributionContact(query) {
  loading.value = true;
  urgeStateDistributionContact(query)
    .then((res) => {
      dataList.value = res.data.tableData || [];
      histogramData1.value = res.data.histogramData1 || [];
      histogramData2.value = res.data.histogramData2 || [];
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style lang="scss" scoped>
.echartDiv {
  margin-top: 20px;
  div {
    display: inline-block;
    width: 50%;
    height: 400px;
    margin-top: 20;
  }
}
</style>
