<template>
  <div style="height: calc(100vh - 203px); overflow: auto">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
      <el-form-item label="催收员" prop="odvIds">
        <el-select
          v-model="queryParams.odvIds"
          placeholder="请选择催收员"
          @visible-change="getOdvOptions"
          :loading="selectloading"
          multiple
          collapse-tags
          filterable
          :reserve-keyword="false"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in odvOptions"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.time"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="催收状态" prop="urgeState">
        <el-select
          v-model="queryParams.urgeState"
          placeholder="请输入或选择催收状态"
          clearable
          filterable
          :reserve-keyword="false"
          :loading="selectloading"
          @visible-change="UrgeState"
          style="width: 240px"
        >
          <el-option
            v-for="item in backs"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
      <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
    </div>
    <!-- 每日联系情况 -->
    <dailyInfoVue class="mb20" :queryParams="query" />

    <!-- 催收状态分布--委外机构 -->
    <urgeStateTeam class="mb20" :queryParams="query" />

    <!-- 催收状态分布-联络结果 -->
    <urgeStateResult :queryParams="query" />
  </div>
</template>

<script setup>
import dailyInfoVue from "./collectionForm/dailyInfo.vue";
import urgeStateTeam from "./collectionForm/urgeStateTeam.vue";
import urgeStateResult from "./collectionForm/urgeStateResult.vue";
import { getUrgeState } from "@/api/assets/casemanage.js";
import { getOdvOption } from "@/api/team/teamlist/operate";

const { proxy } = getCurrentInstance();
const route = useRoute();
const data = reactive({
  queryParams: {
    outsourcingTeamId: route.params.teamId,
    odvIds: [],
    time: [],
    urgeState: undefined,
  },
});
const { queryParams } = toRefs(data);
const rangFileds = ["time"];

const selectloading = ref(false);
const odvOptions = ref([]);
const backs = ref([]);

const query = ref({
  outsourcingTeamId: route.params.teamId,
});

//获取机构名称
function getOdvOptions(val) {
  if (!val) return;
  selectloading.value = true;
  getOdvOption({ teamId: route.params.teamId })
    .then((res) => {
      odvOptions.value = res.data;
    })
    .finally(() => {
      selectloading.value = false;
    });
}

//催收状态
function UrgeState(val) {
  if (!val) return;
  selectloading.value = true;
  getUrgeState()
    .then((res) => {
      backs.value = res.data;
    })
    .finally(() => {
      selectloading.value = false;
    });
}

//搜索
function handleQuery() {
  let req = proxy.addFieldsRange(queryParams.value, rangFileds);
  if (req.odvIds) {
    req.odvIds = String(req.odvIds);
  }
  query.value = req;
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    outsourcingTeamId: route.params.teamId,
    time: [],
    urgeState: undefined,
  };
  query.value = proxy.addFieldsRange(queryParams.value, rangFileds);
}
</script>

<style lang="scss" scoped>
:deep(.warp .title) {
  margin: 20px 0 10px;
  color: #888888;
  background-color: #f9f9f9;
  line-height: 38px;
  padding-left: 20px;
}
</style>
