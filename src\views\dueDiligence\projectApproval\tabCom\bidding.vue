<template>
  <div class="mt20">
    <el-form inline label-width="auto" :model="approveData">
      <el-form-item label="项目ID">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectId"
          :options="projectIdList"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectName"
          :options="projectNameList"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item label="产品类型">
        <el-select
          v-model="approveData.productId"
          style="width: 320px"
          placeholder="请选择产品类型"
        >
          <el-option
            v-for="item in ductTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="竞价状态">
        <el-input
          style="width: 320px"
          placeholder="请输入竞价状态"
          v-model="approveData.biddingStatus"
        />
      </el-form-item>
      <el-form-item label="资产转让方">
        <el-select
          v-model="approveData.transferorId"
          style="width: 320px"
          placeholder="请选择资产转让方"
        >
          <el-option
            v-for="item in transferorList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="处理人">
        <el-input
          style="width: 320px"
          placeholder="请输入处理人"
          v-model="approveData.processor"
        />
      </el-form-item> -->
      <!-- <el-form-item label="处理时间">
        <el-date-picker
          v-model="approveData.createTime"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item> -->
      <el-form-item label="申请人">
        <MultiSelect 
          style="width: 320px"
           v-model="queryParmas.applicantIdList"
          placeholder="请选择申请人"
          :options="createByList"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="approveData.createTime"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="投标方式">
        <el-select
          v-model="approveData.biddingMethod"
          style="width: 320px"
          placeholder="请选择投标方式"
        >
          <el-option
            v-for="item in tenderTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="印章类型">
        <el-input
          style="width: 320px"
          placeholder="请输入印章类型"
          v-model="approveData.stampType"
        />
      </el-form-item>
      <el-form-item label="是否用印">
        <el-input
          style="width: 320px"
          placeholder="请输入是否用印"
          v-model="approveData.needStamp"
        />
      </el-form-item>
      <el-form-item label="报价金额">
        <div class="range-scope" style="width: 320px">
          <el-input v-model="approveData.amountMin" />
          <span>-</span>
          <el-input v-model="approveData.amountMax" />
        </div>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button @click="handleQuery" plain type="primary">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </div>
    <el-row class="mt20">
      <el-button
        type="primary"
        :disabled="!selectedArr.length"
        @click="handle(null, 0)"
        >通过</el-button
      >
      <el-button
        type="warning"
        :disabled="!selectedArr.length"
        @click="handle(null, 1)"
        >不通过</el-button
      >
    </el-row>
    <SelectedAll
      ref="selectedAllRef"
      :dataList="dataList"
      :selectedArr="selectedArr"
      v-model:allQuery="allQuery"
    />
    <el-tabs v-model="activetab" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.code"
        :label="item.info"
        :name="item.code"
      />
    </el-tabs>
    <el-table
      ref="multipleTableRef"
      :data="dataList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="44px"
        :selectable="selectable"
        align="right"
      />
      <el-table-column
        label="项目ID"
        prop="projectId"
        width="120"
        align="center"
      />
      <el-table-column
        label="项目名称"
        prop="projectName"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="资产转让方"
        prop="transferor"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="产品类型"
        prop="productType"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="竞价状态"
        prop="biddingStatus"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="流程标题"
        prop="title"
        min-width="120"
        align="center"
      />
      <el-table-column
        label="投标方式"
        prop="biddingMethod"
        width="120"
        align="center"
      />
      <el-table-column
        label="报价金额（元）"
        prop="price"
        width="120"
        align="center"
      />
      <el-table-column
        label="是否用印"
        prop="isSeal"
        width="120"
        align="center"
      >
      <template #default="{ row }">
        {{ row.isSeal ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        label="印章类型"
        prop="sealType"
        width="120"
        align="center"
      />
      <el-table-column
        label="申请人"
        prop="createBy"
        width="120"
        align="center"
      />
      <el-table-column
        label="申请时间"
        prop="applyDate"
        width="120"
        align="center"
        >
      <template #default="{ row }">
        {{ formatTime(row.applyDate) }}
        </template>
      </el-table-column>
      <el-table-column
        label="处理状态"
        prop="approveStatus"
        width="120"
        align="center"
      />
      <el-table-column
        label="处理人"
        prop="reviewer"
        width="120"
        align="center"
      />
      <el-table-column
        label="处理时间"
        prop="reviewTime"
        width="120"
        align="center"
        >      
        <template #default="{ row }">
        {{ formatTime(row.reviewTime) }}
        </template>
        </el-table-column>
      <el-table-column width="180" fixed="right" label="操作" align="center">
        <template #default="{ row }">
          <el-button v-if="row.approveStatus == '待处理'" type="text" @click="handle(row, 0)"
            >通过</el-button>
          <el-button v-if="row.approveStatus == '待处理'" type="text" @click="handle(row, 1)"
            >不通过</el-button
          >
          <el-button type="text" @click="handleDetails(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParmas.pageNum"
      v-model:limit="queryParmas.pageSize"
      @pagination="getList"
    />

    <Pass ref="passRef" @submit="handleSubmit" />
  </div>
</template>

<script setup>
import Pass from "../dialog/pass.vue";
import { zcQueryApproveList } from "@/api/common";
import { fillEmptyToDash } from "@/api/conference/utils"
import { getProjectId, getProjectName } from "@/api/dueDiligence/startNapeList";
import ProjectApproval from "../index.js";
import {
  getProductType,
  getTenderType,
  getUserInfo
} from "@/api/dueDiligence/startNapeList";
import { assetOwnerTree } from "@/api/assets/assetside";
import { formatTime } from "@/utils/common";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();


// 创建ProjectApproval实例
const projectApproval = new ProjectApproval();

// 响应式数据，用于模板绑定
const activetab = ref(projectApproval.activetab);
const total = ref(projectApproval.total);
const queryParmas = ref({
  approveCode: "bidding",
  ...projectApproval.queryParmas,
});
const approveData = ref(projectApproval.approveData);
const dataList = ref(projectApproval.dataList);
const tabList = ref(projectApproval.tabList);
const selectedArr = ref([]);
const allQuery = ref(false);
const passRef = ref(null);
const ductTypeList = ref([]); // 产品类型
const transferorList = ref([]); // 资产转让方
const tenderTypeList = ref([]); // 投标方式
const projectIdList = ref([]);
const projectNameList = ref([]);
const createByList = ref([]);//申请人

const loading = ref(false);
const propsList = [
  'projectId',
  'projectName',
  'transferor',
  'productType',
  'biddingStatus',
  'title',
  'biddingMethod',
  'price',
  'isSeal',
  'sealType',
  'createBy',
  'applyDate',
  'approveStatus',
  'reviewer',
  'reviewTime'
];


// 设置审批代码
projectApproval.queryParmas.approveCode = "bidding";

// 模拟数据，可以删除或保留
const mockDataList = ref([]);


const handleQuery = () => {
  projectApproval.queryParmas.pageNum = 1;
  getList();
};

const handle = (row = null, type) => {
  let req = {
    approveCode: "bidding",
    allQuery: false,
  };
  if (row !== null) {
    req.approveIds = [row.approveId];
    passRef.value.opendialog(req, type);
  } else {
    if (allQuery.value) {
      req.allQuery = true;
      req.approvePageRequest = JSON.parse(JSON.stringify(approveData.value));
    } else {
      req.approveIds = selectedArr.value.map((item) => item.approveId);
    }
    passRef.value.opendialog(req, type);
  }
};

const handleSubmit = (req) => {
  if (req.type == 0) {
    projectApproval.handleApprove(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        getList();
        passRef.value.close();
      }
    });
  } else {
    projectApproval.handleReject(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        getList();
        passRef.value.close();
      }
    });
  }
};

function handleDetails(row) {
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}
  const query = {
    path: route.path,
    pageType: "bidding",
    progressStatus: 2,
    isDetails: 1,
    activeTab: "1",
    rowData: JSON.stringify(rowData),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

const selectable = (row) => {
  return projectApproval.selectable(row);
};

const resetQuery = () => {
  loading.value = true;
  projectApproval.resetQuery(({ dataList: newDataList, total: newTotal }) => {
    dataList.value = fillEmptyToDash(newDataList,propsList);
    total.value = newTotal;
    // 重置响应式数据
    queryParmas.value = projectApproval.queryParmas;
    approveData.value = projectApproval.approveData;
    loading.value = false;
  });
};

const getList = () => {
  projectApproval.queryParmas.approveCode = "bidding";
  loading.value = true;
  projectApproval
    .getList(({ dataList: newDataList, total: newTotal }) => {
      dataList.value = fillEmptyToDash(newDataList,propsList);
      total.value = newTotal;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 标签页切换处理
const handleTabChange = (tabName) => {
  loading.value = true;
  projectApproval.changeTab(
    tabName,
    ({ dataList: newDataList, total: newTotal }) => {
      dataList.value = fillEmptyToDash(newDataList,propsList);
      activetab.value = tabName;
      loading.value = false;
    }
  );
};

const handleSelectionChange = (val) => {
  selectedArr.value = val;
};

// 同步响应式数据到类实例
const syncDataToInstance = () => {
  projectApproval.updateApproveData(approveData.value);
  projectApproval.updateQueryParams(queryParmas.value);
};

// 监听数据变化，同步到类实例
watch(
  approveData,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

watch(
  queryParmas,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

onMounted(() => {
  // 初始化时使用模拟数据，实际使用时可以调用getList()
  dataList.value = mockDataList.value;
  total.value = mockDataList.value.length;
  getProductTypeFun();
  getTenderTypeFun();
  getTreeselectFun();
  getProjectIdFun();
  getProjectNameFun();
  getUserInfoFun()
  getList();
});

function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      projectIdList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      projectNameList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

function getProductTypeFun() {
  getProductType().then((res) => {
    ductTypeList.value = res.data.map((item) => ({
      value: item.code,
      label: item.info,
    }));
  });
}

function getTenderTypeFun() {
  getTenderType()
    .then((res) => {
      tenderTypeList.value = res.data.map((item) => ({
        value: item.dictLabel || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}

function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      transferorList.value = res.data.map((item) => ({
        value: item.id.split(":")[1],
        label: item.label,
      }));
    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}

// 获取申请人下拉数据
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("接口返回数据格式错误:", res.data);
        return;
      }
      console.log("获取用户信息成功:", res.data);
      const userOptions = res.data.map((item) => ({
        value: item.code,
        label: item.info,
      }));
      createByList.value = userOptions;
    })
    .catch((error) => {
      console.error("获取用户信息失败:", error);
    });
}

</script>

<style lang="scss" scoped>
.range-scope {
  display: flex;

  span {
    margin: 0 10px;
  }
}
</style>
