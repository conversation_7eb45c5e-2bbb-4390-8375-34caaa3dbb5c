<template>
  <div class="team-set">
    <div class="title">安全设置</div>
    <el-collapse accordion v-model="activeNames">
      <el-collapse-item v-show="false" name="1">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">标签设置</span>
            <span class="hint">（案件自定义标签设置）</span>
          </div>
        </template>
        <div class="col-content">
          <!-- 标签设置组件 -->
          <stamp></stamp>
        </div>
      </el-collapse-item>
      <el-collapse-item v-show="false" name="2">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">审批设置</span>
            <span class="hint">（机构案件处理审批默认设置）</span>
          </div>
        </template>
        <div class="col-content">
          <div class="text-danger">
            审批流程默认主账号进行审批，如需要进行设置，请完成创建机构及员工后进行编辑！
          </div>
          <!-- 审批流程 -->
          <!-- <verify></verify> -->
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">白名单访问</span>
            <span class="hint">（为了保障作业安全，设置白名单访问）</span>
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.whitelistStatus"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
        <div class="col-content">
          <!-- 白名单 -->
          <white></white>
        </div>
      </el-collapse-item>
      <el-collapse-item name="4">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">信息脱敏</span>
            <span class="hint"
              >（为了保障信息安全，对敏感信息进行脱敏处理，例:134****4532）</span
            >
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.informationStatus"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
        <div class="col-content">
          <secrecy-vue :mainStatus="state.informationStatus"></secrecy-vue>
        </div>
      </el-collapse-item>
      <el-collapse-item name="5">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">页面限制</span>
            <span class="hint"
              >（为了保证数据安全性和保护用户个人信息，案件页面需要禁用右键、复制、粘贴功能。）</span
            >
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.restrictedState"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
      </el-collapse-item>
      <el-collapse-item name="6">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">水印设置</span>
            <span class="hint"
              >（为了提高催收作业过程中的信息安全，系统页面添加水印样式）</span
            >
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.settingStatus"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
      </el-collapse-item>
      <el-collapse-item name="7">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">外访授权</span>
            <span class="hint">（监控外访人员催收全程记录，保证外访人员合规作业）</span>
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.authorizationStatus"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
      </el-collapse-item>
      <el-collapse-item name="8">
        <template #title>
          <div class="wcc-flex">
            <span class="tit">实名认证</span>
            <span class="hint"
              >（对作业人员真实性进行的验证审核，以便建立完善可靠的作业基础，实现人证合一）</span
            >
            <div class="text-right" @click.stop>
              <el-switch
                class="myswitch mr10"
                v-model="state.authenticationStatus"
                active-color="#2ECC71"
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              ></el-switch>
            </div>
          </div>
        </template>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import stamp from "@/views/team/components/stamp/index.vue";
import verify from "@/views/team/components/verifyProcess/index.vue";
import white from "@/views/team/components/whiteList/index.vue";
import secrecyVue from "@/views/team/components/infoSecrecy/index.vue";

const activeNames = ref(["1"]);

const state = inject("state");
</script>

<style lang="scss" scoped>
.team-set {
  border: 1px solid #e8e8e8;
  border-bottom: unset;
  margin-bottom: 20px;
}
.wcc-flex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 48px;
  padding: 0 20px;
  font-size: 12px;
  flex: 1;
  .tit {
    color: #3f3f3f;
  }
  .hint {
    color: #888888;
  }
}
.col-content {
  padding: 20px 20px 0;
}
.text-right {
  margin-left: auto;
}
</style>
