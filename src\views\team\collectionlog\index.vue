<template>
  <div class="app-container">
    <el-tabs type="border-card" v-model="active">
      <el-tab-pane label="催记报表" name="0">
        <collectionFormVue v-if="active == '0'" />
      </el-tab-pane>
      <el-tab-pane label="催记记录" name="1">
        <collectionRecordVue v-if="active == '1'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Urgelog">
import collectionFormVue from "./page/collectionForm.vue";
import collectionRecordVue from "./page/collectionRecord.vue";

const active = ref("0");
</script>

<style scoped></style>
