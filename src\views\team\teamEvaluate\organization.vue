<template>
  <div>
    <el-form :model="queryParams" prop="organization" :inline="true">
      <el-form-item label="日期:" prop="recordTime" style="width: 380px">
        <el-date-picker
          v-model="queryParams.recordTime"
          type="daterange"
          value-format="YYYY-MM-DD"
          style="width:240px"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column
        label="机构名称"
        align="center"
        key="teamName"
        prop="teamName"
      />
      <el-table-column label="机构类型" align="center" key="teamType" prop="teamType" />
      <el-table-column
        label="回款率"
        align="center"
        key="collectionRate"
        prop="collectionRate"
        show-overflow-tooltip
      />
      <el-table-column
        label="催记量"
        align="center"
        key="reminderQuantity"
        prop="reminderQuantity"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.reminderQuantity || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="坐席数量"
        align="center"
        key="numberSeats"
        prop="numberSeats"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.numberSeats || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="通话量"
        align="center"
        key="callVolume"
        prop="callVolume"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.callVolume || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="外访覆盖率"
        align="center"
        key="countryCoverage"
        prop="countryCoverage"
        show-overflow-tooltip
      />
      <el-table-column label="操作" fixed="right" width="180px">
        <template #default="{ row }">
          <el-button type="primary" link v-hasPermi="['team:list:create']" @click="openList(row)"
            >生成评价表</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>

 <v-grade ref="gradeRef"  @getList="getList"/>
</template>

<script setup>
import { selectEvaluate } from "@/api/team/teamevaluate";
import vGrade from "./dialog/grade";
const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recordTime: getData(),
  },

});
const { queryParams } = toRefs(data);
const total = ref(0);
const loading = ref(false);
const dataList = ref([]);
const targetRef = ref();
//拆分字段
const rangfiles = [
   "recordTime"
];

function getList() {
  loading.value = false;
   let form = proxy.addFieldsRange(queryParams.value, rangfiles)
  selectEvaluate(form).then((res)=>{
    console.log(res);
    total.value=res.total;
    dataList.value=res.rows
  })
  .catch(() => {
      loading.value = false;
    });

}
provide("getList", Function, true);
getList();

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
function resetQuery() {
  proxy.resetForm("queryRef");
   queryParams.value =  {
    pageNum: 1,
    pageSize: 10,
    recordTime: getData(),
  },
  handleQuery();
}
function openList(row) {
  let req  = row;
  proxy.$refs["gradeRef"].opendialog(req,queryParams.value);
}

//格式化时间
function getData(){
    var date = new Date();
    date.setDate(1)
    var before = new Date(date.getFullYear(), date.getMonth() + 1, 0); //前一天
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate(1);

    var beforeMonth = before.getMonth() + 1;
    var beforeDay = before.getDate(0);

    month = month >= 10 ? month : '0' + month;
    day = day >= 10 ? day : '0' + day;
    beforeMonth = beforeMonth >= 10 ? beforeMonth : '0' + beforeMonth
    beforeDay = beforeDay >= 10 ? beforeDay : '0' + beforeDay

    return [`${year}-${month}-${day}`,`${year}-${beforeMonth}-${beforeDay}`]
}

</script>

<style lang="scss" scoped></style>
