<template>
  <el-table :data="list">
    <el-table-column
      label="字段"
      prop="fieldname"
      align="center"
      width="150"
    ></el-table-column>
    <el-table-column label="状态" prop="status" align="center" width="150">
      <template #default="{ row }">
        <el-switch
          class="myswitch"
          v-model="row.status"
          active-color="#2ECC71"
          :active-value="1"
          :inactive-value="0"
          active-text="开"
          inactive-text="关"
          :disabled="states.informationStatus === 0"
          @change="change"
        ></el-switch>
      </template>
    </el-table-column>
    <el-table-column
      label="脱敏规则"
      prop="rules"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
  </el-table>
</template>

<script setup>
import { changSecrecys } from "@/api/team/team";

const props = defineProps({
  mainStatus: {
    type: Number,
  },
});
const route = useRoute();
const { proxy } = getCurrentInstance();

const list = inject("desensitization");
const states = inject("state");
const _desensitization = inject("_desensitization"); //脱敏

watch(
  () => props.mainStatus,
  (newval, oldval) => {
    list.map((item) => {
      item.status = newval;
    });
  }
);

const getTeamSafe = inject("getTeamSafe", Function, true);
function change() {
  if (route.params.teamId) {
    let obj = _desensitization.value;
    list.value.map((item) => {
      let key = item.filed;
      if (key in obj) {
        obj[key] = item.status;
      }
    });
    changSecrecys(obj)
      .then((res) => {})
      .catch(() => {
        getTeamSafe();
      });
  }
}
</script>

<style scoped></style>
