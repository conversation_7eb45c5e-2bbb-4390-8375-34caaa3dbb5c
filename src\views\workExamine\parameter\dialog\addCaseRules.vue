<template>
  <el-dialog
    :title="exType == 0 ? '新建分案规则' : '修改分案规则'"
    v-model="open"
    width="1000px"
    :before-close="cancel"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form :model="form" class="mt10" :rules="rules" ref="formRef">
      <el-form-item class="mar0" label-width="120px" label="分案规则名称" prop="ruleName">
        <el-input
          v-model="form.ruleName"
          clearable
          :disabled="exType == 1"
          show-word-limit
          maxlength="20"
          style="width: 360px"
          placeholder="请输入分案规则名称"
        />
        <el-button
          type="primary"
          style="position: absolute; right: 10px"
          icon="Plus"
          link
          @click="add(row)"
          >新增</el-button
        >
      </el-form-item>
      <el-table :data="form.detailList" border width="100%" style="max-height: 600px">
        <el-table-column align="center">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`detailList.${$index}.fieldType`"
              :rules="rules.fieldType"
            >
              <el-select style="width: 240px"
                v-model="row.fieldType"
                clearable
                filterable
                :reserve-keyword="false"
                @change="clearRowData($index)"
              >
                <el-option
                  v-for="item in fieldTypeList"
                  :key="item.info"
                  :value="item.info"
                  :label="item.info"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row, $index }">
            <el-form-item :prop="`detailList.${$index}.field`" :rules="rules.field">
              <el-select style="width: 240px"
                v-model="row.field"
                clearable
                filterable
                :reserve-keyword="false"
                @change="completeExpectedValue(row.fieldType, 2, $index, 2)"
              >
                <el-option
                  v-for="item in row.fieldType == '案件属性' ? fieldCaseList : fieldList"
                  :key="item.info"
                  :value="item.info"
                  :label="item.info"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row, $index }">
            <el-form-item :prop="`detailList.${$index}.operator`" :rules="rules.operator">
              <el-select style="width: 240px"
                v-model="row.operator"
                clearable
                filterable
                :reserve-keyword="false"
                @focus="computedOperatorList(row.field, $index)"
              >
                <el-option
                  v-for="item in row.operatorList"
                  :key="item"
                  :value="item"
                  :label="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`detailList.${$index}.expectedValue`"
              :rules="rules.expectedValue"
            >
              <el-select style="width: 240px"
                v-if="row.type == 'select' && row.field !== '逾期天数'"
                v-model="row.expectedValue"
                clearable
                filterable
                placeholder="请选择"
                :reserve-keyword="false"
                @focus="completeExpectedValue(row.field, 2, $index, 4)"
              >
                <el-option
                  v-for="item in row.expectedValueList"
                  :key="item.info"
                  :value="item.info"
                  :label="item.info"
                >
                </el-option>
              </el-select>
              <el-select style="width: 240px"
                v-if="row.type == 'select' && row.field === '逾期天数'"
                v-model="row.expectedValue"
                clearable
                filterable
                placeholder="请选择"
                :reserve-keyword="false"
                @focus="completeExpectedValue(row.field, 2, $index, 4)"
              >
                <el-option
                  v-for="item in row.expectedValueList"
                  :key="item.code"
                  :value="item.info"
                  :label="item.code"
                >
                </el-option>
              </el-select>
              <el-input
                v-model="row.expectedValue"
                v-else-if="row.type == 'input'"
                clearable
                show-word-limit
                placeholder="请输入"
                @blur="checkOverDays(row.field, $index)"
              />
              <el-date-picker
                v-else-if="row.type == 'date'"
                v-model="row.expectedValue"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="yyyy-MM-dd"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row, $index }">
            <el-form-item :prop="`detailList.${$index}.linkage`" :rules="rules.linkage">
              <el-select style="width: 240px"
                v-model="row.linkage"
                clearable
                filterable
                :reserve-keyword="false"
              >
                <el-option
                  v-for="item in linkageList"
                  :key="item"
                  :value="item"
                  :label="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column width="90px">
          <template #default="{ $index }">
            <el-button type="primary" link @click="remove($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { addRule, editRule, selectWithRule } from "@/api/workExamine/parameter";
//全局配置
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
//接口参数
const typeInfo = ref([addRule, editRule]);
const fieldTypeList = ref([
  { code: 0, info: "渠道定制" },
  { code: 1, info: "案件属性" },
]);
const fieldList = ref([]);
const fieldCaseList = ref([]);
const linkageList = ref(["且", "或"]);
//数值列表参数
const listInfo = ref(["fieldList", "fieldCaseList", "expectedValueList"]);
//表单属性
const open = ref(false);
const loading = ref(false);
const exType = ref(0);
const reqId = ref(undefined);
//表单参数
const data = reactive({
  form: {
    ruleName: undefined,
    detailList: [
      {
        fieldType: "渠道定制",
        field: "渠道",
        operator: "等于",
        expectedValue: "智信360",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "渠道定制",
        field: "品牌",
        operator: "等于",
        expectedValue: "众利e贷",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "渠道定制",
        field: "产品",
        operator: "等于",
        expectedValue: "智信360",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "案件属性",
        field: "",
        operator: "",
        expectedValue: "",
        linkage: "",
        type: "select",
      },
    ],
  },
  rules: {
    ruleName: [{ required: true, message: "请输入分案规则名称！", trigger: "blur" }],
    fieldType: [{ required: true, message: "请选择字段类型！", trigger: "change" }],
    field: [{ required: true, message: "请选择字段名称！", trigger: "change" }],
    operator: [{ required: true, message: "请选择操作符！", trigger: "change" }],
    expectedValue: [{ required: true, message: "请选择期望值！", trigger: "change" }],
    linkage: [{ required: true, message: "请选择关联关系！", trigger: "change" }],
  },
});
const removeIds = ref([]);
const { form, rules } = toRefs(data);
const clearList = ref(["field", "operator", "expectedValue", "linkage"]);
const overdueDaysStatus = ref([
  {
    code: "C",
    info:"D0"
  },
  {
    code: "M1",
    info:"D1-D30"
  },
  {
    code: "M2",
    info: "D31-D60",
  },
  {
    code: "M3",
    info: "D61-D90",
  },
  {
    code: "M4",
    info: "D91-D120",
  },
  {
    code: "M5",
    info: "D121-D150",
  },
  {
    code: "M6",
    info: "D151-D180",
  },
  {
    code: "M7",
    info: "D181-D210",
  },
  {
    code: "M8",
    info: "D211-D240",
  },
  {
    code: "M9",
    info: "D241-D270",
  },
  {
    code: "M10",
    info: "D271-D300",
  },
  {
    code: "M11",
    info: "D301-D330",
  },
  {
    code: "M12",
    info: "D331-D360",
  },
  {
    code: "M12+",
    info: "D361+",
  },
]);

//开启弹窗
function opendialog(type, row) {
  open.value = true;
  exType.value = type;
  if (type == 1) {
    reqId.value = row.id;
    form.value = row;
  }
  fieldTypeList.value.forEach((item, index) => {
    getSelectData(item.info, index, index);
  });
}

//新增
function add() {
  form.value.detailList.push({
    fieldType: "",
    field: "",
    operator: "",
    expectedValue: "",
    linkage: "",
    type: "select",
  });
}

//删除
function remove(index) {
  console.log(form.value.detailList[index].id);
  if (form.value.detailList[index].id) {
    removeIds.value.push(form.value.detailList[index].id);
  }
  form.value.detailList.splice(index, 1);
}

//查询下拉框信息
function getSelectData(selectStr, selectType, index) {
  let req = {
    ruleName: selectStr,
  };
  selectWithRule(req).then((res) => {
    if (2 === selectType) {
      let item = form.value.detailList?.[index];
      if (res.data?.[0]) {
        item.expectedValueList = selectStr == "逾期阶段"? overdueDaysStatus.value : res.data[0];
        item.type = "select";
      } else if (res.data?.[1]) {
        item.expectedValueList = [];
        item.type = "input";
      } else {
        item.expectedValueList = [];
        item.type = "date";
      }
    } else if (selectType == 0) {
      fieldList.value = res.data;
    } else {
      fieldCaseList.value = res.data;
    }
  });
}

//补全期望值下拉数据
function completeExpectedValue(selectStr, selectType, index, column) {
  if (column === 2) {
    ["operator", "expectedValue", "linkage"].forEach((item, i) => {
      form.value.detailList[index][`${item}`] = "";
    });
    let count = 0;
    form.value.detailList.forEach((item, index) => {
      if (item.field === "上手机构") {
        count++;
        if (count > 1) {
          item.field = "";
          proxy.$modal.msgWarning("只能选择一个上手机构！");
          return false;
        }
      }
    });
  }
  if (form.value.detailList[index]?.field?.length > 0) {
    getSelectData(form.value.detailList[index]?.field, 2, index);
  }
}

//检测逾期天数
function checkOverDays(field, index) {
  if (
    field === "逾期天数" &&
    form.value.detailList[index].expectedValue &&
    !/^(0|[1-9][0-9]*)$/.test(form.value.detailList[index].expectedValue)
  ) {
    console.log(form.value.detailList[index]?.expectedValue);
    form.value.detailList[index].expectedValue = undefined;
    proxy.$modal.msgWarning("逾期天数只能输入正整数或0！");
  }
}

//计算操作符
function computedOperatorList(fieldStr, index) {
  let operatorArr = ["等于", "不等于", "小于", "大于", "小于等于", "大于等于"];
  let writeList = [
    "渠道",
    "品牌",
    "产品",
    "案件状态",
    "资金方",
    "担保方",
    "借款用户来源",
    "上手机构",
    "结清状态",
  ];
  form.value.detailList[index].operatorList = writeList.includes(fieldStr)
    ? operatorArr.filter((item) => item == "等于")
    : fieldStr == "A卡等级"
    ? ["等于", "不等于"]
    : operatorArr;
}

//选中规则限制
function clearRowData(index) {
  clearList.value.forEach((item, i) => {
    form.value.detailList[index][`${item}`] = "";
  });
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      console.log(2222);
      loading.value = true;
      let req = {
        id: exType.value == 1 ? reqId.value : undefined,
        status: exType.value == 1 ? form.value.status : true,
        ruleName: form.value.ruleName.replace(/\s/g, ""),
        detailList: form.value.detailList,
        removeIds: removeIds.value,
      };
      typeInfo.value[exType.value](req)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("操作成功");
          cancel();
          emit("getList");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  removeIds.value = [];
  form.value = {
    ruleName: undefined,
    detailList: [
      {
        fieldType: "渠道定制",
        field: "渠道",
        operator: "等于",
        expectedValue: "智信360",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "渠道定制",
        field: "品牌",
        operator: "等于",
        expectedValue: "众利e贷",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "渠道定制",
        field: "产品",
        operator: "等于",
        expectedValue: "智信360",
        linkage: "且",
        type: "select",
      },
      {
        fieldType: "案件属性",
        field: "",
        operator: "",
        expectedValue: "",
        linkage: "",
        type: "select",
      },
    ],
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
:deep(.el-table thead) {
  display: none;
}
</style>
