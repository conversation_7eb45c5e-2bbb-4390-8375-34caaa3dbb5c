<template>
  <el-table :data="dataList">
    <el-table-column label="序号" width="50px" align="center">
      <template #default="{ $index }">
        {{ $index + 1 }}
      </template>
    </el-table-column>
    <el-table-column
      label="审批名称"
      prop="approvalName"
      align="center"
    ></el-table-column>
    <el-table-column label="更新时间" prop="modifyTime" align="center">
      <template #default="{ row }">
        {{ row.modifyTime || row.creationtime }}
      </template>
    </el-table-column>
    <el-table-column label="创建人" prop="founder" align="center"></el-table-column>
    <el-table-column label="操作">
      <template #default="{row}">
        <el-button type="primary" link @click="openset(row)">{{          edit ? "修改" : "查看"        }}</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog :title="title" v-model="open" width="950px" append-to-body>
    <div class="dispostWin mb8">
      <div class="item">
        <div class="icon">1</div>
        <div class="item-tit">发起人</div>
        <div class="item-content">所有人</div>
      </div>
      <div
        style="display: inline-block"
        v-for="(item, index) in form.approvalSteps"
        :key="index"
      >
        <div class="item-line"></div>
        <div class="item">
          <div class="icon">{{ index + 2 }}</div>
          <div class="item-tit">{{ CNnumber[index] }}级审批</div>
          <div class="item-content">{{ item.approver || "请配置" }}</div>
        </div>
      </div>
    </div>
    <el-row v-if="edit" :gutter="10" class="mb10 mt10">
      <el-col :span="2">
        <el-button
          :disabled="form.approvalSteps.length > 5"
          type="primary"
          plain
          icon="Plus"
          @click="add"
          >新增</el-button
        >
      </el-col>
    </el-row>
    <el-form :model="form" :rules="rules" ref="verifyRef">
      <el-table :data="form.approvalSteps">
        <el-table-column label="步骤" width="55px" align="center">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="审批角色">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`approvalSteps.${$index}.approvalId`"
              :rules="rules.approvalId"
            >
              <el-select style="width: 240px"
                v-model="row.approvalId"
                @change="roleChange(row.approvalId, $index)"
                :disabled="!edit || $index == form.approvalSteps.length - 1"
              >
                <el-option
                  v-for="item in verifyroles"
                  :key="item.id"
                  :label="item.roleName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="审批人">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`approvalSteps.${$index}.approverId`"
              :rules="rules.approverId"
            >
              <el-select style="width: 240px"
                v-model="row.approverId"
                @focus="roleChange(row.approvalId, $index)"
                @change="approvalChange(row.approverId, $index)"
                :disabled="
                  !edit || !row.approvalId || $index == form.approvalSteps.length - 1
                "
              >
                <el-option
                  v-for="item in row.verifymans"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column v-if="edit" label="操作">
          <template #default="{ row, $index }">
            <el-button
              type="primary"
              link
              :disabled="$index == 0 || $index == form.approvalSteps.length - 1"
              @click="moveUp($index)"
              >上移</el-button
            >
            <el-button
              type="primary"
              link
              :disabled="
                $index == form.approvalSteps.length - 1 ||
                $index == form.approvalSteps.length - 2
              "
              @click="moveDown($index)"
              >下移</el-button
            >
            <el-button
              type="primary"
              link
              @click="remove(row, $index)"
              :disabled="$index == form.approvalSteps.length - 1"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="edit" :loading="loading" @click="submit"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  selectApprovalSteps,
  selectRole,
  selectEmployeesRole,
  updateApprovalSteps,
} from "@/api/team/teamManage.js";
const props = defineProps({
  dataList: {
    type: Array,
    default: [],
  },
  edit: {
    type: Boolean,
    default: true,
  },
});
const route = useRoute();
const { proxy } = getCurrentInstance();
const getList = inject("getList", Function, true);

const approveCode = ref(); //审批类型

const title = ref("");
const open = ref(false);
const loading = ref(false);
const deleIds = ref([]);
const data = reactive({
  form: {
    approvalSteps: [],
  },
  rules: {
    approvalId: [{ required: true, message: "请选择审批角色", trigger: "blur" }],
    approverId: [{ required: true, message: "请选择审批人", trigger: "blur" }],
  },
});

const { form, rules } = toRefs(data);

//审批角色
const verifyroles = ref([]);

//审批人
const verifymans = ref([]);

//级数
const CNnumber = ["一", "二", "三", "四", "五", "六", "七"];

//新增审批流程
function add() {
  const obj = { approvalId: "", approverId: "", createId: route.params.teamId };
  form.value.approvalSteps.unshift(obj);
}

//删除审批流程
function remove(row, index) {
  if (row.id) {
    deleIds.value.push(row.id);
  }
  form.value.approvalSteps.splice(index, 1);
}
//打开设置
function openset(row) {
  approveCode.value = row.approveCode;
  let req = {
    createId: row.createId,
    approveCode: row.approveCode,
  };
  Promise.all([
    selectRole(route.params.teamId),
    selectEmployeesRole({ createId: route.params.teamId }),
    selectApprovalSteps(req),
  ])
    .then((res) => {
      verifyroles.value = res[0].data;
      form.value.approvalSteps = res[2].data;
      verifymans.value = res[1].data;
      form.value.approvalSteps.map((item) => {
        item.verifymans = res[1].data;
      });
      title.value = row.name;
      open.value = true;
    })
    .catch((err) => {
      console.log(err);
    });
}

//审批角色选择
function roleChange(roleid, index) {
  let obj = {
    createId: route.params.teamId,
  };
  for (let i = 0; i < verifyroles.value.length; i++) {
    const item = verifyroles.value[i];
    if (item.id == roleid) {
      obj.theRole = item.roleName;
      form.value.approvalSteps[index].approvalRole = item.roleName;
      break;
    }
  }
  selectEmployeesRole(obj)
    .then((res) => {
      form.value.approvalSteps[index].verifymans = res.data;
    })
    .catch(() => {
      form.value.approvalSteps[index].verifymans = [];
    });
}

//获取审批人
function approvalChange(approverId, index) {
  for (let i = 0; i < verifymans.value.length; i++) {
    const item = verifymans.value[i];
    if (item.id === approverId) {
      form.value.approvalSteps[index].approver = item.name;
      break;
    }
  }
}


//上移
function moveUp(index) {
  let upData = form.value.approvalSteps[index - 1];
  form.value.approvalSteps.splice(index - 1, 1);
  form.value.approvalSteps.splice(index, 0, upData);
}

//下移
function moveDown(index) {
  let downData = form.value.approvalSteps[index + 1];
  form.value.approvalSteps.splice(index + 1, 1);
  form.value.approvalSteps.splice(index, 0, downData);
}

//提交
function submit() {
  loading.value = true;
  proxy.$refs["verifyRef"].validate((valid) => {
    if (valid) {
      let req_form = JSON.parse(JSON.stringify(form.value));
      if (deleIds.value.length) {
        //删除的id集合
        req_form.ids = deleIds.value;
      }
      req_form.approvalSteps.map((item, index) => {
        item.sort = index + 1;
        item.approveCode = approveCode.value;
        item.createId = route.params.teamId;
        delete item.verifymans;
      });
      updateApprovalSteps(req_form)
        .then((res) => {
          proxy.$modal.msgSuccess("设置成功！");
          cancel();
          getList();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

//取消配置
function cancel() {
  proxy.resetForm("verifyRef");
  open.value = false;
  form.value = {
    approvalSteps: [],
  };
}
</script>

<style scoped lang="scss">
.dispostWin {
  width: 100%;
  text-align: center;
  overflow: hidden;
}

.dispostWin .item {
  line-height: 20px;
  display: inline-block;
}

.dispostWin .item .icon {
  margin: 0 auto 10px;
  width: 30px;
  color: #ffffff;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: var(--theme);
}

.item-tit {
  color: #3f3f3f;
  font-size: 14px;
}
.item-content {
  font-size: 12px;
  color: var(--theme);
}
.dispostWin .item-line {
  margin-bottom: 60px;
  display: inline-block;
  width: 100px;
  height: 1px;
  background: #e8e8e8;
}
</style>
