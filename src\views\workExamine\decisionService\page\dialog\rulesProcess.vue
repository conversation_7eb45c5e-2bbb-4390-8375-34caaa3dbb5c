<template>
  <el-dialog
    title="规则"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="规则信息" prop="headers">
        <el-checkbox-group v-model="form.headers">
          <el-row :gutter="20">
            <el-col :span="24" :xs="24" v-for="item in headerArr" :key="item">
              <el-checkbox :label="item">{{ item }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        规则（变量）：按放款月、账期、分案模式、分配模式等筛选条件筛选案件，比例分案
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="subloading" @click="cancel">确 认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const route = useRoute();

const headerArr = ref(['金额大于1万','逾期100天大于2万','逾期50天大于2万','金额大于3万']);
const subloading = ref(false);
const open = ref(false);
const data = reactive({
  form: {
    headers:['金额大于1万','逾期50天大于2万']
  },
  rules: {
    headers: [{ required: true, message: "请勾选规则字段", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

 function opendialog(type, caseIds, condition) {
  let policyId = route.params.id;
  open.value = true;
 }

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    headers:['金额大于1万','逾期50天大于2万']
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
