<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="操作人" prop="operationBy">
        <el-input
         v-model="queryParams.operationBy" 
         clearable 
         @keyup.enter="handleQuery" 
         placeholder="请输入" 
         style="width: 240px" 
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 240px">
          <el-option label="合作中" value="0"/>
          <el-option label="合作暂停" value="1"/>
          <el-option label="合作关闭" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10 height32">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
        :columns="columns"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
    >
      <el-table-column
        label="操作时间"
        align="center"
        key="operationTime"
        prop="operationTime"
        sortable
        show-overflow-tooltip
        v-if="columns[0].visible"
      />
      <el-table-column
        label="操作人"
        align="center"
        key="operationBy"
        prop="operationBy"
        sortable
        show-overflow-tooltip
        v-if="columns[1].visible"
      />
      <el-table-column
        label="机构名称"
        align="center"
        key="cname"
        prop="cname"
        sortable
        v-if="columns[2].visible"
      />
      <el-table-column
        label="修改前状态"
        align="center"
        key="statusBefore"
        prop="statusBefore"
        sortable
        :formatter="statusBeforeChange"
        show-overflow-tooltip
        v-if="columns[3].visible"
      />
      <el-table-column
        label="修改后状态"
        align="center"
        key="statusAfter"
        prop="statusAfter"
        sortable
        :formatter="statusAfterChange"
        show-overflow-tooltip
        v-if="columns[4].visible"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Teamlog">
import { selectCooperation } from '@/api/team/teamlog.js'
const { proxy } = getCurrentInstance();

const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const dataList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  operationBy: undefined,
  status: undefined
})

// 列显隐信息
const columns = ref([
  { key: 0, label: `操作时间`, visible: true },
  { key: 1, label: `操作人`, visible: true },
  { key: 2, label: `机构名称`, visible: true },
  { key: 3, label: `修改前状态`, visible: true },
  { key: 4, label: `修改后状态`, visible: true }
]);

//获取列表
function getList() {
  loading.value = true
  selectCooperation(queryParams.value).then(res => {
    console.log(res);
    total.value = res.total;
    dataList.value = res.rows;
  }).finally(() => {
    loading.value = false
  })
}
getList()

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

//修改前状态
function statusBeforeChange(row) {
  return statusChange(row.statusBefore)
}

//修改后状态
function statusAfterChange(row) {
  return statusChange(row.statusAfter)
}

//状态修改
function statusChange(status) {
  let arr = ["合作中", "合作暂停", "合作关闭"];
  return arr[status] || ''
}

</script>

<style lang="scss" scoped>
.height32{
  height: 32px;
}
</style>