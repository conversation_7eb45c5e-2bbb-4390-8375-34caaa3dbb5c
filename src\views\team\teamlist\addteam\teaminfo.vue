<template>
  <div class="team-set">
    <div class="title">机构资料</div>
    <div class="pd20 form-box">
      <el-form
        :inline="true"
        :model="form"
        :rules="rules"
        ref="teamRef"
        label-width="106px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="一级分类" prop="teamLevelType">
              <el-radio-group v-model="form.teamLevelType">
                <el-radio
                  v-for="item in categoryOptions"
                  :key="item.teamType"
                  :label="item.teamType"
                  >{{ item.teamType }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构类别" prop="category">
              <el-radio-group v-model="form.category">
                <el-radio
                  v-for="item in changelawyer(form.teamLevelType)"
                  :key="item"
                  :label="item"
                  >{{ item }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构名称" prop="cname">
              <el-input
                v-model="form.cname"
                placeholder="请输入机构名称"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构类型" prop="teamType">
              <el-select
                v-model="form.teamType"
                placeholder="请选择"
                size="default"
                style="width: 220px"
              >
                <el-option
                  v-for="(item, index) in teamTypeList"
                  :key="index"
                  :label="item.info"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人手机号码" prop="contact">
              <el-input
                v-model="form.contact"
                placeholder="请输入公司联系人手机号码"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="account">
              <el-input
                v-model="form.account"
                placeholder="请输入登录账号"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="座机号码" prop="machine">
              <el-input
                v-model="form.machine"
                placeholder="示例:1234-********"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="子账号个数" prop="numbers">
              <el-input-number
                size="default"
                v-model="form.numbers"
                controls-position="right"
                :min="0"
                style="width: 220px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入联系人邮箱"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作状态" prop="cooperation">
              <el-select
                v-model="form.cooperation"
                placeholder="请选择"
                size="default"
                style="width: 220px"
              >
                <el-option label="合作中" value="0"></el-option>
                <el-option label="合作暂停" value="1"></el-option>
                <el-option label="合作关闭" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保证金" prop="margin">
              <el-input
                v-model="form.margin"
                v-inputMoney
                placeholder="请输入"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作开始时间" prop="starts">
              <el-date-picker
                v-model="form.starts"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择时间"
                size="default"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同到期时间" prop="complete">
              <el-date-picker
                v-model="form.complete"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择时间"
                size="default"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="佣金比" prop="commissionRatio">
              <el-input
                v-inputMoney
                v-model="form.commissionRatio"
                placeholder="请输入"
                style="width: 200px"
              ></el-input>
              <div>%</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构描述" prop="describes">
              <el-input
                v-model="form.describes"
                type="textarea"
                maxlength="100"
                show-word-limit
                placeholder="请输入内容"
                style="width: 600px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构地址" prop="outsideAddress">
              <el-input
                v-model="form.outsideAddress"
                id="suggestId"
                placeholder="请输入地址"
              />
              <div class="mt5" id="container" style="width: 620px; height: 360px"></div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="委托合同及相关凭证">
              <el-upload
                ref="uploadRef"
                multiple
                :limit="5"
                accept=".rar, .zip, .doc, .docx, .pdf"
                :headers="upload.headers"
                :action="upload.url"
                :before-upload="handleFileUploadBefore"
                :on-change="handleEditChange"
                :before-remove="handleRemove"
                :on-success="handleFileSuccess"
                :auto-upload="false"
                drag
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <template #tip>
                  <div class="el-upload__tip">
                    <span
                      >支持格式：.rar .zip .doc .docx .pdf
                      ，单个文件不能超过20MB，最多不能超过5个文件</span
                    >
                  </div>
                  <el-button type="success" @click="submitFile">上传到服务器</el-button>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { verification, selectDictData, OrganizationType } from "@/api/team/team";
const { proxy } = getCurrentInstance();

// 座机验证
const validateMachine = (rule, value, callback) => {
  var reg = /^\d{3,4}[-]\d{7,8}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入座机号码！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的座机号码！"));
  } else {
    callback();
  }
};

//手机号码验证
const validatePhone = (rule, value, callback) => {
  var reg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入手机号码！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的手机号码！"));
  } else {
    callback();
  }
};

//邮箱验证
const validateEmail = (rule, value, callback) => {
  var reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入邮箱！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的邮箱！"));
  } else {
    callback();
  }
};

//账号验证
const validateAccount = (rule, value, callback) => {
  let reg = /^[a-zA-Z_][a-zA-Z0-9_]{5,14}/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入登陆账号！"));
  } else if (value.length < 6 || value.length > 15) {
    callback(new Error("请保持登陆账号长度为6～15位！"));
  } else if (!reg.test(value)) {
    callback(new Error("由字母.下划线.数字组成,不能以数字开头!"));
  } else {
    callback();
  }
};

const categoryOptions = ref([]); //团队类型
const lawyerOptions = ref([]);

const data = reactive({
  form: {
    cooperation: "0",
    teamType: undefined,
    outsideAddress: "",
    longitudeAtitudeStart: "",
    teamLevelType: "催收类",
  },
  rules: {
    teamLevelType: [{ required: true, message: "请选择一级类别", trigger: "change" }],
    category: [{ required: true, message: "请选择机构类别", trigger: "change" }],
    cname: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
    contact: [{ required: true, validator: validatePhone, trigger: "blur" }],
    account: [{ required: true, validator: validateAccount, trigger: "blur" }],
    machine: [{ required: true, validator: validateMachine, trigger: "blur" }],
    numbers: [{ required: true, message: "请输入子账号个数", trigger: "change" }],
    email: [{ required: true, validator: validateEmail, trigger: "blur" }],
    cooperation: [{ required: true, message: "请选择合作状态", trigger: "change" }],
    teamType: [{ required: true, message: "请选择机构类型", trigger: "blur" }],
    outsideAddress: [{ required: true, message: "请输入机构地址", trigger: "blur" }],
    commissionRatio: [
      { required: false, message: "请填入佣金比", trigger: "blur" },
      {
        pattern: /[?!^0-9]/g,
        message: "请输入数字",
        trigger: "blur",
      },
    ],
    // margin: [{  required: true, message: "请输入保证金", trigger: "blur"  }]
  },
});
const { form, rules } = toRefs(data);

const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/dispose/file/minioUpload",
});
const teamTypeList = ref([]);

let files = inject("files");

const changelawyer = computed(() => (value) => {
  let newLayer = [];
  if (categoryOptions.value.length > 0) {
    const lawyerArr = categoryOptions.value.filter(
      (item) => item.teamType == form.value.teamLevelType
    );
    lawyerArr.forEach((item) => {
      newLayer = item.categoryList;
    });
  }
  return newLayer;
});

//团队类别下拉
const getSelectDictData = () => {
  selectDictData().then((res) => {
    categoryOptions.value = res.data;
  });
};

getSelectDictData();

function initMap() {
  // 百度地图API功能
  var map = new BMap.Map("container"); // 创建Map实例
  map.centerAndZoom(new BMap.Point(116.404, 39.915), 12); // 初始化地图,设置中心点坐标和地图级别
  //添加地图类型控件
  map.addControl(
    new BMap.MapTypeControl({
      mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
    })
  );
  map.setCurrentCity("北京"); // 设置地图显示的城市 此项是必须设置的
  map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

  var geoc = new BMap.Geocoder();

  var ac = new BMap.Autocomplete({
    // suggestId 是输入框id
    input: "suggestId",
    //  这个是地图实例
    location: map,
  });

  // 下拉列表里的内容确认发生的事件 ()
  ac.addEventListener("onconfirm", function (e) {
    // 把城市啥的拼接起来
    const myValue =
      e.item.value.province +
      e.item.value.city +
      e.item.value.district +
      e.item.value.street +
      e.item.value.business;
    // 搜索
    // 搜索结束执行的函数
    const mySearchFun = () => {
      // 传入定位函数的经纬度
      getAddOverlay(local.getResults().getPoi(0).point, true);
    };

    // 创建一个搜索的实例
    var local = new BMap.LocalSearch(map, {
      //搜索成功后的回调
      onSearchComplete: mySearchFun,
    });
    // 传入搜索位置的关键字
    local.search(myValue);
  });

  // 下面是开始定位的
  var point = new BMap.Point(116.404, 39.915); // 定位
  getAddOverlay(point, true);
  // 当地图点击的时候发生的事件
  map.addEventListener("click", function (e) {
    // 创建标点
    getAddOverlay(new BMap.Point(e.point.lng, e.point.lat));
  });
  // 定位点的函数
  function getAddOverlay(point, centerAndZoom = false) {
    // 清空地图上所有的标准当然你想要多个点的话可以不清除
    map.clearOverlays();
    var marker = new BMap.Marker(point); // 创建标注
    map.addOverlay(marker); // 添加到地图

    centerAndZoom && map.centerAndZoom(point, 12); // 中心点位 15是级别

    // 把定位转换为详细文字地址
    geoc.getLocation(point, (rs) => {
      // 显示到页面上s
      form.value.outsideAddress = rs.address;
    });
    // 把位置传出
    // emits("addressData", point);
    if (form.value.outsideAddress) {
      const pointArr = [point.lng, point.lat];
      form.value.longitudeAtitudeStart = pointArr.join(",");
    }
  }
}

onMounted(() => {
  nextTick(() => {
    initMap();
  });
});

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    fileList.pop();
    return false;
  }
}

//获取机构类别
function getTeamType() {
  OrganizationType().then((res) => {
    teamTypeList.value = res.data;
  });
}
getTeamType();

/* 文件移除 */
function handleRemove(file, fileList) {
  let modifyName = "";
  if (file.response && file.response.code === 200) {
    modifyName = file.response.data.modifyName[0];
    for (let i = 0; i < files.length; i++) {
      if (files[i].modifyName == modifyName) {
        files.splice(i, 1);
        break;
      }
    }
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    var obj = {
      firstName: data.firstName[0],
      modifyName: data.modifyName[0],
      fileUrl: data.fileUrl[0],
    };
    files.push(obj);
  }
};

// 保存数据
function saveTeamInfo() {
  return new Promise((reslove) => {
    proxy.$refs["teamRef"].validate((valid) => {
      if (valid) {
        verification(form.value)
          .then((res) => {
            reslove(form.value);
          })
          .catch(() => {
            reslove(false);
          });
      } else {
        reslove(false);
      }
    });
  });
}

defineExpose({
  //暴露组件方法
  saveTeamInfo,
});
</script>

<style scoped>
.form-box {
  width: 800px;
  margin: 0 auto;
}
</style>
