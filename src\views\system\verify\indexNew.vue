<template>
  <div class="app-container">
    <el-form :model="queryParams" :inline="true" ref="queryRef">
      <el-form-item prop="approveName">
        <el-input v-show="false" />
        <el-input
          v-model="queryParams.approveName"
          clearable
          @keyup.enter="handleQuery"
          placeholder="审批名称"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="dataList" v-loading="loading">
      <el-table-column label="序号" width="50px" align="center">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="审批名称" prop="approveName" align="center" />
      <el-table-column label="更新时间" prop="updateTime" align="center" />
      <el-table-column label="创建人" prop="updateBy" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            v-hasPermi="['system:newVerify:edit']"
            @click="edit(row)"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <editVue ref="editVueRef" @getList="getList" />
  </div>
</template>

<script setup name="Verify">
import { zcGetApprovalType } from "@/api/system/verify";
import editVue from "./dialog/edit.vue";
const { proxy } = getCurrentInstance();

const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    approveName: undefined,
  },
});
const { queryParams } = toRefs(data);

//获取列表
function getList() {
  loading.value = true;
  zcGetApprovalType(queryParams.value)
    .then((res) => {
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//修改
function edit(row) {
  proxy.$refs["editVueRef"].openDialog(row);
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    approveName: undefined,
  };
  getList();
}
</script>

<style lang="scss" scoped>
.dispostWin {
  width: 100%;
  text-align: center;
  overflow: hidden;
}

.dispostWin .item {
  line-height: 20px;
  display: inline-block;
}

.dispostWin .item .icon {
  margin: 0 auto 10px;
  width: 30px;
  color: #ffffff;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: var(--theme);
}

.item-tit {
  color: #3f3f3f;
  font-size: 14px;
}
.item-content {
  font-size: 12px;
  color: var(--theme);
}
.dispostWin .item-line {
  margin-bottom: 60px;
  display: inline-block;
  width: 100px;
  height: 1px;
  background: #e8e8e8;
}
:deep(.el-popper .el-select-dropdown__wrap) {
  max-height: 180px;
  height: 180px;
}
</style>
<style>
.el-select-dropdown__wrap {
  max-height: 180px;
}
</style>

