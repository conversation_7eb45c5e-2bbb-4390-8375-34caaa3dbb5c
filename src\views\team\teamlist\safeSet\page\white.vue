<template>
  <div>
    <div class="title mb20">
      <span>开启后，白名单列表外的IP地址禁止访问系统。</span>
      <el-switch
        class="myswitch ml20"
        v-model="state.whitelistStatus"
        active-color="#2ECC71"
        :active-value="1"
        :inactive-value="0"
        active-text="开"
        inactive-text="关"
        @change="change"
      ></el-switch>
    </div>
    <div v-if="state.whitelistStatus">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="85px"
      >
        <el-form-item label="白名单名称" prop="whitelistName">
          <el-input
            v-model="queryParams.whitelistName"
            placeholder="请输入"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          ></el-input>
        </el-form-item>
        <el-form-item label="IP地址" prop="addressIp">
          <el-input
            v-model="queryParams.addressIp"
            placeholder="请输入"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="founder">
          <el-input
            v-model="queryParams.founder"
            placeholder="请输入"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            :disabled="state.whitelistStatus === 0"
            @click="add"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            :disabled="single || state.whitelistStatus === 0"
            @click="remove()"
            >批量删除</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
          :columns="columns"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="whiteList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="30" align="right" />
        <el-table-column
          label="公司名称"
          align="center"
          key="createName"
          prop="createName"
          v-if="columns[0].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="白名单名称"
          align="center"
          key="whitelistName"
          prop="whitelistName"
          v-if="columns[1].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="IP地址"
          align="center"
          key="addressIp"
          prop="addressIp"
          v-if="columns[2].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          key="note"
          prop="note"
          v-if="columns[3].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建人"
          align="center"
          key="founder"
          prop="founder"
          v-if="columns[4].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          align="center"
          key="creationTime"
          prop="creationTime"
          v-if="columns[5].visible"
          show-overflow-tooltip
        />
        <el-table-column
          label="状态"
          align="center"
          key="state"
          prop="state"
          v-if="columns[6].visible"
        >
          <template #default="{row}">
            <el-switch
              class="myswitch"
              v-model="row.state"
              active-color="#2ECC71"
              :active-value="1"
              :inactive-value="0"
              active-text="开"
              inactive-text="关"
              @change="listStateChange(row)"
              :disabled="state.whitelistStatus === 0"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{row}">
            <el-button
              type="primary"
              link
              :disabled="state.whitelistStatus === 0"
              @click="edit(row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              :disabled="state.whitelistStatus === 0"
              @click="remove(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total >= 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增或编辑白名单 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <whiteForm ref="whiteRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import whiteForm from "@/views/team/components/whiteList/index.vue";
import {
  getWhites,
  addWhites,
  changeSafeStatus,
  updateWhite,
  deleteWhite,
  updateListWhiteState,
} from "@/api/team/team";
const { proxy } = getCurrentInstance();
const route = useRoute();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);

const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const loading = ref(false);
const whiteList = ref([]);
const title = ref("");
const open = ref(false);
const whiteRef = ref();
const data = reactive({
  form: {
    createId: route.params.teamId,
    whitelistName: undefined,
    addressIp: undefined,
    note: undefined,
  },
  queryParams: {
    createId: route.params.teamId,
    pageNum: 1,
    pageSize: 10,
    whitelistName: undefined,
    addressIp: undefined,
    founder: undefined,
  },
});

const columns = ref([
  { key: 0, label: `公司名称`, visible: true },
  { key: 1, label: `白名单名称`, visible: true },
  { key: 2, label: `IP地址`, visible: true },
  { key: 3, label: `备注`, visible: true },
  { key: 4, label: `创建人`, visible: true },
  { key: 5, label: `创建时间`, visible: true },
  { key: 6, label: `状态`, visible: true },
]);

const { form, queryParams } = toRefs(data);
function getList() {
  loading.value = true;
  getWhites(queryParams.value).then((res) => {
    loading.value = false;
    whiteList.value = res.rows;
    total.value = res.total;
  });
}
getList();

//新增白名单
function add() {
  open.value = true;
  title.value = "添加白名单";
}

//编辑白名单
function edit(row) {
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
  title.value = "编辑白名单";
}

//删除白名单
function remove(row) {
  if (row) {
    proxy.$modal.confirm('是否确认删除白名单名称为"' + row.whitelistName + '"的数据项？')
      .then(function () {
        return deleteWhite([{ id: row.id }]);
      }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
  } else {
    proxy.$modal.confirm("是否确认删除选中的数据项？").then(()=> {
        let data = [];
        ids.value.map((item) => {
          let obj = { id: item };
          data.push(obj);
        });
        return deleteWhite(data);
      }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
  }
}
provide("white", form);

//开关
function change() {
  changeSafeStatus(state.value)
    .then(() => {})
    .catch(() => {
      getTeamSafe();
    });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  whiteList.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

//选择表格
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = !selection.length;
}

//白名单列表开关
function listStateChange(row) {
  let data = {
    id: row.id,
    state: row.state,
  };
  updateListWhiteState(data).then((res) => {
    let { code, msg } = res;
    if (code !== 200) {
      proxy.$modal.msgError(msg);
    } else {
      proxy.$modal.msgSuccess(msg);
    }
    getList();
  });
}

//清空表单
function reset() {
  whiteRef.value.reset();
  form.value = {
    createId: route.params.teamId,
    whitelistName: undefined,
    addressIp: undefined,
    note: undefined,
  };
}

//取消按钮
function cancel() {
  open.value = false;
  reset();
}

//添加/编辑白名单
function submitForm() {
  whiteRef.value.validateForm().then((valid) => {
    if (valid) {
      if (form.value.id) {
        //修改
        updateWhite(form.value).then((res) => {
          let { code, msg } = res;
          if (code !== 200) {
            proxy.$modal.msgError(msg);
          } else {
            proxy.$modal.msgSuccess(msg);
            cancel();
            getList();
          }
        });
      } else {
        addWhites(form.value).then((res) => {
          let { code, msg } = res;
          if (code !== 200) {
            proxy.$modal.msgError(msg);
          } else {
            proxy.$modal.msgSuccess(msg);
            cancel();
            getList();
          }
        });
      }
    }
  });
}

</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
