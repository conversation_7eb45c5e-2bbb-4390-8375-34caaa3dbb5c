// import { listAndQuery } from "@/api/conference/conference";
// import {
//   getBiddingList,
//   getContractList,
// } from "../../../api/conference/projectInfo";
import { getDetailList } from "../../../api/conference/projectInfo";
import dayjs from "dayjs";
const data = ref({
  formInfo: {
    startNapeList_processTitle: "xxx收购项目方案审批",
    startNapeList_projectName: "收购某某某资产包项目",
    startNapeList_projectId: "dg202411110001",
    startNapeList_transferor: "中国银行东莞分行",
    startNapeList_produceType: "金融不良收购",
    startNapeList_entrustMoney: "10000",
    startNapeList_entrustPrincipal: "10000000000000000",
    startNapeList_entrustInterest: "10000000000000000",
    startNapeList_households: "10000",
    startNapeList_whetherPayDeposit: "是",
    startNapeList_payDeposit: "100.00",
    startNapeList_baseDate: "2024-12-22 12:00:00",
    startNapeList_biddingDate: "2024-12-22 ",
    startNapeList_biddingMethod: "线下公开 ",
    startNapeList_ouotationCeiling: "10000000000000 ",
    startNapeList_projectSponsor: "admin ",
    startNapeList_projectUndertaking: "胡图图 ",
    startNapeList_applayBy: "admin ",
    startNapeList_applayTime: "-- ",
    startNapeList_feasibilityReport: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的汇报尽调报告",
        fileUrl: "",
      },
    ],
    startNapeList_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],
    startNapeList_ohterFileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产资料表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产资料表",
        fileUrl: "",
      },
    ],

    schemeApproval_processTitle: "收购某某某资产包项目",
    schemeApproval_projectName: "收购某某某资产包项目",
    schemeApproval_projectId: "dg202411110001",
    schemeApproval_transferor: "中国银行东莞分行",
    schemeApproval_produceType: "金融不良收购",
    schemeApproval_entrustMoney: "10000",
    schemeApproval_entrustPrincipal: "10000000000000000",
    schemeApproval_entrustInterest: "10000000000000000",
    schemeApproval_households: "10000",
    schemeApproval_whetherPayDeposit: "是",
    schemeApproval_payDeposit: "100.00",
    schemeApproval_baseDate: "2024-12-22 12:00:00",
    schemeApproval_biddingDate: "2024-12-22 ",
    schemeApproval_biddingMethod: "线下公开 ",
    schemeApproval_ouotationCeiling: "10000000000000 ",
    schemeApproval_projectSponsor: "admin ",
    schemeApproval_projectUndertaking: "胡图图 ",
    schemeApproval_applayBy: "admin ",
    schemeApproval_applayTime: "2024-11-22 12:00:00",
    schemeApproval_feasibilityReport: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的汇报尽调报告",
        fileUrl: "",
      },
    ],
    schemeApproval_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],
    schemeApproval_ohterFileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产资料表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产资料表",
        fileUrl: "",
      },
    ],

    bidding_processTitle: "xxxxx2025-04竞价收购申请",
    bidding_applayBy: "adXmin ",
    bidding_applayDept: "xxxxx",
    bidding_projectName: "xxxxx2025-04收购项目",
    bidding_biddingMethod: "线上公开",
    bidding_produceType: "消费贷",
    bidding_quotationAmount: "100000000000000",
    bidding_whetherSeal: "是",
    bidding_SealType: "公章",
    bidding_remarks: "情况属实",
    bidding_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],

    conference_processTitle: "xxxxx2025-04竞价收购申请",
    conference_whetherPass: "是",
    conference_decisionMeeting: "2024年第8次总经理办公会议",
    conference_decisionTime: "2024-0108",
    conference_decisionNo: "【2024】8号",
    conference_decisionResult:
      "同意以不高于8888.88万元的价格收购某某公司资产包",
    conference_remarks: "--",
    conference_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],

    contract_processTitle: "xxxxx2025-03收购合同申请",
    contract_contractName: "是收购合同",
    contract_contractNo: "xxxxxxxxx",
    contract_contractType: "XXXXXXXXx",
    contract_contractStatus: "XXXXXXXXx",
    contract_initiateWay: "线下",
    contract_signNum: "3",
    contract_seal: "公章",
    contract_remarks: "--",
    contract_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],
    contract_ohterFileList: [],

    projectAccounting_processTitle: "xxxxx2025-03项目建账申请",
    projectAccounting_serialNo: "**********",
    projectAccounting_payStatus: "已支付",
    projectAccounting_payType: "全款",
    projectAccounting_accountNum: "***************",
    projectAccounting_accountName: "中国银行",
    projectAccounting_openingBank: "中国银行 东莞支行",
    projectAccounting_contractMoney: "***************.00",
    projectAccounting_applayMoney: "***************.00",
    projectAccounting_remarks: "--",
    projectAccounting_fileList: [
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
      {
        fileName: "关于收购中信银行郑州分行2024-03号资产包项目的资产评估表",
        fileUrl: "",
      },
    ],
  },
  dataList: [
    {
      projectAccounting_processTitle: "资产包收购合同...",
      projectAccounting_accountStatus: "建账失败",
      projectAccounting_paymentRequestSerialNo: "********...",
      projectAccounting_paymentOrderStatus: "支付",
      projectAccounting_paymentType: "全款",
      projectAccounting_receivingAccount: "**********...",
      projectAccounting_accountName: "城投地产",
      projectAccounting_recipient: "胡图图",
      projectAccounting_openingBank: "中信银行南城支行",
      projectAccounting_contractAmount: "200,000.00",
      projectAccounting_applicationAmount: "200,000.00",
      projectAccounting_remarks: "",
      projectAccounting_applicant: "胡图图",
      projectAccounting_applicationTime: "2025-11-10 12:00:00",
      projectAccounting_attachment: "查看",
      //合同列表
      contract_contractName: "资产包收购合同...",
      contract_contractNumber: "",
      contract_contractType: "收购合同",
      contract_contractStatus: "通过",
      contract_initiationMethod: "线下",
      contract_signatureCopies: "2",
      contract_sealType: "公章",
      contract_remarks: "--",
      contract_creator: "胡图图",
      contract_creationTime: "2025-11-10 12:00:00",
      contract_attachment: "查看",
      //会议列表
      conference_meetingTitle: "2024年第8次总经...",
      conference_decisionTime: "2025-11-01",
      conference_decisionNumber: "[2025] 8号",
      conference_decisionResult: "不同意收购...",
      conference_creator: "胡图图",
      conference_creationTime: "2025-11-10 12:00:00",
      conference_isApproved: "是",
      conference_remarks: "",
      conference_attachment: "查看",
      //竞争列表
      bidding_processTitle: "2024年第1次竞价...",
      bidding_projectName: "",
      bidding_biddingStatus: "竞价成功",
      bidding_productType: "对公",
      bidding_biddingMethod: "线下",
      bidding_bidAmount: "8,000,000.00",
      bidding_isSealed: "是",
      bidding_sealType: "公章",
      bidding_remarks: "",
      bidding_applicant: "胡图图",
      bidding_department: "",
      bidding_applicationTime: "2025-11-10 12:00:00",
      bidding_attachment: "查看",
    },
    {
      projectAccounting_processTitle: "资产包收购合同...",
      projectAccounting_accountStatus: "建账成功",
      projectAccounting_paymentRequestSerialNo: "********...",
      projectAccounting_paymentOrderStatus: "未支付",
      projectAccounting_paymentType: "全款",
      projectAccounting_receivingAccount: "**********...",
      projectAccounting_accountName: "城投地产",
      projectAccounting_recipient: "胡图图",
      projectAccounting_openingBank: "中信银行南城支行",
      projectAccounting_contractAmount: "200,000.00",
      projectAccounting_applicationAmount: "200,000.00",
      projectAccounting_remarks: "",
      projectAccounting_applicant: "胡图图",
      projectAccounting_applicationTime: "2024-11-11 12:00:00",
      projectAccounting_attachment: "查看",
      contract_contractName: "资产包收购合同...",
      contract_contractNumber: "",
      contract_contractType: "收购合同",
      contract_contractStatus: "未通过",
      contract_initiationMethod: "线上",
      contract_signatureCopies: "4",
      contract_sealType: "公章",
      contract_remarks: "--",
      contract_creator: "胡图图",
      contract_creationTime: "2024-11-11 12:00:00",
      contract_attachment: "查看",
      // conference_meetingTitle: "2024年第8次总经...",
      // conference_decisionTime: "2024-11-05",
      // conference_decisionNumber: "[2024] 8号",
      // conference_decisionResult: "同意收购...",
      // conference_creator: "胡图图",
      // conference_creationTime: "2024-11-11 12:00:00",
      // conference_isApproved: "否",
      // conference_remarks: "",
      // conference_attachment: "查看",
      // bidding_processTitle: "2024年第2次竞价...",
      // bidding_projectName: "",
      // bidding_biddingStatus: "竞价失败",
      // bidding_productType: "对公",
      // bidding_biddingMethod: "线上",
      // bidding_bidAmount: "10,000,000.00",
      // bidding_isSealed: "是",
      // bidding_sealType: "公章",
      // bidding_remarks: "",
      // bidding_applicant: "胡图图",
      // bidding_department: "",
      // bidding_applicationTime: "2024-11-11 12:00:00",
      // bidding_attachment: "查看",
    },
  ],
});

const meetingDecision = ref([]);
const biddingList = ref([]);
const contractList = ref([]);

// 1. 添加前缀工具
function addPrefixToFields(data, prefix) {
  return data.map((item) => {
    item.attachment = "查看";
    if (item.createTime) {
      item.createTime = dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss");
    }
    const newItem = {};
    for (const key in item) {
      newItem[`${prefix}${key}`] = item[key];
    }
    return newItem;
  });
}

// 2. 按索引“拉链合并”多个数组
function zipMergeByIndex(...lists) {
  const maxLen = Math.max(...lists.map((arr) => arr.length));
  const result = [];

  for (let i = 0; i < maxLen; i++) {
    const merged = {};
    lists.forEach((arr) => {
      if (arr[i]) {
        Object.assign(merged, arr[i]);
      }
    });
    result.push(merged);
  }

  return result;
}

// 获取并合并数据列表;
// function fetchAndZipMergeData(id) {
//   Promise.all([
//     getDetailList("selectMeetingDecision", id),
//     getDetailList("selectBiddingAcquisition", id),
//     getDetailList("selectListContract", id),
//   ])
//     .then(([confRes, bidRes, conRes]) => {
//       // 给每个模块的数据添加对应前缀
//       meetingDecision.value = addPrefixToFields(
//         confRes.rows || [],
//         "conference_"
//       );
//       biddingList.value = addPrefixToFields(bidRes.rows || [], "bidding_");
//       contractList.value = addPrefixToFields(conRes.rows || [], "contract_");

//       // 按数组索引合并到 dataList
//       data.value.dataList = zipMergeByIndex(
//         meetingDecision.value,
//         biddingList.value,
//         contractList.value
//       );

//       console.log("合并后的 dataList:", data.value.dataList);
//     })
//     .catch((err) => {
//       console.error("数据获取或合并失败：", err);
//     });
// }

// fetchAndZipMergeData();
export function fetchDataByType(pageType, id) {
  let apiName = "";
  let prefix = "";
  if (pageType === "contract") {
    apiName = "selectListContract";
    prefix = "contract_";
  } else if (pageType === "bidding") {
    apiName = "selectBiddingAcquisition";
    prefix = "bidding_";
  } else if (pageType === "conference") {
    apiName = "selectMeetingDecision";
    prefix = "conference_";
  } else {
    return Promise.resolve([]);
  }

  return getDetailList(apiName, id).then((res) => {
    const list = addPrefixToFields(res.rows || [], prefix);
    data.value.dataList = list;
    return list;
  });
}

export default data;
