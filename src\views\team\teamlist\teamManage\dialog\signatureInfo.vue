<template>
    <el-dialog v-model="open" :title="title" :before-close="cancel" width="650px" append-to-body>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="签章名称" prop="signatureName">
                <el-input v-model="form.signatureName" placeholder="请输入签章名称" />
            </el-form-item>
            <el-form-item label="签章ID" prop="signatureId">
                <el-input v-model="form.signatureId" placeholder="请输入签章ID" />
                <p>签章id来自于深圳CA提供，如无请联系产品</p>
            </el-form-item>
            <el-form-item label="状态" prop="stutas">
                <el-radio-group v-model="form.stutas">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="0">停用</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="notes">
                <el-input v-model="form.notes" type="textarea" rows="3" placeholder="请输入备注" maxlength="300"
                    show-word-limit />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="text-right">
                <el-button type="primary" @click="sumbit">确定</el-button>
                <el-button @click="cancel">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
// 全局变量
const { proxy } = getCurrentInstance()
const open = ref(false)
const title = ref('添加签章')
const data = reactive({
    form: {
        signatureName: undefined,
        signatureId: undefined,
        stutas: undefined,
    },
    rules: {
        signatureName: [{ required: true, message: '请输入', trigger: 'blur' }],
        signatureId: [{ required: true, message: '请输入', trigger: 'blur' }],
        stutas: [{ required: true, message: '请输入', trigger: 'blur' }],
    }
})
const { form, rules } = toRefs(data)
// 打开
function openDailog(data) {
    open.value = true
    form.value = data
    title.value = data.title
}

// 提交
function sumbit() {

}
// 取消
function cancel() {
    open.value = false
    proxy.resetForm('formRef')
    form.value = {
        signatureName: undefined,
        signatureId: undefined,
        stutas: undefined,
    }
}
defineExpose({ openDailog })
</script>