<template>
  <el-dialog
    title="接收机构信息"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="接收机构信息" prop="headers">
        <el-checkbox-group v-model="form.headers">
          <el-row :span="24">
            <el-col :span="24" :xs="24" v-for="item in headerArr" :key="item">
              <el-checkbox :label="item">{{ item }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="subloading" @click="cancel">确 认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const route = useRoute();

const headerArr = ref(['接收机构一','接收机构二','接收机构三','接收机构四']);
const subloading = ref(false);
const open = ref(false);
const data = reactive({
  form: {
    headers:['接收机构一','接收机构四']
  },
  rules: {
    headers: [{ required: true, message: "请勾选接收机构字段", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

 function opendialog(type, caseIds, condition) {
  reset();
  let policyId = route.params.id;
  open.value = true;
 }

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    headers:['接收机构一','接收机构四']
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
