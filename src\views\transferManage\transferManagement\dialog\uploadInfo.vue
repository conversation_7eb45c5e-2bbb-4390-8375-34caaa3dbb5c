<template>
    <el-dialog :title="dialogInfo.title" v-model="open" append-to-body width="650px" :before-close="cancel">
        <div class="header-msg">
            <el-button type="text" icon="Warning" />
            {{ dialogInfo.desc }}
        </div>
        <el-form :model="form" :rules="rules" label-width="120px">
            <el-form-item :label="dialogInfo.fileName" prop="fileUrl">
                <FileUpload drag autoUpload v-model:fileList="fileList" style="width: 600px;"
                    uploadFileUrl="/file/upload">
                    <template #drag-tip-msg>
                        <div class="tip-msg">支持扩展名：.rar .zip .doc .docx .pdf .jpg...</div>
                    </template>
                </FileUpload>
            </el-form-item>
            <el-form-item label="备注信息">
                <el-input v-model="form.remarks" type="textarea" :rows="5" placeholder="请输入" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div>
                <el-button @click="cancel">取消</el-button>
                <el-button @click="submit" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
const updateStep = ref(null)
const optFun = ref(null)
const fileName = ref(null)
const dialogInfo = ref({})
const open = ref(false)
const fileList = ref([])
const data = reactive({
    form: {},
    rules: {
        fileUrl: [{ required: true, message: '请上传文件', trigger: 'blur' }],
    }
})
const step = ref(1)
const { form, rules } = toRefs(data)
function submit() {
    updateStep.value && updateStep.value(step.value)
    optFun.value && optFun.value(fileName.value)
    setTimeout(() => {
        cancel()
    }, 200)
}
function openDialog(data) {
    open.value = true
    step.value = data.step || 1
    updateStep.value = data.updateStep
    dialogInfo.value = data.dialogInfo
    optFun.value = data.optFun
    fileName.value = data.fileName
}
function cancel() {
    open.value = false
    optFun.value = null
    updateStep.value = null
    fileName.value = null
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.header-msg {
    color: #409EFF;
    padding: 5px 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #409EFF;
    background-color: #409eff21;
}

.tip-msg {
    color: #8a8a8a;
}
</style>