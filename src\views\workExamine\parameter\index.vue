<template>
  <div class="app-container">
    <el-tabs class="mb8" v-model="activeTab">
      <el-tab-pane label="分案规则" :name="0">
        <caseRules v-if="activeTab == 0" />
      </el-tab-pane>
      <el-tab-pane label="分案节点" :name="1">
        <caseNode v-if="activeTab == 1" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup name="Template">
import caseRules from "./page/caseRules"
import caseNode from "./page/caseNode"
import { checkPermi } from "@/utils/permission";
const activeTab = ref(0);

//检测权限
function checkPower() {
  if (checkPermi(['workExamine:parameter:rules'])) {
    activeTab.value = 0;
  } else if (checkPermi(['workExamine:parameter:node'])) {
    activeTab.value = 1;
  } else {
    activeTab.value = undefined;
  }
}
checkPower()
</script>
<style lang="scss" scoped></style>
