<template>
  <div>
    <!-- 添加或修改对话框 -->
    <el-dialog
      title="重置密码"
      v-model="open"
      width="700px"
      :before-close="cancel"
      append-to-body
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账户密码" prop="password">
          <el-input
            v-model="form.password"
            placeholder="请输入密码，长度8-20位，同时包含数字、大小写字母及符号（除空格）"
            type="password"
            show-password
            style="width: 500px"
            oncut="return false"
            onpaste="return false"
            oncopy="return false"
            onkeyup="value=value.replace(/[^\x00-\xff]/g, '')"
            oninput="value=value.replace(/[^\x00-\xff]/g, '')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { resetPassword } from "@/api/team/team";
//全局配置
const { proxy } = getCurrentInstance();
//表单信息
const open = ref(false);
const loading = ref(false);
const emit = defineEmits(["getList"]);
const ids = ref([]);
//查询参数
const data = reactive({
  form: {
    password: undefined,
  },
  rules: {
    password: [
      {
        required: true,
        pattern: /^\S*$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },
      {
        required: true,
        pattern: /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$/,
        message: "长度8-20位，同时包含数字、大小写字母及符号（除空格）",
        trigger: "blur",
      },
    ],
  },
});
const { form, rules } = toRefs(data);
//开启弹窗
function opendialog(data) {
  ids.value = data;
  open.value = true;
}
//取消弹窗
function cancel() {
  reset();
  open.value = false;
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    password: undefined,
  };
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req = [];
      for (let i = 0; i < ids.value.length; i++) {
        req.push({ id: ids.value[i], password: form.value.password });
      }
      loading.value = true;
      resetPassword(req)
        .then((res) => {
          proxy.$modal.msgSuccess(`操作成功！`);
          cancel();
          emit("getList");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped></style>
