<template>
  <div class="warp">
    <div class="title">每日联系情况</div>
    <el-table :data="dataList" v-loading="loading" height="320px">
      <el-table-column label="催收员" prop="employeeName" align="center" />
      <el-table-column label="日均行动次数" prop="average" align="center" />
      <el-table-column label="行动日期" prop="dateRange" align="center" />
      <el-table-column label="总计" prop="contactTotal" align="center" />
    </el-table>
  </div>
</template>

<script setup>
import { dailyContactInfo } from "@/api/team/teamlist/operate";
const { proxy } = getCurrentInstance();
const props = defineProps({
  queryParams: {
    type: Object,
  },
});

const loading = ref(false);
const dataList = ref([]);

watch(
  () => props.queryParams,
  (newVal) => {
    getDailyContactInfo(newVal);
  }
);

onMounted(() => {
  getDailyContactInfo(props.queryParams);
});

//每日练习情况
function getDailyContactInfo(query) {
  loading.value = true;
  dailyContactInfo(query)
    .then((res) => {
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style scoped></style>
