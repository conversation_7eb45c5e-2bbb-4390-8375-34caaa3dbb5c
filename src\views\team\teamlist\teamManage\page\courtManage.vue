<template>
  <div class="app-container">
    <el-table :data="dataTable" v-loading="loading">
      <el-table-column
        label="法院名称"
        prop="agencyName"
        key="agencyName"
        align="center"
      />
      <el-table-column label="辖区" align="center">
        <template #default="{ row }">
          <span>{{ row.province + row.city }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="地址"
        prop="agencyAddress"
        key="agencyAddress"
        align="center"
      />
      <el-table-column label="关联文书" prop="number" key="number" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { getLawAgencyList } from "@/api/team/teamManage";

const { proxy } = getCurrentInstance();

const route = useRoute();

const dataTable = ref([]);
const total = ref(0);
const loading = ref(false);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});

const getList = () => {
  loading.value = true;
  getLawAgencyList({ teamId: route.params.teamId })
    .then((res) => {
      dataTable.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();
</script>

<style lang="scss" scoped></style>
