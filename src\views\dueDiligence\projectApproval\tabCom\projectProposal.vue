<template>
  <div class="mt20">
    <el-form inline label-width="auto" :model="approveData">
      <el-form-item label="项目ID">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectId"
          :options="projectIdList"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectName"
          :options="projectNameList"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item label="产品类型">
        <MultiSelect
          v-model="approveData.productId"
          style="width: 320px"
          placeholder="产品类型"
          :options="ductTypeList"
        />
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select
          v-model="queryParmas.approveState"
          style="width: 320px"
          placeholder="请选择立项状态"
        >
          <el-option
            v-for="item in [
              { value: '0', label: '待审核' },
              { value: '1', label: '审核中' },
              { value: '2', label: '已通过' },
              { value: '3', label: '未通过' },
              { value: '4', label: '已撤销' },
              { value: '5', label: '已作废' },
              { value: '6', label: '已退案关闭' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产转让方">
        <MultiSelect
          v-model="approveData.transferorId"
          style="width: 320px"
          placeholder="资产转让方"
           :options="transferorList"
        />
      </el-form-item>
      <el-form-item label="基准日">
        <el-date-picker
          v-model="approveData.baseDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="申请人">
        <MultiSelect 
          style="width: 320px"
           v-model="queryParmas.applicantIdList"
          placeholder="请选择申请人"
          :options="createByList"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="approveData.createTime"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="债权总金额">
        <div class="range-scope" style="width: 320px">
          <el-input v-model="approveData.totalDebt1" placeholder="最小金额" />
          <span>-</span>
          <el-input v-model="approveData.totalDebt2" placeholder="最大金额" />
        </div>
      </el-form-item>
      <el-form-item label="债权本金">
        <div class="range-scope" style="width: 320px">
          <el-input v-model="approveData.principal1" placeholder="最小金额" />
          <span>-</span>
          <el-input v-model="approveData.principal2" placeholder="最大金额" />
        </div>
      </el-form-item>
      <el-form-item label="投标方式">
        <el-select
          v-model="approveData.biddingMethod"
          style="width: 320px"
          placeholder="请选择投标方式"
        >
          <el-option
            v-for="item in tenderTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预计竞价日期">
        <el-date-picker
          v-model="approveData.planBiddingDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <!-- <el-form-item label="处理人">
        <MultiSelect 
          style="width: 320px"
           v-model="queryParmas.applicantIdList"
          placeholder="请选择申请人"
          :options="createByList"
        />
      </el-form-item>
      <el-form-item label="处理时间">
        <el-date-picker
          v-model="approveData.planBiddingDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item> -->
    </el-form>
    <div class="text-center">
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </div>
    <el-row class="mt20">
      <el-button
        type="primary"
        :disabled="!selectedArr.length"
        @click="handle(null, 0)"
        >通过</el-button
      >
      <el-button
        type="warning"
        :disabled="!selectedArr.length"
        @click="handle(null, 1)"
        >不通过</el-button
      >
    </el-row>
    <SelectedAll
      ref="selectedAllRef"
      :dataList="dataList"
      :selectedArr="selectedArr"
      v-model:allQuery="allQuery"
    />

    <el-tabs v-model="activetab" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.code"
        :label="item.info"
        :name="item.code"
      />
    </el-tabs>
    <el-table
      ref="multipleTableRef"
      :data="dataList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="44px"
        :selectable="selectable"
        align="right"
      />
      <el-table-column
        label="项目ID"
        width="120"
        prop="projectId"
        align="center"
      />
      <el-table-column
        label="项目名称"
        width="120"
        prop="projectName"
        align="center"
      />
      <el-table-column
        label="产品类型"
        width="120"
        prop="productName"
        align="center"
      />
      <el-table-column
        label="审核状态"
        width="120"
        prop="examineState"
        align="center"
      >
        <template #default="{ row }">
          {{ projectApproval.approveStatusEnum[row.examineState] }}
        </template>
      </el-table-column>
      <el-table-column
        label="资产转让方"
        width="120"
        prop="transferor"
        align="center"
      />
      <el-table-column
        label="债权总金额（元）"
        width="160"
        prop="totalDebt"
        align="center"
      />
      <el-table-column
        label="债权本金（元）"
        width="120"
        prop="principal"
        align="center"
      />
      <el-table-column
        label="户数"
        width="120"
        prop="householdCount"
        align="center"
      />
      <el-table-column
        label="基准日"
        width="120"
        prop="baseDate"
        align="center"
      />
      <el-table-column
        label="预计竞价日期"
        width="120"
        prop="planBiddingDate"
        align="center"
      />
      <el-table-column
        label="投标方式"
        width="120"
        prop="biddingMethod"
        align="center"
      />
      <el-table-column
        label="报价上限"
        width="120"
        prop="priceLimit"
        align="center"
      />
      <el-table-column label="资产评估表" width="120" align="center">
        <template #default="{ row }">
          <el-button type="text" @click="handleCheck(row, 1)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="立项报告"
        width="120"
        prop="projectReport"
        align="center"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleCheck(row, 0)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="其他附件"
        width="120"
        prop="otherAttachments"
        align="center"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleCheck(row, 2)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="申请人"
        width="120"
        prop="createBy"
        align="center"
      />
      <el-table-column
        label="申请时间"
        width="120"
        prop="createTime"
        align="center"
      />
      <el-table-column
        label="处理状态"
        width="120"
        prop="approveStart"
        align="center"
      >
        <template #default="{ row }">
          {{ formatApproveStart(row.approveStart) }}
        </template>
      </el-table-column>
      <el-table-column
        label="处理人"
        width="120"
        prop="reviewer"
        align="center"
      />
      <el-table-column
        label="处理时间"
        width="120"
        prop="approveTime"
        align="center"
      />
      <el-table-column width="180" fixed="right" label="操作" align="center">
        <template #default="{ row }">
          <el-button v-if="row.approveStart == 0" type="text" @click="handle(row, 0)"
            >通过</el-button
          >
          <el-button v-if="row.approveStart == 0" type="text" @click="handle(row, 1)"
            >不通过</el-button
          >
          <el-button type="text" @click="handleDetails(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParmas.pageNum"
      v-model:limit="queryParmas.pageSize"
      @pagination="getList"
    />
    <CheckFileDialog ref="checkFileDialogRef" />
    <Pass ref="passRef" @submit="handleSubmit" />
  </div>
</template>

<script setup>
import Pass from "../dialog/pass.vue";

import {
  getProductType,
  getTenderType,
  getProjectId,
  getProjectName,
  getUserInfo
} from "@/api/dueDiligence/startNapeList";
import { assetOwnerTree } from "@/api/assets/assetside";

import { getNapeListByIdAndType } from "@/api/dueDiligence/startNapeList";
import CheckFileDialog from "../../startNapeList/dialog/checkFile.vue";
import ProjectApproval from "../index.js";
import { formatTime } from "@/utils/common";
import { fillEmptyToDash } from "@/api/conference/utils";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

// 创建ProjectApproval实例
const projectApproval = new ProjectApproval();

// 响应式数据，用于模板绑定
const activetab = ref(projectApproval.activetab);
const total = ref(projectApproval.total);
const queryParmas = ref({
  approveCode: "initiation",
  ...projectApproval.queryParmas,
});
const approveData = ref(projectApproval.approveData);
const dataList = ref(projectApproval.dataList);
const tabList = ref(projectApproval.tabList);
const selectedArr = ref([]);
const allQuery = ref(false);
const ductTypeList = ref([]); // 产品类型
const transferorList = ref([]); // 资产转让方
const tenderTypeList = ref([]); // 投标方式
const projectIdList = ref([]);
const createByList = ref([]);//申请人

const projectNameList = ref([]);
const passRef = ref(null);
// 模拟数据，可以删除或保留
const mockDataList = ref([]);

const loading = ref(false);

const rangFields = ["baseDate", "createTime", "planBiddingDate"]; //时间处理字段

// 表格主要字段，用于空值替换
const tableProps = [
  'projectId',
  'projectName',
  'productName',
  'examineState',
  'transferor',
  'totalDebt',
  'principal',
  'householdCount',
  'baseDate',
  'planBiddingDate',
  'biddingMethod',
  'priceLimit',
  'createBy',
  'createTime',
];

projectApproval.queryParmas.approveCode = "initiation";

// 使用类的方法
const formatApproveStart = (value) => {
  return projectApproval.formatApproveStart(value);
};

const handleQuery = () => {
  projectApproval.queryParmas.pageNum = 1;
  getList();
};

const handle = (row = null, type) => {
  let req = {
    approveCode: "initiation",
    allQuery: false,
  };
  if (row !== null) {
    req.approveIds = [row.approveId];
    passRef.value.opendialog(req, type);
  } else {
    if (allQuery.value) {
      req.allQuery = true;
      req.approvePageRequest = JSON.parse(
        JSON.stringify(proxy.newAddFieldsRange(approveData.value, rangFields))
      );
    } else {
      req.approveIds = selectedArr.value.map((item) => item.approveId);
    }
    passRef.value.opendialog(req, type);
  }
};

const handleSubmit = (req) => {
  if (req.type == 0) {
    passRef.value.close();
    projectApproval.handleApprove(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        passRef.value.close();
        getList();
      }
    });
  } else {
    projectApproval.handleReject(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        passRef.value.close();
        getList();
      }
    });
  }
};

function handleDetails(row) {
  const query = {
    path: route.path,
    pageType: "startNapeList",
    progressStatus: 0,
    isDetails: 1,
    rowData: JSON.stringify(row),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

const selectable = (row) => {
  return projectApproval.selectable(row);
};

const resetQuery = () => {
  loading.value = true;
  projectApproval.resetQuery(({ dataList: newDataList, total: newTotal }) => {
    dataList.value = newDataList;
    total.value = newTotal;
    // 重置响应式数据
    queryParmas.value = projectApproval.queryParmas;
    approveData.value = projectApproval.approveData;
    loading.value = false;
  });
};

const getList = () => {
  projectApproval.queryParmas.approveCode = "initiation";
  projectApproval.approveData = JSON.parse(
    JSON.stringify(
      proxy.newAddFieldsRange(projectApproval.approveData, rangFields)
    )
  );
  loading.value = true;
  projectApproval
    .getList(({ dataList: newDataList, total: newTotal }) => {
      dataList.value = fillEmptyToDash(newDataList, tableProps);
      total.value = newTotal;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 标签页切换处理
const handleTabChange = (tabName) => {
  loading.value = true;
  projectApproval.changeTab(
    tabName,
    ({ dataList: newDataList, total: newTotal }) => {
      dataList.value = newDataList;
      total.value = newTotal;
      activetab.value = tabName;
      loading.value = false;
    }
  );
};

function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      projectIdList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      projectNameList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

function getProductTypeFun() {
  getProductType().then((res) => {
    ductTypeList.value = res.data.map((item) => ({
      value: item.code,
      label: item.info,
    }));
  });
}

function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      transferorList.value = res.data.map((item) => ({
        value: item.id.split(":")[1],
        label: item.label,
      }));
    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}
function getTenderTypeFun() {
  getTenderType()
    .then((res) => {
      tenderTypeList.value = res.data.map((item) => ({
        value: item.dictLabel || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}
const handleSelectionChange = (val) => {
  selectedArr.value = val;
};
// 同步响应式数据到类实例
const syncDataToInstance = () => {
  projectApproval.updateApproveData(approveData.value);
  projectApproval.updateQueryParams(queryParmas.value);
};

// 监听数据变化，同步到类实例
watch(
  approveData,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

watch(
  queryParmas,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

const handleCheck = (row, type) => {
  getNapeListByIdAndType({
    id: row.id,
    fileType: type,
  }).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      proxy.$refs["checkFileDialogRef"].openDialog(res.data);
    } else {
      proxy.$modal.msgWarning("未上传文件");
    }
  });
};

// 获取申请人下拉数据
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("接口返回数据格式错误:", res.data);
        return;
      }
      const userOptions = res.data.map((item) => ({
        value: item.code,
        label: item.info,
      }));
      createByList.value = userOptions;
    })
    .catch((error) => {
      console.error("获取用户信息失败:", error);
    });
}

onMounted(() => {
  // 初始化时使用模拟数据，实际使用时可以调用getList()
  dataList.value = mockDataList.value;
  total.value = mockDataList.value.length;
  getProductTypeFun();
  getTreeselectFun();
  getTenderTypeFun();
  getProjectIdFun();
  getProjectNameFun();
  getUserInfoFun()
  getList();
});
</script>

<style lang="scss" scoped>
.range-scope {
  display: flex;

  span {
    margin: 0 10px;
  }
}
</style>
