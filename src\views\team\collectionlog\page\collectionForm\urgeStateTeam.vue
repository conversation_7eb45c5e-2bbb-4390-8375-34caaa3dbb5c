<template>
  <div class="warp">
    <div class="title">催收状态分布--委外机构</div>
    <el-table :data="dataList" v-loading="loading" height="400px">
      <template v-for="item in dictDataList" :key="item.value">
        <el-table-column
          v-if="item.value == 'searchingProportion'"
          label="查找占比"
          prop="searchingProportion"
          align="center"
        >
          <template #default="{ row }">
            {{ row.searchingProportion + " %" }}
          </template>
        </el-table-column>
        <el-table-column v-else :label="item.title" :prop="item.value" align="center" />
      </template>
    </el-table>
    <div id="UrgeStateTeam" class="mt20"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { urgeStateDistribution } from "@/api/team/collectionlog";
const { proxy } = getCurrentInstance();
const props = defineProps({
  queryParams: {
    type: Object,
  },
});

const loading = ref(false);
const dataList = ref([]);
const echartsOptions = ref({
  title: {
    text: "催收状态-委外机构",
    x: "center",
    textStyle: {
      textAlign: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999",
      },
    },
  },
  toolbox: {
    feature: {
      magicType: { show: true, type: ["line", "bar"] },
      restore: { show: true },
      saveAsImage: { show: true },
    },
    padding: [0, 20, 0, 0],
  },
  dataZoom: [
    {
      type: "inside",
    },
    {
      type: "slider",
    },
  ],
  legend: {
    orient: "vertical",
    left: "right",
    top: "center",
    align: "left",
    padding: [0, 40, 0, 0],
    data: [],
  },
  grid: {
    left: "3%",
    right: "15%",
    bottom: "90",
    containLabel: true,
  },
  xAxis: [
    {
      type: "category",
      data: [],
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        interval: 0,
        rotate: -30,
      },
    },
  ],
  yAxis: [
    {
      type: "value",
    },
    {
      type: "value",
      name: "查找占比",
      min: 0,
      max: 100,
      interval: 20,
      axisLabel: {
        formatter: "{value} %",
      },
    },
  ],
  series: [],
});
const dictDataMap = ref({});
const dictDataList = ref([]);
const histogramData = ref({});

watch(
  () => props.queryParams,
  (newVal) => {
    getUrgeStateDistribution(newVal);
  }
);

watch(
  () => [dictDataList.value, histogramData.value],
  (newVal) => {
    let dictData = newVal[0];
    let echartsData = newVal[1];
    let series = [];
    let legend = [];
    for (let i = 0; i < dictData.length; i++) {
      const item = dictData[i];
      let option = {};
      let isOld = false; //是否存在
      if (item.value == "employeeName") {
        echartsOptions.value.xAxis[0].data = echartsData.employeeName || []; //横坐标
        continue;
      }
      legend.push(item.title);

      for (let j = 0; j < echartsOptions.value.series.length; j++) {
        if (echartsOptions.value.series.name == item.title) {
          //已存在
          isOld = true;
          break;
        }
      }
      if (!isOld) {
        //查找占比
        if (item.value == "searchingProportion") {
          option = {
            name: item.title,
            type: "line",
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: (value) => value + "%",
            },
            data: echartsData.searchingProportion || [],
            lineStyle: {
              width: 2,
              type: "dotted",
            },
          };
        } else {
          option = {
            name: item.title,
            type: "bar",
            stack: "wtf",
            emphasis: {
              focus: "series",
            },
            data: echartsData[item.value] || [],
          };
        }
      } else {
        option = {
          name: item.title,
          data: echartsData.searchingProportion || [],
        };
      }
      series.push(option);
    }
    echartsOptions.value.series = series;
    echartsOptions.value.legend.data = legend;
    nextTick(() => {
      echartsInit();
    });
  }
);

onMounted(() => {
  getUrgeStateDistribution({});
});

//初始化echarts
function echartsInit() {
  var chartDom = document.getElementById("UrgeStateTeam");
  var myChart = echarts.init(chartDom);
  myChart.setOption(echartsOptions.value);
  window.onresize = function () {
    myChart.resize();
  };
}

//每日练习情况
function getUrgeStateDistribution(query) {
  loading.value = true;
  urgeStateDistribution(query)
    .then((res) => {
      dataList.value = res.data.tableData;
      dictDataMap.value = res.data.dictDataMap || {};
      dictDataList.value = res.data.dictDataList || [];
      histogramData.value = res.data.histogramData || [];
    })
    .finally(() => {
      loading.value = false;
    });
}

//echarts
</script>

<style scoped>
#UrgeStateTeam {
  height: 400px;
  width: 100%;
}
</style>
