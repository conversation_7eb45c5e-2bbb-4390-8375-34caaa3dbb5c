<template>
  <div>
    <verify :dataList="dataList" :edit="false" />
  </div>
</template>

<script setup>
import verify from "@/views/team/components/verifyProcess/index.vue";
import { selectApprovalSet } from "@/api/team/teamManage.js";

const route = useRoute();
const { proxy } = getCurrentInstance();

const dataList = ref([]);

//获取列表数据
function getList() {
  selectApprovalSet(route.params.teamId).then((res) => {
    dataList.value = res.data;
  });
}

provide("getList", getList);
getList();
</script>

<style scoped></style>
