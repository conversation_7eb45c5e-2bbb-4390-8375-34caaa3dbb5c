<template>
  <el-dialog
    title="资产数据"
    v-model="open"
    width="750px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="资产批次号" prop="headers">
        <el-checkbox-group v-model="form.headers">
          <el-row :span="24">
            <el-col :span="12" :xs="24" v-for="item in batchs" :key="item.info">
              <el-checkbox :label="item.info">{{ item.info }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="subloading" @click="cancel">确 认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getBatchNums } from "@/api/assets/casemanage.js";
const { proxy } = getCurrentInstance();

const batchs = ref([]);
const subloading = ref(false);
const open = ref(false);
const data = reactive({
  form: {
    headers:[]
  },
  rules: {
    headers: [{ required: true, message: "请勾选接收方字段", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

function opendialog() {
    reset();
    open.value = true;
}

 //获取资产批次号
function BatchList() {
  getBatchNums().then((res) => {
    batchs.value = res.data;
  });
}
BatchList()

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    headers: [],
  };
}

//取消
function cancel() {
  reset();
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
