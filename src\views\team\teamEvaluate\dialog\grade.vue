<template>
  <el-dialog title="填写评分" v-model="open" width="800px" append-to-body @close="cancel">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="评估时间区间:" prop="collectionTargets">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          value-format="YYYY-MM-DD"
          style="width=90px"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getRepayScore()"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-table style="margin-left: 0px" v-loading="loading" :data="gradeList">
          <el-table-column prop="scoringItems" label="评分项" width="180" />
          <el-table-column label="评分" fixed="right" width="180px">
             <template #default="{row,$index}">
              <el-form-item>
                <el-input
                  v-model="row.score"
                  type="text"
                  class="topbut botbut"
                  :onblur="(e)=>formatterScore(e,$index,'score')"
                  placeholder="请输入评分"
                  @blur="changeGrade"
                  :disabled="$index == 0"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="扣分" fixed="right" width="180px">
             <template #default="{row}">
              <el-input
                v-model="row.deduction"
                type="text"
                class="topbut botbut"
                placeholder="请输入"
                :onblur="(e)=>formatterScore(e,$index,'deduction')"
                @blur="changeGrade"
                v-if="$index !== 0"
              />
            </template>
          </el-table-column>
          <el-table-column label="备注" fixed="right" width="180px">
             <template #default="{row}">
              <el-input v-model="row.remarks" placeholder="请输入" />
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-left: 12px; margin-top: 5px">
          <span>综合评分：</span>
          <span>{{ allGrade }}</span>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >生成评价表</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { insertEvaluationForm ,selectRepaymentScore} from "@/api/team/teamevaluate";
import { number } from "echarts";
const { proxy } = getCurrentInstance();
const open = ref(false);
const emit = defineEmits(["getList"]);
const teamObj = ref({});
const data = reactive({
  form: {
    time: [],
  },
  rules: {
    gradeRule: [{ required: true, message: "请填入评分", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);
const subloading = ref(false);
const loading = ref(false);
//评分
const allGrade = ref(100);
const gradeList = ref([
  {
    scoringItems: "回款率达成(满分40)",
    score: 0,
    remarks: "",
    deduction:""
  },
  {
    scoringItems: "合规作业(满分20)",
    score: 20,
    remarks: "",
    deduction:""
  },
  {
    scoringItems: "案件投诉(满分20)",
    score: 20,
    remarks: "",
    deduction:""
  },
  {
    scoringItems: "个人信息安全(满分20)",
    score: 20,
    remarks: "",
    deduction:""
  },
]);

function formatterScore(e,index,key){
  let formatterValue = e.target.value.replace(/[\+\-]/g,'')
  if(formatterValue == ''){

    return key == "deduction"?'':proxy.$modal.msgWarning(`请输入${['合规作业','案件投诉','个人信息安全'][index-1]}评分`);;
  }
  let reg = /^[0-9]+(\.[0-9]{1,2})?$|^0+[0-9]+(\.[0-9]{1,2})?$/
  if(!reg.test(formatterValue)){
    gradeList.value[index][key] = '';
    return proxy.$modal.msgWarning(`${['合规作业','案件投诉','个人信息安全'][index-1]}请输入正确的格式,最多保留两位小数`);
  }
  return formatterValue;
}
//提交
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      let req = {
        recordTime1: form.value.time?.[0] || undefined,
        recordTime2: form.value.time?.[1] || undefined,
        teamId: teamObj.value.teamId,
        serviceQuality:gradeList.value[0].score,
        serviceDeductPoints: gradeList.value[0].deduction,
        serviceRemarks: gradeList.value[0].remarks,
        informationSafety:gradeList.value[1].score,
        informationDeductPoints: gradeList.value[1].deduction,
        informationRemarks: gradeList.value[1].remarks,
        complianceManagement:gradeList.value[2].score,
        complianceDeductPoints:gradeList.value[2].deduction,
        complianceRemarks: gradeList.value[2].remarks,
        personalScore: gradeList.value[3].score,
        personalDeductPoints:gradeList.value[3].deduction,
        personalRemarks:gradeList.value[3].remarks,
        comprehensiveScore: allGrade.value,
      };
      insertEvaluationForm(req)
        .then((res) => {
          subloading.value = false;
          proxy.$modal.msgSuccess("提交成功，结果请查看机构评价表页面！");
          cancel();
          getList();
        })
        .catch(() => {
          subloading.value = false;
        });
    }
  });
}

//改变总分
function changeGrade() {
  let total = 0;
  gradeList.value.forEach((item, index) => {
    //验证不能小于0
    if(item.score < 0 || item.score.length <= 0){
      item.score = "";
      return proxy.$modal.msgWarning("评分不能小于0");
    }else if(item.deduction < 0){
      item.deduction = "";
      return proxy.$modal.msgWarning("扣分不能小于0");
    }
    //判断评分
    if (index == 0 && item.score > 40) {
      item.score = "";
      return proxy.$modal.msgWarning("回款率达成分数不能大于40");
    }else if(index > 0 && item.score > 20) {
      item.score = "";
      return proxy.$modal.msgWarning(`${['合规作业','案件投诉','个人信息安全'][index-1]}分数不能大于20`);
    }
    //判断扣分
    if(index > 0 && item.deduction > 20) {
      item.deduction = "";
      return proxy.$modal.msgWarning(`${['合规作业','案件投诉','个人信息安全'][index-1]}扣分不能大于20`);
    }
    if(index > 0 && Number(item.deduction) > Number(item.score)) {
      item.deduction = "";
      return proxy.$modal.msgWarning(`${['合规作业','案件投诉','个人信息安全'][index-1]}扣分不能大于所评的分数`);
    }
  });
  gradeList.value.forEach((item, index) =>{
    total += item.score.length == 0 ? 0 : Number(item.score);
    total -= item.deduction.length == 0 ? 0 : Number(item.deduction); 
  })
  allGrade.value = Number(total).toFixed(2);
}

//重置
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    time: [],
  };
  gradeList.value = [
    {
      scoringItems: "回款率达成(满分40)",
      score: 0,
      remarks: "",
      deduction:""
    },
    {
      scoringItems: "合规作业(满分20)",
      score: 20,
      remarks: "",
      deduction:""
    },
    {
      scoringItems: "案件投诉(满分20)",
      score: 20,
      remarks: "",
      deduction:""
    },
    {
      scoringItems: "个人信息安全(满分20)",
      score: 20,
      remarks: "",
      deduction:""
    }
  ];
}

//获取用户回款分数
function getRepayScore(){
  let req = {
    recordTime1: form.value.time?.[0] || undefined,
    recordTime2: form.value.time?.[1] || undefined,
    teamId: teamObj.value.teamId,
  }
  selectRepaymentScore(req).then((res) =>{
    gradeList.value[0].score = res.data || 0;
    changeGrade()
  }).catch(() =>{
    gradeList.value[0].score = res.data || 0;
  })
}

function opendialog(data, query) {
  open.value = true;
  teamObj.value = data;
  form.value.time = query.recordTime;
  getRepayScore();
}
function cancel() {
  reset();
  open.value = false;
}
defineExpose({
  opendialog,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0px !important;
}
:deep(.el-form-item__error) {
  position: unset;
}
</style>
<style>
.topbut input::-webkit-outer-spin-button,
.topbut input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.topbut input[type="number"] {
  -moz-appearance: textfield;
}
.botbut inpit {
  border: none;
}
</style>
