<template>
  <div>
    <div class="title mb20">
      <span>开启后，所有的登录账号必须实名认证后才能登录。</span>
      <el-switch
        class="myswitch ml20"
        v-model="state.authenticationStatus"
        active-color="#2ECC71"
        :active-value="1"
        :inactive-value="0"
        active-text="开"
        inactive-text="关"
        @change="change"
      ></el-switch>
    </div>
    <div v-if="state.authenticationStatus">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-input v-show="false" />
        <el-form-item prop="value">
          <el-input
            v-model="queryParams.value"
            placeholder="请输入搜索关键词"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
          <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain :disabled="single || loading" @click="manMadePass"
            >人工通过</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
          :columns="columns"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="30" align="right" />
        <el-table-column
          label="所属公司"
          align="center"
          key="createName"
          prop="createName"
          show-overflow-tooltip
          v-if="columns[0].visible"
        />
        <el-table-column
          label="所属账号"
          align="center"
          key="loginAccount"
          prop="loginAccount"
          v-if="columns[1].visible"
        />
        <el-table-column
          label="成员姓名"
          align="center"
          key="employeeName"
          prop="employeeName"
          v-if="columns[2].visible"
        />
        <el-table-column
          label="工号"
          align="center"
          key="employeesWorking"
          prop="employeesWorking"
          v-if="columns[3].visible"
        />
        <el-table-column
          label="手机号码"
          align="center"
          key="phoneNumber"
          prop="phoneNumber"
          v-if="columns[4].visible"
        />
        <el-table-column
          label="证件号码"
          align="center"
          key="identityCard"
          prop="identityCard"
          v-if="columns[5].visible"
        />
        <el-table-column
          label="邮箱"
          align="center"
          key="mailbox"
          prop="mailbox"
          show-overflow-tooltip
          v-if="columns[6].visible"
        />
        <el-table-column
          label="状态"
          align="center"
          key="state"
          prop="state"
          :formatter="stateChange"
          v-if="columns[7].visible"
        />
      </el-table>
      <pagination
        v-show="total >= 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
import {
  changeSafeStatus,
  selectAuthentication,
  manualCertification,
} from "@/api/team/team";
const route = useRoute();
const { proxy } = getCurrentInstance();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);

const showSearch = ref(true);
const total = ref(0);
const single = ref(true);
const loading = ref(false);
const dataList = ref([]);
const ids = ref([]);
const userIds = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createId: route.params.teamId,
    value: undefined,
  },
});
const manMadeForm = ref([]);

const columns = ref([
  { key: 0, label: `所属公司`, visible: true },
  { key: 1, label: `所属账号`, visible: true },
  { key: 2, label: `成员姓名`, visible: true },
  { key: 3, label: `工号`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `证件号码`, visible: true },
  { key: 6, label: `邮箱`, visible: true },
  { key: 7, label: `状态`, visible: true },
]);

const { queryParams } = toRefs(data);

//获取列表
function getList() {
  loading.value = true;
  selectAuthentication(queryParams.value)
    .then((res) => {
      dataList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//开关设置
function change() {
  changeSafeStatus(state.value)
    .then(() => {})
    .catch(() => {
      getTeamSafe();
    });
}

//搜索
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    createId: route.params.teamId,
    value: undefined,
  };
  getList();
}

//选择表格
function handleSelectionChange(selection) {
  single.value = !(selection.length > 0);
  let arr = [];
  let teamId = route.params.teamId;
  selection.map((item) => {
    arr.push({
      createId: teamId,
      userId: item.userId,
      id: item.id || undefined,
    });
  });
  manMadeForm.value = arr;
}

//状态转换 0-未验证,1-已验证,2-验证失败,3-人工通过
function stateChange(row) {
  return ["未验证", "已验证", "验证失败", "人工通过"][row.state] || "--";
}

//人工通过
function manMadePass() {
  proxy.$modal
    .confirm("是否人工通过所选账号的实名认证！？")
    .then(function () {
      loading.value = true;
      return manualCertification(manMadeForm.value);
    })
    .then(() => {
      proxy.$modal.msgSuccess("操作成功！");
      getList();
    })
    .catch(() => {loading.value = false;})
}
</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
