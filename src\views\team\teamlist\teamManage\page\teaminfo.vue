<template>
  <div style="width: 80%; margin: 0 auto">
    <el-form :model="form" :rules="rules" ref="teamRef" label-width="116px">
      <el-row>
        <el-col :span="12" :xs="24">
          <el-form-item label="机构名称" prop="cname">
            <el-input
              :disabled="isEdit"
              v-model="form.cname"
              placeholder="请输入机构名称"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="联系人手机号码" prop="contact">
            <el-input
              :disabled="isEdit"
              v-model="form.contact"
              placeholder="请输入公司联系人手机号码"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" :xs="24">
          <el-form-item label="一级类别" prop="teamLevelType">
            <el-radio-group :disabled="isEdit" v-model="form.teamLevelType">
              <el-radio
                v-for="item in categoryOptions"
                :key="item.teamType"
                :label="item.teamType"
                >{{ item.teamType }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" :xs="24">
          <el-form-item label="机构类别" prop="category">
            <el-radio-group :disabled="isEdit" v-model="form.category">
              <el-radio
                v-for="item in changelawyer(form.teamLevelType)"
                :key="item"
                :label="item"
                >{{ item }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="机构类型" prop="teamType">
            <el-select
              v-model="form.teamType"
              placeholder="请选择"
              style="width: 220px"
              :disabled="isEdit"
            >
              <el-option
                v-for="(item, index) in teamTypeList"
                :key="index"
                :label="item.info"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="座机号码" prop="machine">
            <el-input
              :disabled="isEdit"
              v-model="form.machine"
              placeholder="请输入座机号码"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="登录账号" prop="account">
            <el-input
              :disabled="true"
              v-model="form.account"
              placeholder="请输入登录账号"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="联系人邮箱" prop="email">
            <el-tooltip
              v-if="form.email?.length > 28 && isEdit"
              class="box-item"
              effect="dark"
              :content="form.email"
              placement="top"
            >
              <el-input
                :disabled="isEdit"
                v-model="form.email"
                placeholder="请输入联系人邮箱"
                style="width: 220px"
              ></el-input>
            </el-tooltip>
            <el-input
              v-else
              :disabled="isEdit"
              v-model="form.email"
              placeholder="请输入联系人邮箱"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="子账号个数" prop="numbers">
            <el-input-number
              :disabled="isEdit"
              v-model="form.numbers"
              controls-position="right"
              :min="0"
              style="width: 220px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="登陆标识" prop="loginId">
            <el-input
              :disabled="isEdit"
              v-model="form.loginId"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="合作状态" prop="cooperation">
            <el-select
              v-model="form.cooperation"
              placeholder="请选择"
              style="width: 220px"
              :disabled="isEdit"
            >
              <el-option label="合作中" :value="0"></el-option>
              <el-option label="合作暂停" :value="1"></el-option>
              <el-option label="合作关闭" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="保证金" prop="margin">
            <el-input
              :disabled="isEdit"
              v-model="form.margin"
              v-inputMoney
              placeholder="请输入"
              style="width: 220px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="合作开始时间" prop="starts">
            <el-date-picker
              :disabled="isEdit"
              v-model="form.starts"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择时间"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="合同到期时间" prop="complete">
            <el-date-picker
              :disabled="isEdit"
              v-model="form.complete"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择时间"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="企业编码" prop="companyNum">
            <el-tooltip effect="light" placement="bottom-start">
              <div class="table-title-question">
                <svg-icon class="hint-item" icon-class="question" />
              </div>
              <template #content>
                <div class="info-tip">
                  <p>注：该企业编码为对接呼叫中心的企业编码</p>
                </div>
              </template>
            </el-tooltip>
            <el-input
              :disabled="isEdit"
              v-model="form.companyNum"
              placeholder="请输入"
              @input="(value) => (form.companyNum = value.replace(/[^a-zA-Z0-9]/g, ''))"
              style="width: 206px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="佣金比" prop="commissionRatio">
            <el-input
              v-inputMoney
              :disabled="isEdit"
              v-model="form.commissionRatio"
              placeholder="请输入"
              style="width: 200px"
            ></el-input>
            <div>%</div>
          </el-form-item>
        </el-col>
        <el-col :span="24" :xs="24">
          <el-form-item label="机构描述" prop="describes">
            <el-input
              :disabled="isEdit"
              v-model="form.describes"
              type="textarea"
              maxlength="100"
              show-word-limit
              placeholder="请输入内容"
              style="width: 600px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" :xs="24">
          <el-form-item label="机构地址" prop="outsideAddress">
            <el-input
              :disabled="isEdit"
              ref="InputRef"
              :id="isEdit ? '' : 'suggestId'"
              v-model="form.outsideAddress"
              placeholder="请输入地址"
              style="width: 600px"
            />
            <div
              v-if="!isEdit"
              :id="isEdit ? '' : 'container'"
              class="mt5"
              style="width: 620px; height: 360px"
            ></div>
          </el-form-item>
        </el-col>
        <el-col :span="24" :xs="24">
          <div class="upload-label">委托合同及相关凭证</div>
          <el-form-item>
            <el-upload
              ref="uploadRef"
              :disabled="isEdit"
              v-if="!isEdit"
              multiple
              :limit="5"
              accept=".rar, .zip, .doc, .docx, .pdf"
              :headers="upload.headers"
              :action="upload.url"
              :before-upload="handleFileUploadBefore"
              :on-change="handleEditChange"
              :before-remove="handleRemove"
              :on-success="handleFileSuccess"
              :auto-upload="false"
              :file-list="fileList"
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  <span
                    >支持格式：.rar .zip .doc .docx .pdf
                    ，单个文件不能超过20MB，最多不能超过5个文件</span
                  >
                </div>
                <el-button type="success" @click="submitFile">上传到服务器</el-button>
              </template>
            </el-upload>
            <div class="file-download" v-if="isEdit">
              <div
                class="file-box"
                v-for="(item, index) in dowmloadFileList"
                :key="index"
              >
                <el-icon class="mt5" color="#909399" :size="16"><Tickets /></el-icon>
                <span class="file-name">{{ item.firstName }}</span>
                <el-button
                  type="primary"
                  link
                  class="mt5"
                  style="float: right"
                  @click="downFileByLink(item.firstName, item.fileUrl)"
                  >点击下载</el-button
                >
              </div>
              <div class="none-file" v-if="dowmloadFileList.length == 0">
                当前用户没有上传委托合同及相关凭证
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="text-center">
      <el-button v-if="isEdit" type="primary" @click="isEdit = false"
        >编辑机构资料</el-button
      >
      <div v-else>
        <el-button type="primary" :loading="subloading" @click="saveteamInfo"
          >保存机构资料</el-button
        >
        <el-button @click="cancle">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {
  teamInfo,
  deleteTeamFile,
  addTeamFiles,
  editTeamInfo,
  selectDictData,
  OrganizationType,
} from "@/api/team/team";
const { proxy } = getCurrentInstance();
const route = useRoute();
const isEdit = ref(true);
const files = ref([]);
const dowmloadFileList = ref([]);
const subloading = ref(false);
const teamTypeList = ref([]);
const lawyerOptions = ref([]);

const showInput = ref(false);

const InputRef = ref(null);

// 座机验证
const validateMachine = (rule, value, callback) => {
  var reg = /^\d{3,4}[-]\d{7,8}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入座机号码！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的座机号码！"));
  } else {
    callback();
  }
};

//手机号码验证
const validatePhone = (rule, value, callback) => {
  var reg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入手机号码！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的手机号码！"));
  } else {
    callback();
  }
};

//邮箱验证
const validateEmail = (rule, value, callback) => {
  var reg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,5}$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入邮箱！"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入正确的邮箱！"));
  } else {
    callback();
  }
};

//账号验证
const validateAccount = (rule, value, callback) => {
  let reg = /^[a-zA-Z_][a-zA-Z0-9_]{5,14}/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入登陆账号！"));
  } else if (value.length < 6 || value.length > 15) {
    callback(new Error("请保持登陆账号长度为6～15位！"));
  } else if (!reg.test(value)) {
    callback(new Error("由字母.下划线.数字组成,不能以数字开头!"));
  } else {
    callback();
  }
};
const data = reactive({
  form: {
    id: undefined,
    cname: undefined,
    contact: undefined,
    category: undefined,
    machine: undefined,
    account: undefined,
    email: undefined,
    numbers: undefined,
    loginId: undefined,
    cooperation: undefined,
    margin: undefined,
    starts: undefined,
    complete: undefined,
    companyNum: undefined,
    describes: undefined,
    teamType: undefined,
    teamLevelType: undefined,
    outsideAddress: undefined,
    longitudeAtitudeStart: undefined,
  },
  rules: {
    category: [{ required: true, message: "请选择机构类别", trigger: "blur" }],
    teamLevelType: [{ required: true, message: "请选择一级类别", trigger: "blur" }],
    cname: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
    contact: [{ required: true, validator: validatePhone, trigger: "blur" }],
    account: [{ required: true, validator: validateAccount, trigger: "blur" }],
    machine: [{ required: true, validator: validateMachine, trigger: "blur" }],
    numbers: [{ required: true, message: "请输入子账号个数", trigger: "blur" }],
    email: [{ required: true, validator: validateEmail, trigger: "blur" }],
    cooperation: [{ required: true, message: "请选择合作状态", trigger: "blur" }],
    // margin: [{  required: true, message: "请输入保证金", trigger: "blur"  }],
    teamType: [{ required: true, message: "请选择机构类型", trigger: "blur" }],
    loginId: [{ required: true, message: "请输入登录标识", trigger: "blur" }],
    outsideAddress: [{ required: true, message: "请输入机构地址", trigger: "blur" }],
    commissionRatio: [
      { required: false, message: "请填入佣金比", trigger: "blur" },
      {
        pattern: /[?!^0-9]/,
        message: "请输入数字",
        trigger: "blur",
      },
    ],
  },
});
const { form, rules } = toRefs(data);

const fileList = ref([]); //已上传文件列表
const upload = reactive({
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/dispose/file/minioUpload",
});

const categoryOptions = ref([]); //机构类别下拉

//获取机构信息
function getTeamInfo() {
  teamInfo(route.params.teamId).then((res) => {
    form.value = JSON.parse(JSON.stringify(res.data.create)) || {};
    let filearr = JSON.parse(JSON.stringify(res.data.files));
    dowmloadFileList.value = res.data.files;
    for (let i = 0; i < filearr.length; i++) {
      filearr[i].url = filearr[i].fileUrl;
      filearr[i].name = filearr[i].firstName;
      delete filearr[i].fileUrl;
      delete filearr[i].firstName;
    }
    fileList.value = filearr;
  });
}
getTeamInfo();

const changelawyer = computed(() => (value) => {
  let newLayer = [];
  if (categoryOptions.value.length > 0) {
    const lawyerArr = categoryOptions.value.filter(
      (item) => item.teamType == form.value.teamLevelType
    );
    lawyerArr.forEach((item) => {
      newLayer = item.categoryList;
    });
  }
  return newLayer;
});

const getSelectDictData = () => {
  selectDictData().then((res) => {
    categoryOptions.value = res.data;
  });
};

getSelectDictData();

const initMap = () => {
  // 百度地图API功能
  var map = new BMap.Map("container"); // 创建Map实例
  form.value.longitudeAtitudeStart = form.value.longitudeAtitudeStart
    ? form.value.longitudeAtitudeStart
    : "116.404, 39.915";
  const startArr = form.value.longitudeAtitudeStart?.split(",");
  form.value.outsideAddress = "北京";
  const strCityValue = form.value.outsideAddress;
  map.centerAndZoom(new BMap.Point(Number(startArr[0]), Number(startArr[1])), 15); // 初始化地图,设置中心点坐标和地图级别
  //添加地图类型控件
  map.addControl(
    new BMap.MapTypeControl({
      mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
    })
  );
  map.setCurrentCity(strCityValue); // 设置地图显示的城市 此项是必须设置的
  map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

  var geoc = new BMap.Geocoder();

  var ac = new BMap.Autocomplete({
    // suggestId 是输入框id
    input: "suggestId",
    //  这个是地图实例
    location: map,
  });

  // 下拉列表里的内容确认发生的事件 ()
  ac.addEventListener("onconfirm", function (e) {
    // 把城市啥的拼接起来
    const myValue =
      e.item.value.province +
      e.item.value.city +
      e.item.value.district +
      e.item.value.street +
      e.item.value.business;
    // 搜索
    // 搜索结束执行的函数
    const mySearchFun = () => {
      // 传入定位函数的经纬度
      getAddOverlay(local.getResults().getPoi(0).point, true);
    };
    // 创建一个搜索的实例
    var local = new BMap.LocalSearch(map, {
      //搜索成功后的回调
      onSearchComplete: mySearchFun,
    });
    local.search(myValue);
  });

  // 下面是开始定位的
  var point = new BMap.Point(Number(startArr[0]), Number(startArr[1])); // 定位
  getAddOverlay(point, true);
  // 当地图点击的时候发生的事件
  map.addEventListener("click", function (e) {
    // 创建标点
    getAddOverlay(new BMap.Point(e.point.lng, e.point.lat));
  });
  // 定位点的函数
  function getAddOverlay(point, centerAndZoom = false) {
    // 清空地图上所有的标准当然你想要多个点的话可以不清除
    map.clearOverlays();
    var marker = new BMap.Marker(point); // 创建标注
    map.addOverlay(marker); // 添加到地图

    centerAndZoom && map.centerAndZoom(point, 15); // 中心点位 15是级别

    // 把定位转换为详细文字地址
    geoc.getLocation(point, (rs) => {
      form.value.outsideAddress = rs.address;
    });
    // 把位置传出
    // emits("addressData", point);
    if (form.value.outsideAddress) {
      const pointArr = [point.lng, point.lat];
      form.value.longitudeAtitudeStart = pointArr.join(",");
    }
  }
};

watch(isEdit, (value) => {
  if (value == false) {
    nextTick(() => {
      initMap();
    });
  }
});

//上传文件
function submitFile() {
  proxy.$refs["uploadRef"].submit();
}

/** 文件上传前的处理*/
const handleFileUploadBefore = (file) => {
  let size = file.size;
  if (size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    return false;
  }
};

//文件列表变化
function handleEditChange(file, fileList) {
  if (file.size > 20 * 1024 * 1024) {
    proxy.$modal.msgWarning("上传的单个文件不能超过20M!");
    fileList.pop();
    return false;
  }
}

/* 文件移除前的处理 */
async function handleRemove(file, fileList) {
  if (file.id) {
    return proxy.$modal
      .confirm(`请确认是否删除机构凭证文件：${file.name}`)
      .then((con) => {
        deleteTeamFile(file.id)
          .then((res) => {
            let { code, msg } = res;
            if (code === 200) {
              proxy.$modal.msgSuccess(msg);
            } else {
              proxy.$modal.msgError(msg);
              reject(false);
            }
          })
          .catch(() => {
            reject(false);
          });
      })
      .catch(() => {
        reject(false);
      });
  } else {
    let modifyName = "";
    if (file.response && file.response.code === 200) {
      modifyName = file.response.data.modifyName[0];
      for (let i = 0; i < files.value.length; i++) {
        if (files.value[i].modifyName == modifyName) {
          files.value.splice(i, 1);
          break;
        }
      }
    }
  }
}

/* 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  const { code, data } = response;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    file.status = "error";
  } else {
    var obj = {
      createId: route.params.teamId,
      firstName: data.firstName[0],
      modifyName: data.modifyName[0],
      fileUrl: data.fileUrl[0],
    };
    files.value.push(obj);
    addTeamFiles([obj])
      .then((res) => {
        //绑定给机构
        let { code } = res;
        if (code === 200) {
          proxy.$modal.msgSuccess("文件上传成功！");
        } else {
          proxy.$modal.msgError(`文件上传失败！`);
          proxy.$refs["uploadRef"].handleRemove(file);
        }
      })
      .catch(() => {
        proxy.$refs["uploadRef"].handleRemove(file);
      });
  }
};

/** 保存 */
function saveteamInfo() {
  proxy.$refs["teamRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      editTeamInfo(form.value)
        .then((res) => {
          subloading.value = false;
          let { code, msg } = res;
          if (code !== 200) {
            proxy.$modal.msgError(msg);
          } else {
            proxy.$modal.msgSuccess(msg);
            isEdit.value = true;
          }
        })
        .catch(() => {
          subloading.value = false;
        });
    }
  });
}

//获取机构类别
function getTeamType() {
  OrganizationType().then((res) => {
    teamTypeList.value = res.data;
  });
}
getTeamType();

//文件下载
function downFileByLink(name, fileUrl) {
  if (
    fileUrl.indexOf(".png") > -1 ||
    fileUrl.indexOf(".jpg") > -1 ||
    fileUrl.indexOf(".jpeg") > -1
  ) {
    let data = {
      url: fileUrl,
      type: "img",
      name: name,
    };
    downloadType(data);
  } else if (fileUrl.indexOf(".pdf") > -1) {
    let data = {
      url: fileUrl,
      type: "pdf",
      name: name,
    };
    downloadType(data);
  } else {
    window.open(fileUrl, "_blank");
  }
}

//判断下载类型
function downloadType(data) {
  switch (data.type) {
    case "img":
      return downloadImg(data);
    case "pdf":
      return downloadFile(data);
  }
}

function downloadFile(data) {
  fetch(data.url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((res) => {
      const downloadUrl = window.URL.createObjectURL(
        //new Blob() 对后端返回文件流类型处理
        new Blob([res], {
          type:
            data.type == "pdf"
              ? "application/pdf"
              : data.type == "word"
              ? "application/msword"
              : data.type == "xlsx"
              ? "application/vnd.ms-excel"
              : "",
        })
      );
      //word文档为msword,pdf文档为pdf
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      window.open(url);
    });
}

//下载图片
function downloadImg(data) {
  pathToBase64(data.url)
    .then((res) => {
      const link = document.createElement("a");
      link.href = res;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((err) => {
      console.log(err);
    });
}

//获取Base64
function pathToBase64(url) {
  return new Promise((resolve, reject) => {
    let image = new Image();
    image.onload = function () {
      let canvas = document.createElement("canvas");
      canvas.width = this.naturalWidth;
      canvas.height = this.naturalHeight;
      canvas.getContext("2d").drawImage(image, 0, 0);
      let result = canvas.toDataURL("image/png");
      resolve(result);
    };
    image.setAttribute("crossOrigin", "Anonymous");
    image.src = url;
    image.onerror = () => {
      reject(new Error("urlToBase64 error"));
    };
  });
}

//取消保存
function cancle() {
  isEdit.value = true;
  proxy.resetForm("teamRef");
  form.value = {
    id: undefined,
    cname: undefined,
    contact: undefined,
    category: undefined,
    teamLevelType: undefined,
    machine: undefined,
    account: undefined,
    email: undefined,
    numbers: undefined,
    loginId: undefined,
    cooperation: undefined,
    margin: undefined,
    starts: undefined,
    complete: undefined,
    describes: undefined,
    companyNum: undefined,
  };
  getTeamInfo();
}
</script>

<style lang="scss" scoped>
.file-download {
  width: 400px;
  line-height: 30px;
  margin-bottom: 30px;
  .file-box {
    padding: 5px 10px;
    .file-name {
      margin-left: 6px;
      font-size: 16px;
      color: #909399;
      vertical-align: top;
    }
  }
}
.file-box:hover {
  background-color: #f5fcfd;
}
.none-file {
  color: #999;
  font-size: 16px;
  line-height: 30px;
  padding: 5px 10px;
}
.upload-label {
  font-size: 14px;
  display: inline-block;
  margin-left: -30px;
  font-weight: 700;
  color: #606266;
  position: relative;
  top: 28px;
}
</style>
