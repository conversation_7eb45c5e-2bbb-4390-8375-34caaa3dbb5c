<template>
  <div id="container" style="width: 100vw; height: 100vh;"></div>
  <TeleportContainer />
  <!-- 规则弹窗 -->
  <rulesProcessVue ref="rulesProcessVueRef" />
  <!-- 接收方弹窗 -->
  <keepProcessVue ref="keepProcessVueRef" />
  <!-- 批次号弹窗 -->
  <dataProcessVue ref="dataProcessVueRef" />
</template>
<script setup>
const { proxy } = getCurrentInstance();
import AlgoNode from "./component/AlgoNode.vue";
import rulesProcessVue from "./dialog/rulesProcess.vue";
import keepProcessVue from "./dialog/keepProcess.vue";
import dataProcessVue from "./dialog/dataProcess.vue";
import { register, getTeleport } from '@antv/x6-vue-shape'
import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import insertCss from 'insert-css'
import { onMounted } from 'vue'
provide("setRules",Function,true)

function aaa() {


  // #region 初始化画布
  const graph = new Graph({
    container: document.getElementById('graph-container'),
    grid: true,
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3,
    },
    connecting: {
      router: 'manhattan',
      connector: {
        snap: true,
        allowBlank: false,
        allowLoop: false,
        allowNode: false,
        highlight: true,
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: 0,
        })
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet
      },
      validateMagnet({ magnet }) {
        return magnet.getAttribute('port-group') !== 'in'
      },

    },
    validateConnection({ sourceMagnet, targetMagnet }) {
      // 只能从输出链接桩创建连接
      if (
        !sourceMagnet ||
        sourceMagnet.getAttribute('port-group') === 'in'
      ) {
        return false
      }
      // 只能连接到输入链接桩
      if (
        !targetMagnet ||
        targetMagnet.getAttribute('port-group') !== 'in'
      ) {
        return false
      }
      return true
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
          },
        },
      },
    },
  })
  // #endregion

  // #region 使用插件
  graph.use(
    new Transform({
      resizing: true,
      rotating: true,
    }),
  )
    .use(
      new Selection({
        rubberband: true,
        showNodeSelectionBox: true,
      }),
    )
    .use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History())
  // #endregion

  // #region 初始化 stencil
  const stencil = new Stencil({
    title: '流程图',
    target: graph,
    stencilGraphWidth: 300,
    stencilGraphHeight: 800,
    collapsable: true,
    groups: [
      {
        title: '基础流程图',
        name: 'group1',
        graphHeight: 350,
      },
    ],
    layoutOptions: {
      columns: 1,
      columnWidth: 240,
      rowHeight: 60,
    },
  })
  document.getElementById('stencil').appendChild(stencil.container)
  // #endregion

  // #region 快捷键与事件
  graph.bindKey(['meta+c', 'ctrl+c'], () => {
    const cells = graph.getSelectedCells()
    if (cells.length) {
      graph.copy(cells)
    }
    return false
  })
  graph.bindKey(['meta+x', 'ctrl+x'], () => {
    const cells = graph.getSelectedCells()
    if (cells.length) {
      graph.cut(cells)
    }
    return false
  })
  graph.bindKey(['meta+v', 'ctrl+v'], () => {
    if (!graph.isClipboardEmpty()) {
      const cells = graph.paste({ offset: 32 })
      graph.cleanSelection()
      graph.select(cells)
    }
    return false
  })

  // undo redo
  graph.bindKey(['meta+z', 'ctrl+z'], () => {
    if (graph.canUndo()) {
      graph.undo()
    }
    return false
  })
  graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
    if (graph.canRedo()) {
      graph.redo()
    }
    return false
  })

  // select all
  graph.bindKey(['meta+a', 'ctrl+a'], () => {
    const nodes = graph.getNodes()
    if (nodes) {
      graph.select(nodes)
    }
  })

  // delete
  graph.bindKey('backspace', () => {
    const cells = graph.getSelectedCells()
    if (cells.length) {
      graph.removeCells(cells)
    }
  })

  // zoom
  graph.bindKey(['ctrl+1', 'meta+1'], () => {
    const zoom = graph.zoom()
    if (zoom < 1.5) {
      graph.zoom(0.1)
    }
  })
  graph.bindKey(['ctrl+2', 'meta+2'], () => {
    const zoom = graph.zoom()
    if (zoom > 0.5) {
      graph.zoom(-0.1)
    }
  })

  // 控制连接桩显示/隐藏
  const showPorts = (ports, show) => {
    for (let i = 0, len = ports.length; i < len; i += 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden'
    }
  }
  graph.on('node:mouseenter', () => {
    const container = document.getElementById('graph-container')
    const ports = container.querySelectorAll(
      '.x6-port-body',
    )
    showPorts(ports, true)
  })
  graph.on('node:dblclick', (data) => {
    console.log(data.node?.getData())
    if(data.node?.getData().status == "rules"){
        setRules()
    }
    if(data.node?.getData().status == "keep"){
        setKeeps()
    }
    if(data.node?.getData().status == "data"){
        setBatch()
    }
  })
  graph.on('node:mouseleave', () => {
    const container = document.getElementById('graph-container')
    const ports = container.querySelectorAll('.x6-port-body',)
    showPorts(ports, false)
  })
  graph.on('edge:mouseenter', ({ cell }) => {
    cell.addTools([
      {
        name: 'source-arrowhead',
      },
      {
        name: 'target-arrowhead',
        args: {
          attrs: {
            fill: 'red',
          },
        },
      },
    ])
  })
  graph.on('edge:mouseleave', ({ cell }) => {
    cell.removeTools()
  })
  graph.centerContent()
  // #endregion

  // #region 初始化图形
  const ports = {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
          img: {
            'xlink:href':
              'https://gw.alipayobjects.com/zos/antfincdn/FLrTNDvlna/antv.png',
            width: 16,
            height: 16,
            left: 20,
            x: 12,
            y: 12,
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
    },
    items: [
      {
        group: 'top',
      },
      {
        group: 'right',
      },
      {
        group: 'bottom',
      },
      {
        group: 'left',
      },
    ],
  }


  //节点组一
  register({
    shape: 'custom-rect',
    width: 150,
    height: 45,
    component: AlgoNode,
    ports: { ...ports },
  })

  Graph.registerNode(
    'custom-polygon',
    {
      inherit: 'polygon',
      width: 100,
      height: 45,
      attrs: {
        body: {
          strokeWidth: 1,
          stroke: '#5F95FF',
          fill: '#fff',
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: {
        ...ports,
        items: [
          {
            group: 'top',
          },
          {
            group: 'bottom',
          },
        ],
      },
    },
    true,
  )


  const r1 = graph.createNode({
    shape: 'custom-rect',
    label: '开始',
    attrs: {
      body: {
        rx: 20,
        ry: 26,
      },
    },
    data: {
        status: "start",
        label: '开始',
    },
  })
  const r2 = graph.createNode({
    shape: 'custom-rect',
    label: '分案规则',
    attrs: {
      body: {
        rx: 20,
        ry: 26,
        event:'node:rules'
      }
    },
    data: {
        status: "rules",
        label: '分案规则',
    },
  })
  const r3 = graph.createNode({
    shape: 'custom-rect',
    attrs: {
      body: {
        rx: 20,
        ry: 26,
        event:'node:keep'
      },
    },
    data: {
        status: "keep",
        label: '接收机构',
    },
  })
  const r4 = graph.createNode({
    shape: 'custom-rect',
    label: '结束',
    attrs: {
      body: {
        rx: 20,
        ry: 26,
      }
    },
    data: {
        status: "end",
        label: '结束',
    },
  })
  const r5 = graph.createNode({
    shape: 'custom-polygon',
    attrs: {
      body: {
        refPoints: '10,0 40,0 30,20 0,20',
      },
    },
    data:{
        status:"data"
    },
    label: '资产案件',
  })
  stencil.load([r1, r2, r3, r4, r5], 'group1')
}
function preWork() {
  // 这里协助演示的代码，在实际项目中根据实际情况进行调整
  const container = document.getElementById('container')
  const stencilContainer = document.createElement('div')
  stencilContainer.id = 'stencil'
  const graphContainer = document.createElement('div')
  graphContainer.id = 'graph-container'
  container.appendChild(stencilContainer)
  container.appendChild(graphContainer)

  insertCss(`
    #container {
      display: flex;
      border: 1px solid #dfe3e8;
    }
    #stencil {
      width: 320px;
      height: 100%;
      position: relative;
      border-right: 1px solid #dfe3e8;
    }
    #graph-container {
      width: calc(100% - 180px);
      height: 100%;
    }
    .x6-widget-stencil  {
      background-color: #fff;
    }
    .x6-widget-stencil-title {
      background-color: #fff;
    }
    .x6-widget-stencil-group-title {
      background-color: #fff !important;
    }
    .x6-widget-transform {
      margin: -1px 0 0 -1px;
      padding: 0px;
      border: 1px solid #239edd;
    }
    .x6-widget-transform > div {
      border: 1px solid #239edd;
    }
    .x6-widget-transform > div:hover {
      background-color: #3dafe4;
    }
    .x6-widget-transform-active-handle {
      background-color: #3dafe4;
    }
    .x6-widget-transform-resize {
      border-radius: 0;
    }
    .x6-widget-selection-inner {
      border: 1px solid #239edd;
    }
    .x6-widget-selection-box {
      opacity: 0;
    }
    .x6-node rect {
      fill: #fff;
      stroke: #409EFF;
      border: 1px solid rgba(0, 0, 0, 10%);
      box-shadow: 0 -1px 4px 0 rgba(209, 209, 209, 50%), 1px 1px 4px 0 rgba(217, 217, 217, 50%);
    }
    .x6-node foreignObject{
      width:150px;
      height:45px;
    }
  `)
}

//设置规则
function setRules(){
    proxy.$refs['rulesProcessVueRef'].opendialog()
}

//设置接收方
function setKeeps(){
    proxy.$refs['keepProcessVueRef'].opendialog()
}

//设置批次号
function setBatch(){
    proxy.$refs['dataProcessVueRef'].opendialog()
}


onMounted(() => {
  // 为了协助代码演示
  preWork()
  aaa()
})

</script>

<style scoped></style>
