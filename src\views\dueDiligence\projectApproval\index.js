import { zcQueryApproveList, zcApprovePass, zcApproveReject } from "@/api/common";

export class ProjectApproval {
  constructor() {
    // 查询参数
    this.queryParmas = {
      pageNum: 1,
      pageSize: 10,
    };
    // 审批状态枚举
    this.approveStatusEnum = {
      0: "待审核",
      1: "审核中",
      2: "已通过",
      3: "未通过",
      4: "已撤销",
      5: "已作废",
      6: "已退案关闭"
    };
    // 审批数据
    this.approveData = {};

    // 标签页列表
    this.tabList = [
      { code: "0", info: "待处理" },
      { code: "2", info: "已同意" },
      { code: "3", info: "未同意" },
      { code: "4", info: "已撤销" },
      { code: "all", info: "全部" },
    ];

    // 数据列表
    this.dataList = [];

    // 总数
    this.total = 0;

    // 当前激活的标签页
    this.activetab = "0";
  }

  /**
   * 格式化审批状态
   * @param {number} value - 状态值
   * @returns {string} 格式化后的状态文本
   */
  formatApproveStart(value) {
    const statusMap = {
      0:"待审核",
      2: "通过",
      3: "不通过",
      4: "撤销",
      5: "作废",
      6: "已退案关闭",
    };
    return statusMap[value] || "-";
  }

  /**
   * 获取列表数据
   * @param {Function} callback - 回调函数，用于更新组件状态
   */
  async getList(callback) {
    try {
      let params = JSON.parse(JSON.stringify(this.queryParmas));
      params.approveProcessState = this.activetab === "all" ? undefined : this.activetab;
      params.approveData = JSON.parse(JSON.stringify(this.approveData));

      const res = await zcQueryApproveList(params);

      if (res && res.rows) {
        this.dataList = res.rows;
        this.total = res.total || 0;
      }

      // 调用回调函数更新组件状态
      if (typeof callback === 'function') {
        callback({
          dataList: this.dataList,
          total: this.total
        });
      }
    } catch (error) {
      console.error('获取列表数据失败:', error);
      throw error;
    }
  }

  /**
   * 处理查询
   * @param {Function} callback - 回调函数
   */
  handleQuery(callback) {
    this.queryParmas.pageNum = 1;
    this.getList(callback);
  }

  /**
   * 重置查询
   * @param {Function} callback - 回调函数
   */
  resetQuery(callback) {
    this.queryParmas = {
      pageNum: 1,
      pageSize: 10,
      approveCode: this.queryParmas.approveCode
    };
    this.approveData = {};
    this.getList(callback);
  }

  /**
   * 切换标签页
   * @param {string} tabCode - 标签页代码
   * @param {Function} callback - 回调函数
   */
  changeTab(tabCode, callback) {
    this.activetab = tabCode;
    this.getList(callback);
  }

  /**
   * 更新查询参数
   * @param {Object} newParams - 新的查询参数
   */
  updateQueryParams(newParams) {
    this.queryParmas = { ...this.queryParmas, ...newParams };
  }

  /**
   * 更新审批数据
   * @param {Object} newData - 新的审批数据
   */
  updateApproveData(newData) {
    this.approveData = { ...this.approveData, ...newData };
  }

  /**
   * 设置当前页
   * @param {number} pageNum - 页码
   */
  setCurrentPage(pageNum) {
    this.queryParmas.pageNum = pageNum;
  }

  /**
   * 设置每页大小
   * @param {number} pageSize - 每页大小
   */
  setPageSize(pageSize) {
    this.queryParmas.pageSize = pageSize;
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态对象
   */
  getCurrentState() {
    return {
      queryParmas: this.queryParmas,
      approveData: this.approveData,
      tabList: this.tabList,
      dataList: this.dataList,
      total: this.total,
      activetab: this.activetab
    };
  }

  /**
   * 检查行是否可选
   * @param {Object} row - 行数据
   * @returns {boolean} 是否可选
   */
  selectable(row) {
    // 根据状态判断是否可选，可以根据业务需求修改逻辑
    return true;
  }

  /**
   * 处理行操作
   * @param {Object} row - 行数据
   * @param {string} action - 操作类型
   */
  handleRowAction(row, action) {
    console.log('处理行数据：', row, '操作：', action);
    // 可以根据action类型执行不同的操作
    switch (action) {
      case 'approve':
        // 处理通过操作
        break;
      case 'reject':
        // 处理不通过操作
        break;
      case 'details':
        // 处理详情操作
        break;
      default:
        console.log('未知操作类型：', action);
    }
  }
  async handleApprove(data) {
    return await zcApprovePass(data);
  }
  async handleReject(data) {
    return await zcApproveReject(data);
  }
}

export default ProjectApproval;
