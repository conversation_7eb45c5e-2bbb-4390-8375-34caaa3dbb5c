<template>
  <div>
    <div class="app-container">
    <el-tabs  v-model="activeTab" >
      <el-tab-pane label="机构评价" v-if="checkPermi(['team:teamevaluate:organization'])"  :name="0">
        <organization v-if="activeTab == 0"/>
      </el-tab-pane>
      <el-tab-pane label="机构评价表" v-if="checkPermi(['team:teamevaluate:formBox'])" :name="1">
        <formBox v-if="activeTab == 1"/>
      </el-tab-pane>
    </el-tabs>
    </div>
  </div>
</template>

<script setup name="Teamevaluate">
import organization from "./organization";
import formBox from "./form";
import { checkPermi } from "@/utils/permission";

const activeTab = ref(0);
//检测权限
function checkPower(){
  if(checkPermi(['team:teamevaluate:organization'])){
    activeTab.value = 0;
  }else if(checkPermi(['team:teamevaluate:formBox'])){
    activeTab.value = 1;
  }else{
    activeTab.value = undefined;
  }
}
checkPower()
</script>

<style lang="scss" scoped>

</style>
