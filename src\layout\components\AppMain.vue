<template>
  <section class="app-main">
    <!-- 全局消息最新消息通知展示 -->
    <messageScrooll />
    <router-view v-slot="{ Component, route }" :key="routeKey">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>

<script setup>
import messageScrooll from "@/components/MessageScroll/index.vue";
import { nextTick, ref, watch } from "vue";
let store = useStore();
const route = useRoute();
const { proxy } = getCurrentInstance();
const router = useRouter();
const isRefresh = ref(true)
const routeKey = ref(+new Date())
store.dispatch("tagsView/addCachedView", route);
const cachedViews = computed(() => {
  return store.state.tagsView.cachedViews;
});


watch(()=>router,()=>{
  if (router?.options?.history?.state?.back?.indexOf('/assessment/view-Report/operate')>-1&&isRefresh) {
    routeKey.value = +new Date();
    isRefresh.value = false;
  }else{
    isRefresh.value = true;
  }
},{deep:true})

onMounted(() => {
  console.log("window.name",window.name,route.path)
  // let viewTag = JSON.parse(localStorage.getItem(`viewTag`));
  if (window.name == "") {
    //首次加载
    window.name = "isRefresh";
  } else if (
    window.name == "isRefresh" &&
    route.path.indexOf("view-Report") > -1
  ) {
    //页面为资产评估模型
    let data = localStorage.getItem(`view-Report`);
    localStorage.clear();
    localStorage.setItem(`view-Report`, data);
  } else if (
    window.name == "isRefresh" &&
    route.path.indexOf("send-note") > -1
  ) {
    //页面为资产评估模型
    let data = localStorage.getItem(`/send-note`);
    localStorage.clear();
    localStorage.setItem(`/send-note`, data);
  } else if (
    window.name == "isRefresh" &&
    route.path.indexOf("/dueDiligence/projectInfo") > -1
  ) {
    // 页面为项目尽调详情页
    let data = localStorage.getItem('isSubmit');
    localStorage.clear();
    localStorage.setItem('isSubmit', data);
  } else if (
    window.name == "isRefresh" &&
    route.path.indexOf("caseManage-outcase") === -1
  ) {
    //刷新页面且当前页面不是 快速分案、规则分案、共债分案列表、共债分案
    localStorage.clear();
  }else {
    //当前页是 快速分案、规则分案、共债分案列表、共债分案 保留对应缓存
    let data = localStorage.getItem(`${route.params.time}`);
    localStorage.clear();
    localStorage.setItem(`${route.params.time}`, data);
  }
  // addTags(viewTag);
});

//添加信息
function addTags(viewTag) {
  if (viewTag) {
    viewTag.forEach((item, index) => {
      if (item.name) {
        store.dispatch("tagsView/addView", item);
      }
    });
    localStorage.setItem("viewTag", JSON.stringify(viewTag));
  }
  return false;
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
