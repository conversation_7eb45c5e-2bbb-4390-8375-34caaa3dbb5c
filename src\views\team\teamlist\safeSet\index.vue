<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container mb20" style="color: #888888">
          <svg-icon class="mr5" icon-class="safe" color="#888888" />
          安全设置
        </div>
        <div class="head-container">
          <el-tabs class="mytabs" tab-position="left" v-model="tabActive">
            <el-tab-pane label="信息脱敏" name="0"></el-tab-pane>
            <el-tab-pane label="水印设置" name="1"></el-tab-pane>
            <el-tab-pane label="页面限制" name="2"></el-tab-pane>
            <el-tab-pane label="白名单访问" name="3"></el-tab-pane>
            <el-tab-pane label="外访授权" name="4"></el-tab-pane>
            <el-tab-pane label="实名认证" name="5"></el-tab-pane>
            <el-tab-pane label="安全验证" name="6"></el-tab-pane>
            <el-tab-pane label="导出设置" name="7"></el-tab-pane>
<!--            <el-tab-pane label="推送小程序" name="8"></el-tab-pane>-->
          </el-tabs>
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <infoSecrecy v-if="tabActive == '0'" v-model:status="states.informationStatus" />
        <v-watermark v-if="tabActive == '1'" v-model:status="states.settingStatus" />
        <pagelimit v-if="tabActive == '2'" v-model:status="states.restrictedState" />
        <whiteVue v-if="tabActive == '3'" v-model:status="states.whitelistStatus" />
        <callon v-if="tabActive == '4'" v-model:status="states.authorizationStatus" />
        <realname v-if="tabActive == '5'" v-model:status="states.authenticationStatus" />
        <safetyCheck v-if="tabActive == '6'" v-model:status="states.securityVerificationStatus" />
        <exportSet v-if="tabActive == '7'" v-model:status="states.exportSettingStatus" />
<!--        <pushWebapp v-if="tabActive == '8'" v-model:status="states.pushAppStatus" />-->
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="SafeSet">
import { teamSafeInfo } from "@/api/team/team";
import infoSecrecy from "./page/infoSecrecy";
import vWatermark from "./page/watermark";
import pagelimit from "./page/pagelimit";
import whiteVue from "./page/white";
import callon from "./page/callon";
import realname from "./page/realname";
import safetyCheck from "./page/safetyCheck";
import exportSet from "./page/exportSet";
import pushWebapp from "./page/pushWebapp";

const route = useRoute();
const states = ref({
  informationStatus: 0,
  settingStatus: 0,
  restrictedState: 0,
  whitelistStatus: 0,
  authorizationStatus: 0,
  authenticationStatus: 0,
  safetyCheckStatus: 0,
  securityVerificationStatus: 0,
  exportSettingStatus: 0,
  pushAppStatus:0
});
const desensitization = ref([
  { filed: "dname", fieldname: "姓名", status: 0, rules: "只展示姓名第一个字，例：李**" },
  {
    filed: "numbers",
    fieldname: "手机号码",
    status: 0,
    rules: "脱敏中间四位数字，例:134****4532",
  },
  {
    filed: "cardId",
    fieldname: "证件号码",
    status: 0,
    rules: "前6位和后6位不脱敏，中间脱敏，例:333344******037232",
  },
  {
    filed: "bankCard",
    fieldname: "银行卡号",
    status: 0,
    rules: "除前6位和后4位不脱敏，中间脱敏，例：632243******6543",
  },
  {
    filed: "qq",
    fieldname: "QQ",
    status: 0,
    rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
  },
  {
    filed: "weChat",
    fieldname: "微信",
    status: 0,
    rules: "除前3位和后3位不脱敏，中间脱敏，例：442***789",
  },
  {
    filed: "households",
    fieldname: "户籍地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "unitAddress",
    fieldname: "单位详细地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "residentialAddress",
    fieldname: "居住地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "homeAddress",
    fieldname: "家庭地址",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
  {
    filed: "entityName",
    fieldname: "单位名称",
    status: 0,
    rules: "从第4位开始脱敏,脱敏8位，例：湖南省花********六组",
  },
]);
const _desensitization = ref({}); //脱敏
provide("desensitization", desensitization);
provide("_desensitization", _desensitization);

const waterMdata = ref({});
provide("watermark", waterMdata);

//导出设置
const teamExport = ref({})
provide("teamExport", teamExport);

const tabActive = ref("0");

const state = computed(() => states.value);
provide("state", state);

//获取机构信息
function getTeamSafe() {
  return new Promise((reslove, reject) => {
    teamSafeInfo(route.params.teamId).then((res) => {
      states.value = res.data.state;
      waterMdata.value = res.data.watermark;
      teamExport.value = res.data?.teamExport || {};
      _desensitization.value = JSON.parse(JSON.stringify(res.data.desensitization));

      let obj = res.data.desensitization;
      desensitization.value.map((item) => {
        for (const key in obj) {
          if (item.filed == key) {
            item.status = obj[key];
            break;
          }
        }
      });
      reslove()
    });
  })
}
getTeamSafe();
provide("getTeamSafe", getTeamSafe);
</script>

<style scoped></style>
