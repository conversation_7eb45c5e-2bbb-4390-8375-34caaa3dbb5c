import request from '@/utils/request'

export function selectApprovalList(query) {
  return request({
    url: '/caseManage/projInitiation/selectApprovalList',
    method: 'get',
    params: query
  })
}
export function selectApprovalCount(query) {
  return request({
    url: '/caseManage/projInitiation/selectApprovalCount',
    method: 'get',
    params: query
  })
}
export function groupStatisticsState(query) {
  return request({
    url: '/caseManage/projInitiation/groupStatisticsState',
    method: 'get',
    params: query
  })
}

export function revokeProjectApproval(data) {
  return request({
    url: '/caseManage/projInitiation/revokeProjectApproval',
    method: 'post',
    data: data
  })
}

