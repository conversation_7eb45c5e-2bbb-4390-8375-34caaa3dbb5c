<template>
  <el-dialog title="预览" v-model="open" width="650px" :before-close="cancel">
    <div class="case-pdf" v-loading="loading">
      <previewPdf v-if="pdfSrc && pdfSrc.length > 0" :pdfSrc="pdfSrc" :total="total" />
    </div>
    <template #footer>
      <div class="dialog-footer" style="text-align:center">
        <el-button type="primary" @click="cancel">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="TemplateDetails">
import previewPdf from "@/components/PreviewPdf/previewPdf.vue";
//全局变量
const { proxy } = getCurrentInstance();
const key = ref(+new Date());
const open = ref(false);
const loading = ref(false);
const pdfSrc = ref(
  "https://assest.amcmj.com/resource/preview/2024/08/19/b93b6cad8bfc4ce0811f3c5af2b0f8a5_16251a94a4fd4ece96fa397638e8df1a.pdf"
);
const total = ref(1);
//打开弹窗
function openDialog(id) {
  open.value = true;
  key.value = +new Date();
}

//关闭弹窗
function cancel() {
  open.value = false;
}

defineExpose({ openDialog });
</script>

<style scoped>
.case-pdf {
  width: 100%;
  height: 550px;
  overflow: auto;
  overflow-x: hidden;
}
</style>