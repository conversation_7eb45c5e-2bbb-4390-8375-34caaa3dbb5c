<template>
  <div>
    <div class="stamp-item" v-for="(item, index) in stamps" :key="index">
      <el-icon :color="color[index]" class="icon-flag"><flag /></el-icon>
      <el-input
        class="mr10 ml10"
        v-model="item.labelContent"
        style="width: 200px"
        :disabled="!edit"
        @blur="save(item)"
      ></el-input>
      <el-switch
        v-if="edit"
        v-model="item.stateLabel"
        :disabled="!edit"
        active-color="#2ECC71"
        :active-value="1"
        :inactive-value="0"
        @change="save(item)"
      ></el-switch>
    </div>
  </div>
</template>

<script setup>
import { editLabel } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const props = defineProps({
  edit: {
    type: Boolean,
    default: true,
  },
});
const route = useRoute();
const color = [
  "#E85750",
  "#EA679B ",
  "#EE7F37",
  "#426EE2",
  "#64CEEA",
  "#9980D8",
  "#5AB56E",
];
const stamps = inject("labels");
const getTeamInfo = inject("getTeamInfo", Function, true);

//保存修改标签
function save(data) {
  if (!route.params.teamId) return;
  editLabel([data])
    .then((res) => {})
    .catch(() => {
      getTeamInfo();
    });
}
</script>

<style scoped>
.stamp-item {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}
.icon-flag {
  font-size: 16px;
}
</style>
