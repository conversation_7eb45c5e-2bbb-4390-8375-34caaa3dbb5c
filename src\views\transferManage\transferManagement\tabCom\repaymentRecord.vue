<template>
    <div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="交易时间" align="center" prop="repaymentDate" width="160" />
            <el-table-column label="贷方发生额/元(收入)" align="center" prop="repaymentDate" width="180" />
            <el-table-column label="转账人姓名" align="center" prop="transferorName" width="120" />
            <el-table-column label="转账人账号" align="center" prop="accountNumber" width="100" />
            <el-table-column label="备注" align="center" prop="remarks" min-width="160" />
            <el-table-column label="交易流水号" align="center" prop="orderNo" width="180px" />
            <el-table-column label="还款渠道" align="center" prop="repaymentMode" width="120" />
            <el-table-column label="案件ID" align="center" prop="caseId" width="120" />
            <el-table-column label="借据号" align="center" prop="caseID" width="180" />
            <el-table-column label="机构名称" align="center" prop="outsourcingTeamName" min-width="180" />
            <el-table-column label="姓名" align="center" prop="registrar" />
            <el-table-column label="委案批次号" align="center" width="160" prop="entrustingCaseBatchNum" />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
const dataList = ref([
    {
        caseId: 142270,
        "createTime": "2025-05-09 17:54:17",
        "updateBy": "admin",
        "updateTime": "2025-06-16 14:19:56",
        "repaymentDate": "2025-05-09",
        "repaymentMoney": 8.99,
        "repaymentMode": "微信",
        "repaymentType": "部分还款",
        "examineState": "审核中",
        "registrar": "蝎1",
        "entrustingCaseBatchNum": "12720250509175312",
        "entrustingPartyName": "招商银行佛山分行",
        "batchNum": "zsyhfoshan202502CSD0002",
        "outsourcingTeamId": 127,
        "outsourcingTeamName": "蝎",
        "clientName": "林淑芬",
        "clientIdcard": "520422197406040533",
        "clientIdType": "身份证",
        "examineStateCode": 1,
        "packageName": "zsyh002",
    },
    {
        caseId: 142254,
        "createTime": "2025-04-27 23:12:32",
        "updateBy": "团队长",
        "updateTime": "2025-04-27 23:12:46",
        "repaymentDate": "2025-04-27",
        "repaymentMoney": 1000,
        "repaymentMode": "微信",
        "examineState": "已退案关闭",
        "registrar": "团队长",
        "entrustingCaseBatchNum": "15320250427225153",
        "entrustingPartyName": "招商银行佛山分行",
        "batchNum": "zsyhfoshan202502CC0007",
        "outsourcingTeamId": 153,
        "outsourcingTeamName": "Jacky03",
        "clientName": "黄小庆",
        "clientIdcard": "220104195701012771",
        "clientIdType": "身份证",
        "contractNo": "JZ-20240422-007",
        "packageName": "招商银行4月第1季度信用不良3号包",
    },
    {
        caseId: 142254,
        "createTime": "2025-04-27 22:53:46",
        "updateBy": "admin",
        "updateTime": "2025-04-27 23:07:58",
        "repaymentDate": "2025-04-27",
        "repaymentMoney": 20,
        "repaymentMode": "微信",
        "examineState": "未通过",
        "registrar": "团队长",
        "entrustingCaseBatchNum": "15320250427225153",
        "entrustingPartyName": "招商银行佛山分行",
        "batchNum": "zsyhfoshan202502CC0007",
        "outsourcingTeamId": 153,
        "outsourcingTeamName": "Jacky03",
        "clientName": "黄小庆",
        "clientIdcard": "220104195701012771",
        "clientIdType": "身份证",
        "approveTime": "2025-04-27 23:07:58",
        "approveSort": 2,
        "reviewer": "admin",
        "examineStateCode": 2,
        "contractNo": "JZ-20240422-007",
        "packageName": "招商银行4月第1季度信用不良3号包",
    },
    {
        caseId: 142255,
        "createTime": "2025-04-22 19:16:47",
        "updateBy": "admin",
        "updateTime": "2025-04-27 20:33:26",
        "repaymentDate": "2025-04-09",
        "repaymentMoney": 10000,
        "repaymentMode": "小程序",
        "repaymentType": "结清还款",
        "examineState": "已通过",
        "registrar": "lzh",
        "entrustingCaseBatchNum": "26320250422185223",
        "entrustingPartyName": "中信银行",
        "batchNum": "CITIC202502CC0002",
        "outsourcingTeamId": 263,
        "outsourcingTeamName": "测试机构",
        "clientName": "来来来",
        "clientIdcard": "******************",
        "clientIdType": "身份证",
        "approveTime": "2025-04-22 19:45:04",
        "approveSort": 2,
        "reviewer": "admin",
        "remarks": "测试",
        "examineStateCode": 3,
        "packageName": "test",
    },
    {
        caseId: 142237,
        "createTime": "2025-04-22 16:55:11",
        "updateBy": "admin",
        "updateTime": "2025-04-22 17:11:10",
        "repaymentDate": "2025-04-21",
        "repaymentMoney": 90000,
        "repaymentMode": "微信",
        "repaymentType": "结清还款",
        "examineState": "已通过",
        "registrar": "令狐冲",
        "entrustingPartyName": "招商银行佛山分行",
        "batchNum": "zsyhfoshan202502CC0006",
        "outsourcingTeamId": 261,
        "outsourcingTeamName": "雄大",
        "clientName": "李志华",
        "clientIdcard": "******************",
        "clientIdType": "身份证",
        "approveTime": "2025-04-22 17:11:10",
        "approveSort": 2,
        "reviewer": "admin",
        "examineStateCode": 3,
        "packageName": "11111",
    },
    {
        caseId: 142235,
        "createTime": "2025-04-08 17:10:06",
        "updateBy": "",
        "updateTime": "2025-04-08 17:29:51",
        "repaymentDate": "2025-04-07",
        "repaymentMoney": 25000,
        "repaymentMode": "微信",
        "repaymentType": "结清还款",
        "examineState": "已通过",
        "registrar": "长城",
        "entrustingPartyName": "招商银行佛山分行",
        "batchNum": "zsyhfoshan202502CC0005",
        "outsourcingTeamId": 1,
        "outsourcingTeamName": "机构13",
        "clientName": "张三",
        "clientIdcard": "44030419801228948X",
        "clientIdType": "身份证",
        "approveTime": "2025-04-08 17:29:51",
        "approveSort": 999,
        "reviewer": "系统对账",
        "examineStateCode": 3,
        "packageName": "招商银行3月第1季度信用不良1号包",
    },
    {
        caseId: 11643,
        "createTime": "2025-03-31 09:07:58",
        "updateBy": "admin",
        "updateTime": "2025-04-01 16:01:42",
        "repaymentDate": "2025-03-31",
        "repaymentMoney": 15000,
        "repaymentMode": "微信",
        "repaymentType": "结清还款",
        "examineState": "已通过",
        "registrar": "陈必胜",
        "entrustingCaseBatchNum": "23320250304151400",
        "entrustingPartyName": "广州银行",
        "batchNum": "gzyh202501CL0005",
        "outsourcingTeamId": 233,
        "outsourcingTeamName": "广东佛山串联律师事务所",
        "clientName": "温游昕",
        "clientIdcard": "15010319890420182X",
        "clientIdType": "身份证",
        "approveTime": "2025-03-31 09:08:40",
        "approveSort": 2,
        "reviewer": "admin",
        "examineStateCode": 3,
        "packageName": "广州银行2025第一季度消费不良2号包",
    },
    {
        caseId: 12226,
        "createTime": "2025-03-18 17:09:14",
        "updateBy": "admin",
        "updateTime": "2025-03-18 17:19:47",
        "repaymentDate": "2025-03-20",
        "repaymentMoney": 7000,
        "repaymentMode": "微信",
        "repaymentType": "部分还款",
        "examineState": "已通过",
        "registrar": "小陈1",
        "entrustingPartyName": "中国银行",
        "batchNum": "zgyh202501BP0002",
        "outsourcingTeamId": 243,
        "outsourcingTeamName": "坤亿科技有限公司",
        "clientName": "张三",
        "clientIdcard": "123456789003072519",
        "clientIdType": "居民身份证",
        "approveTime": "2025-03-18 17:19:47",
        "approveSort": 2,
        "reviewer": "admin",
        "remarks": "111",
        "examineStateCode": 3,
        "packageName": "中国银行3月第一季度2号包",
    },
    {
        caseId: 12225,
        "createTime": "2025-03-18 15:29:22",
        "updateBy": "",
        "updateTime": "2025-03-18 15:36:50",
        "repaymentDate": "2025-03-18",
        "repaymentMoney": 900000,
        "repaymentMode": "微信",
        "repaymentType": "部分还款",
        "examineState": "已通过",
        "registrar": "刀疤强",
        "entrustingCaseBatchNum": "24220250318144855",
        "entrustingPartyName": "招商银行",
        "batchNum": "CMB202501JR0003",
        "outsourcingTeamId": 242,
        "outsourcingTeamName": "丽泽科技有限公司",
        "clientName": "陈奕迅",
        "clientIdcard": "******************",
        "clientIdType": "身份证",
        "approveTime": "2025-03-18 15:36:50",
        "approveSort": 999,
        "reviewer": "系统对账",
        "examineStateCode": 3,
        "packageName": "招商银行4月第1季度3号包",
    },
    {
        caseId: 11277,
        "createTime": "2025-02-10 17:01:55",
        "updateBy": "admin",
        "updateTime": "2025-02-10 17:02:18",
        "repaymentDate": "2025-02-12",
        "repaymentMoney": 800,
        "repaymentMode": "小程序",
        "repaymentType": "结清还款",
        "examineState": "已通过",
        "registrar": "雍三",
        "entrustingCaseBatchNum": "23420250208165755",
        "entrustingPartyName": "兴业银行",
        "batchNum": "CIB202404MS0006",
        "outsourcingTeamId": 234,
        "outsourcingTeamName": "华道数据处理（杭州）有限公司",
        "clientName": "李华",
        "clientIdcard": "140302196103020846",
        "clientIdType": "身份证",
        "approveTime": "2025-02-10 17:02:18",
        "approveSort": 2,
        "reviewer": "admin",
        "remarks": "800",
        "examineStateCode": 3,
        "packageName": "兴业银行2024第二季度民事纠纷1号包",
    }
])
const loading = ref(false)

const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(10)
</script>

<style lang="scss" scoped></style>