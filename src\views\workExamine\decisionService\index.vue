<template>
  <div class="app-container">
    <el-row class="mb10">
      <el-button type="primary" v-if="checkPermi(['workExamine:decisionService:addStrategy'])" plain icon="Plus"
        @click="addStrategy()">新增分案决策</el-button>
    </el-row>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column label="ID" align="center" key="ID" prop="id" sortable width="80" />
      <el-table-column label="分案决策名称" align="center" key="decisionName" prop="decisionName" sortable />
      <el-table-column label="分案规则-分案节点" align="center" key="ruleNode" prop="ruleNode" sortable width="400">
        <template #default="{ row }">
          <div v-if="row?.newRuleNodeList && row?.newRuleNodeList.length > 0">
            <el-popover
                placement="bottom"
                :width="400"
                v-for="(item,index) in row.newRuleNodeList"
                :key="index"
                :ref="`popover-${index}`"
                :title="`${item.code}-${item.info}`"
                trigger="click"
              >
                <template #reference>
                  <el-button type="primary" class="tip-btn" link @click="getRulesDetails(item.code,item.info)"
                    >{{`${item.code}-${item.info}`}}</el-button
                  >
                </template>
                <div class="details-tips" v-loading="detailsLoading" v-html="detailsTip"></div>
            </el-popover>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" key="sort" prop="sort" sortable width="100" />
      <!-- <el-table-column label="自动分案时间（每日）" align="center" key="divisionTime" prop="divisionTime" width="120" /> -->
      <el-table-column label="状态" align="center" key="status" prop="status" sortable width="100">
        <template #default="{ row }">
          <el-button type="text" v-if="checkPermi(['workExamine:decisionService:status'])"  @click="stateChange(row)">{{ statusFor(row) }}</el-button>
          <span v-else>{{statusFor(row)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" key="createBy" prop="createBy" sortable width="120"/>
      <el-table-column label="创建时间" align="center" key="createTime" prop="createTime" sortable width="180" />
      <el-table-column label="操作" width="420" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" v-if="checkPermi(['workExamine:decisionService:preview'])" link
            @click="hanldePreview(row)">预览</el-button>
          <el-button type="primary" link v-if="checkPermi(['workExamine:decisionService:setCaseProcess'])"
            @click="setCaseProcess(row)">设置分案决策</el-button>
          <el-button type="primary" v-if="checkPermi(['workExamine:decisionService:updateStrategy'])" link
            @click="addStrategy(row)">修改</el-button>
          <el-button type="primary" v-if="checkPermi(['workExamine:decisionService:checkRecord'])" link
            @click="checkRecord(row)">分案决策记录</el-button>
          <el-button type="primary" v-if="checkPermi(['workExamine:decisionService:set'])"  link
            @click="previewCase(row)">决策预分案</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 新增策略 -->
    <addStrategyVue ref="addStrategyVueRef" @getList="getList" />
    <!-- 预览 -->
    <perviewStrategy ref="perviewStrategyRef" />
  </div>
</template>

<script setup name="DecisionService">
import { checkPermi } from "@/utils/permission";
import { getStrategyApi ,getStrategyDetailApi, updateStrategyApi ,previewStrategyApi} from "@/api/workExamine/decisionService";
import addStrategyVue from "./dialog/addStrategy.vue"
import perviewStrategy from "./dialog/perviewStrategy.vue"

//全局配置
const { proxy } = getCurrentInstance();
const router = useRouter()
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
//表格数据
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const detailsTip = ref(undefined);
const detailsLoading = ref(false);
//获取列表数据
function getList() {
  loading.value = true;
  let req = JSON.parse(JSON.stringify(queryParams.value));
  getStrategyApi(req).then((res) => {
    dataList.value = res.rows;
    total.value = res.total;
  }).finally(() => {
    loading.value = false;
  });
}
getList();

//获取详情
function getRulesDetails(code,info){
  detailsLoading.value = true;
  let req = { 
    ruleName:code,
    nodeName:info
   }
  getStrategyDetailApi(req).then((res) =>{
    detailsLoading.value = false;
    detailsTip.value = res.data || `--`;
  }).catch((error) =>{
    detailsLoading.value = false;
    detailsTip.value = '--';
  })
}

//新增策略
function addStrategy(row) {
  if (row) {
    let req = JSON.parse(JSON.stringify(row));
    proxy.$refs["addStrategyVueRef"].opendialog(req);
  } else {
    proxy.$refs["addStrategyVueRef"].opendialog();
  }
}

//设置分案决策
function setCaseProcess(row){
  router.push(`/workExamine/policyName/${row.id}`)
}

// 分案决策记录
function checkRecord(row) {
  const path = `/workExamine/decisionLog`
  const query = { id: row.id }
  router.push({ path, query })
}

//预览分案
function previewCase(row) {
  let req = {
    decisionId:row.id
  }
  previewStrategyApi(req).then((res) =>{
    proxy.$modal.msgSuccess("操作成功！");
    getList();
  })
}

// 预览
function hanldePreview(row) {
  proxy.$refs['perviewStrategyRef'].opendialog(row)
}
// 状态
function statusFor(row) {
  const statusObj = { 0: '启动', 1: '关闭' }
  return statusObj[row.status]
}

//状态修改
function stateChange(row) {
  loading.value = true;
  let req = JSON.parse(JSON.stringify(row));
  req.status = req.status == 1?0:1
  updateStrategyApi(req)
    .then((res) => {
      getList();
      proxy.$modal.msgSuccess("修改成功");
    })
    .catch(() => {
      getList();
      //   row.state = row.state === "0" ? "1" : "0";
    });
}
</script>

<style>
.tip-btn{
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  -o-text-overflow: ellipsis;
  -webkit-text-overflow: ellipsis;
}
</style>
