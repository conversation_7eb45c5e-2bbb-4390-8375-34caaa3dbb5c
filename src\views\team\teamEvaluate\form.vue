<template>
  <div>
    <el-form :model="queryParams" prop="formBox" :inline="true">
      <el-form-item label="生成日期:" prop="generationDate" style="width: 300px">
        <el-date-picker
          v-model="queryParams.generationDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          style="width:240px"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"/>
      </el-form-item>
      <el-form-item label="机构名称:" prop="name">
        <el-select
          v-model="queryParams.name"
          placeholder="请选择机构名称"
          @focus="getTeamOptions"
          clearable
          style="width: 240px"
        >
          <el-option-group
            v-for="group in teamOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.children"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">查询</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
        <el-button 
        v-hasPermi="['team:list:export']"
        :disabled="single" @click="exportData()">
        导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" @selection-change="handleSelectionChange" ref="multipleTableRef" :data="dataList">
      <el-table-column type="selection" width="30" />
      <el-table-column
        label="机构名称"
        align="center"
        key="teamName"
        prop="teamName"
        
      />
      <el-table-column
        label="机构类型"
        align="center"
        key="teamType"
        prop="teamType"
        show-overflow-tooltip
      />
      <el-table-column
        label="回款率"
        align="center"
        key="collectionRate"
        prop="collectionRate"
        show-overflow-tooltip
      />
      <el-table-column
        label="催记量"
        align="center"
        key="reminderQuantity"
        prop="reminderQuantity"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.reminderQuantity || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="坐席数量"
        align="center"
        key="numberSeats"
        prop="numberSeats"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.numberSeats || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="通话量"
        align="center"
        key="callVolume"
        prop="callVolume"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ proxy.formatAmountWithComma(row.callVolume || 0) }}
        </template>
      </el-table-column>
      <el-table-column
        label="外访覆盖率"
        align="center"
        key="countryCoverage"
        prop="countryCoverage"
        show-overflow-tooltip
      />
      <el-table-column
        label="回款率达成评分"
        align="center"
        key="serviceQuality"
        prop="serviceQuality"
        show-overflow-tooltip
      />
      <el-table-column
        label="回款率达成备注"
        align="center"
        key="serviceRemarks"
        prop="serviceRemarks"
        show-overflow-tooltip
      />
      <el-table-column
        label="合规作业评分"
        align="center"
        key="informationSafety"
        prop="informationSafety"
        show-overflow-tooltip
      />
      <el-table-column
        label="合规作业扣分"
        align="center"
        key="informationDeductPoints"
        prop="informationDeductPoints"
        show-overflow-tooltip
      />
      <el-table-column
        label="合规作业备注"
        align="center"
        key="informationRemarks"
        prop="informationRemarks"
        show-overflow-tooltip
      />
      <el-table-column
        label="案件投诉评分"
        align="center"
        key="complianceManagement"
        prop="complianceManagement"
        show-overflow-tooltip
      />
      <el-table-column
        label="案件投诉扣分"
        align="center"
        key="complianceDeductPoints"
        prop="complianceDeductPoints"
        show-overflow-tooltip
      />
      <el-table-column
        label="案件投诉备注"
        align="center"
        key="complianceRemarks"
        prop="complianceRemarks"
        show-overflow-tooltip
      />
            <el-table-column
        label="个人信息安全评分"
        align="center"
        key="personalScore"
        prop="personalScore"
        show-overflow-tooltip
      />
      <el-table-column
        label="个人信息安全扣分"
        align="center"
        key="personalDeductPoints"
        prop="personalDeductPoints"
        show-overflow-tooltip
      />
      <el-table-column
        label="个人信息安全备注"
        align="center"
        key="personalRemarks"
        prop="personalRemarks"
        show-overflow-tooltip
      />
      <el-table-column
        label="综合评分"
        align="center"
        key="comprehensiveScore"
        prop="comprehensiveScore"
        show-overflow-tooltip
      />
      <el-table-column
        label="生成日期"
        align="center"
        key="generationDate"
        prop="generationDate"
        show-overflow-tooltip
      />
      <el-table-column
        label="评价时间区间"
        align="center"
        key="evaluationTime"
        prop="evaluationTime"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { getTeamTree } from "@/api/team/team";
import { selectEvaluationForm ,insertEvaluationForm} from "@/api/team/teamevaluate";
const { proxy } = getCurrentInstance();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    generationDate: getData(),
    name: undefined,
    teamId:undefined
  },
});
const { queryParams } = toRefs(data);
const total = ref(0);
const loading = ref(false);
const dataList = ref([]);
const teamIds = ref([]);
const single = ref(true)
const selectedArr = ref([]);
const teamOptions = ref([])
//拆分的字段
const rangfiles = [
   "generationDate"
];
function getList() {
  loading.value = false;
  if(queryParams.value.name){
    queryParams.value.teamId = queryParams.value.name.split(":")[1]
  }
   let form = proxy.addFieldsRange(queryParams.value, rangfiles)
  selectEvaluationForm(form).then((res)=>{
    total.value = res.total;
    dataList.value = res.rows
  })
  .catch(() => {
      loading.value = false;
    });
}
getList();
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
function resetQuery() {
  proxy.resetForm("queryRef");
   queryParams.value =  {
    pageNum: 1,
    pageSize: 10,
    generationDate: getData(),
    name:undefined
  },
  handleQuery();
}

//获取机构名称
function getTeamOptions(val) {
  if (!val) return;
  getTeamTree()
    .then((res) => {
      teamOptions.value = res.data;
    })
    .finally(() => {
    });
}

function exportData() {
  let req = {
    generationDate1:queryParams.value.generationDate?.[0],
    generationDate2:queryParams.value.generationDate?.[1],
    ids:teamIds.value
  }
  proxy.download(
    "/team/create/export",
    req,
    `tpl_机构评价表.xlsx`
  );
}

//选择列表
function handleSelectionChange(selection) {
  teamIds.value = selection.map((item) => item.id);
  single.value = !(selection.length > 0);
  selectedArr.value = selection;
}

//格式化时间
function getData(){
    var date = new Date();
    date.setDate(1)
    var before = new Date(date.getFullYear(), date.getMonth() + 1, 0); //前一天
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate(1);

    var beforeMonth = before.getMonth() + 1;
    var beforeDay = before.getDate(0);

    month = month >= 10 ? month : '0' + month;
    day = day >= 10 ? day : '0' + day;
    beforeMonth = beforeMonth >= 10 ? beforeMonth : '0' + beforeMonth
    beforeDay = beforeDay >= 10 ? beforeDay : '0' + beforeDay

    return [`${year}-${month}-${day}`,`${year}-${beforeMonth}-${beforeDay}`]
}

</script>

<style lang="scss" scoped></style>
