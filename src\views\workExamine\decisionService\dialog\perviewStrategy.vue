<template>
  <el-dialog v-model="open" width="50vw" :before-close="cancel" append-to-body>
    <div class="body">
      <div class="title">预览分案决策引擎</div>
      <div class="content-list">
        <div class="content-item">
          <div class="left">分案决策名称</div>
          <div class="right text-center">{{ form.decisionName }}</div>
        </div>
        <div class="content-item">
          <div class="left">分案规则-分案节点</div>
          <div class="right node-list">
            <div class="node-item">
             <div class="right" v-html=" form.decisionDetail"></div>
            </div>
          </div>
        </div>
        <div class="content-item">
          <div class="left">每日分案时间</div>
          <div class="right">{{ form.divisionTime }}</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup name="perviewStrategy">
import { perviewStrategyApi } from "@/api/workExamine/decisionService";
//全局属性
const { proxy } = getCurrentInstance();
const open = ref(false)
const form = ref({})
//开启弹窗
function opendialog(row) {
  open.value = true;
  form.value = row
  getData(row)
}

// 获取数据
function getData(row) {
  perviewStrategyApi({ id: row.id }).then(res => {
    form.value = res.data
  })
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//重置
function reset() {
  open.value = false;
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
.body {
  .title {
    text-align: center;
    line-height: 44px;
    color: #409eff;
    background-color: #eceef4;
  }

  .content-list {
    border: 1px solid #f5f5f5;

    .content-item {
      display: flex;
      border-bottom: 1px solid #f5f5f5;

      .left {
        display: flex;
        width: 240px;
        min-height: 44px;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #f5f5f5;
      }

      .right {
        flex: 1;
        padding: 10px;
      }

      .node-list {
        .node-item {
          margin-bottom: 20px;

          &:last-of-type {
            margin-bottom: 0 !important;
          }

          .node-title {
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
