import request from '@/utils/request'

// 新增项目建账
export const zcAddApprove = (data) => request({
    url: 'caseManage/zws_zc_approve/zcAddApprove',
    method: 'post',
    data
})

// 项目建账-列表查询
export const selectListProjectsAccounting = (query) => request({
    url: 'caseManage/projectsAccounting/selectListProjectsAccounting',
    method: 'get',
    params: query
})

// 查询支付建账详情
export const selectPaymentAccountApplication = (data) => request({
    url: 'caseManage/projectsAccounting/selectPaymentAccountApplication',
    method: 'post',
    data
})

// 查询建账附件
export const selectInitiationFileById = (query) => request({
    url: '/caseManage/projectsAccounting/selectInitiationFileById',
    method: 'get',
    params: query
})

// 查询建账tab数量
export const groupAccountingStatus = (query) => request({
    url: '/caseManage/projectsAccounting/groupAccountingStatus',
    method: 'get',
    params: query
})

// 撤销建账
export const zcApproveRevoke = (data) => request({
    url: 'caseManage/zws_zc_approve/zcApproveRevoke',
    method: 'post',
    data
})

// 获取审批流程
export function getAccountingApprovalProcess(data) {
    return request({
        url: '/caseManage/projectsAccounting/selectApprovalProcess',
        method: 'post',
        data: data
    })
}
