//防抖
let antiShakeTimeout = null;

export function antiShakeMy(fn, delay = 500) {
  if (!antiShakeTimeout) {
    fn();
    antiShakeTimeout = setTimeout(() => {
      antiShakeTimeout = null;
    }, delay);
  } else {
    ElMessage({
      message: "您点击的太频繁了，请休息一下再点！",
      type: "warning",
      duration: 1000,
    });
  }
}

//将所有空属性改为 -- ，除了日期
export function fillEmptyToDash(data, keysToDash = []) {
  if (Array.isArray(data)) {
    return data.map(item => fillEmptyToDash(item, keysToDash));
  } else if (data && typeof data === "object") {
    const newObj = {};
    for (const key in data) {
      if (!data.hasOwnProperty(key)) continue;
      const value = data[key];
      // 不处理 approveId、id、approveCode 字段
      if (["approveid", "id", "approvecode","projectid"].includes(key.toLowerCase())) {
        newObj[key] = value;
        continue;
      }
      if (key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) {
        newObj[key] = value;
        continue;
      }
      // 只对 keysToDash 中的 key 做 '--' 替换
      if (keysToDash.includes(key)) {
        if (value === null || value === undefined || value === "") {
          newObj[key] = "--";
        } else if (typeof value === "object") {
          newObj[key] = fillEmptyToDash(value, keysToDash);
        } else {
          newObj[key] = value;
        }
      } else {
        // 其它 key 保持原值
        newObj[key] = value;
      }
    }
    return newObj;
  }
  return data;
}
