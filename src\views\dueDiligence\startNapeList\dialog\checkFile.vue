<template>
    <el-dialog :title="title" v-model="open" width="400px" :before-close="cancel">
      <el-table :data="fileList" border style="width: 100%">
        <el-table-column prop="name" label="文件名">
          <template #default="{ row }">
            <el-link type="primary" :href="row.fileUrl" target="_blank" >{{ row.firstName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-link type="primary"   :href="row.fileUrl" >下载</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref, defineProps, defineExpose } from 'vue';
  
  const props = defineProps({
    title: { type: String, default: '文件列表' }
  });
  
  const open = ref(false);
  const fileList = ref([]);
  
  // 打开弹窗，传入文件数组和可选标题
  function openDialog(files, dialogTitle) {
    fileList.value = files;
    if (dialogTitle) {
      props.title = dialogTitle;
    }
    open.value = true;
  }

  
  function cancel() {
    open.value = false;
  }
  
  defineExpose({ openDialog });
  </script>