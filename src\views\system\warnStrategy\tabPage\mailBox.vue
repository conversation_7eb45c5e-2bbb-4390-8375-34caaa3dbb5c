<template>
    <div class="ml20">
        <div class="operation-revealing-area mb20">
            <div class="operation-revealing-area-btn">
                <el-button :loading="loading" type="primary" @click="update()">添加邮箱</el-button>
            </div>
            <right-toolbar :columns="columns" @queryTable="getList" :types="[2, 3]" />
        </div>
        <div class="table-box">
            <el-table :data="dataList" :loading="loading">
                <el-table-column v-if="columns[0].visible" align="center" prop="emailName" sortable label="邮箱名称" />
                <el-table-column v-if="columns[1].visible" align="center" prop="emailAccount" sortable label="邮箱账号" />
                <el-table-column v-if="columns[2].visible" align="center" prop="password" sortable label="服务密码" />
                <el-table-column v-if="columns[3].visible" align="center" prop="status" sortable
                    :formatter="row => ({ 0: '启用', 1: '禁用' }[row.status])" label="状态" />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <div>
                            <el-button :loading="loading" type="text" @click="update(row)">编辑</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <mailBoxUpdateInfo ref="mailBoxUpdateInfoRef" />
    </div>
</template>

<script setup>
import mailBoxUpdateInfo from '../dialog/mailBoxUpdateInfo';
const { proxy } = getCurrentInstance()
const data = reactive({
    queryParams: { pageNum: 1, pageSize: 10 }
})
const total = ref(1)
const dataList = ref([
    { "createBy": "admin", "createTime": "2024-08-16 11:58:48", "id": 22, "emailName": "<EMAIL>", "emailAccount": "<EMAIL>", "password": "admin123", "address": "fdsfdvdf", "port": "233", "status": 0, "delFlag": 0, }
])
const loading = ref(false)
const { queryParams } = toRefs(data)
const columns = ref([
    { "key": 0, "label": "邮箱名称", "visible": true },
    { "key": 1, "label": "邮箱账号", "visible": true },
    { "key": 2, "label": "服务密码", "visible": true },
    { "key": 3, "label": "状态", "visible": true },
])
function update(row) {
    const title = row ? '编辑' : '添加'
    proxy.$refs['mailBoxUpdateInfoRef'].openDialog({ row, title })
}
</script>

<style lang="scss" scoped>
.operation-revealing-area-btn {
    display: flex;
    align-items: center;
}
</style>