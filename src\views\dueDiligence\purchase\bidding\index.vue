<template>
  <div class="app-container">
    <el-form
      inline
      label-width="100px"
      ref="queryRef"
    >
      <el-form-item prop="projectId" label="项目ID">
        <el-select
          style="width: 320px"
          v-model="queryParams.projectId"
          placeholder="请输入或选择项目ID"
          filterable
          multiple
          :reserve-keyword="false"
        >
          <el-option
            v-for="item in projectIdOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <el-select
          v-model="queryParams.projectName"
          filterable
          multiple
          :reserve-keyword="false"
          placeholder="请输入或选择项目名称"
          style="width: 320px"
        >
          <el-option
            v-for="item in projectNameOptions"
            :key="Math.random()"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="transferorId" label="资产转让方">
        <el-select
          v-model="queryParams.transferorId"
          collapse-tags
          collapse-tags-tooltip
          multiple
          placeholder="请输入或选择资产转让方"
          clearable
          filterable
          :reserve-keyword="false"
          style="width: 320px"
        >
          <el-option
            v-for="item in transferorOptions"
            :key="item.code"
            :label="item.info"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select
          v-model="queryParams.productType"
          filterable
          :reserve-keyword="false"
          placeholder="请输入或选择产品类型"
          multiple
          style="width: 320px"
        >
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <template v-if="showSearch">
        <el-form-item prop="biddingMethod" label="投标方式">
          <el-select
            v-model="queryParams.biddingMethod"
            collapse-tags
            collapse-tags-tooltip
            multiple
            placeholder="请输入或选择投标方式"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in biddingMethodOption"
              :key="item.dictLabel"
              :label="item.dictLabel"
              :value="item.dictLabel"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="sealType" label="印章类型">
          <el-select
            v-model="queryParams.sealType"
            collapse-tags
            collapse-tags-tooltip
            multiple
            placeholder="请输入或选择印章类型"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in sealTypeOption"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="isSeal" label="是否用印">
          <el-select
            v-model="queryParams.isSeal"
            collapse-tags
            collapse-tags-tooltip
            placeholder="请输入或选择是否用印"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in isSealOption"
              :key="item.dictLabel"
              :label="item.dictLabel"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报价金额">
          <div class="range-area">
            <el-input v-model="queryParams.priceMin" />
            <span>-</span>
            <el-input v-model="queryParams.priceMax" />
          </div>
        </el-form-item>
        <el-form-item prop="createById" label="申请人">
          <el-select
            v-model="queryParams.createById"
            collapse-tags
            collapse-tags-tooltip
            multiple
            placeholder="请输入或选择申请人"
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 320px"
          >
            <el-option
              v-for="item in createByOptions"
              :key="item.code"
              :label="item.info"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="createTime" label="申请时间">
          <el-date-picker
            v-model="createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            clearable
            unlink-panels
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 320px"
          />
        </el-form-item>
      </template>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <el-button type="primary"  @click="handleRevoke()"> 撤销</el-button>
         <!-- 全选功能 -->
         <div class="this-page-selected" style="flex">
            <el-checkbox-group v-model="checkedType" @change="checkedTypeChange" :disabled="dataList.length === 0">
                <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
                    :indeterminate="item.indeterminate" />
            </el-checkbox-group>
            <span class="this-danger-parent">项目量（件）：<i class="danger">{{ total }}</i></span>
        </div>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(tabChangeList)">
      <el-tab-pane
        v-for="(item, index) in erectNapeEnum"
        :key="index"
        :label="item.info"
        :name="index"
      />
    </el-tabs>
    <div class="table-box">
      <el-table :data="dataList" :loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="30px"
          align="right"
          :selectable="checkSelectable"
        />
        <el-table-column
          label="项目ID"
          v-if="columns[0].visible"
          align="center"
          prop="projectId"
        />
        <el-table-column
          label="项目名称"
          v-if="columns[1].visible"
          align="center"
          prop="projectName"
        >
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              :content="row.projectName"
              placement="top"
              :disabled="!(row.projectName && row.projectName.length > 20)"
            >
              <el-link
                type="primary"
                :underline="false"
                @click="handleDetails(row)"
              >
                {{
                  row.projectName
                    ? row.projectName.length > 20
                      ? row.projectName.slice(0, 20) + '...'
                      : row.projectName
                    : ''
                }}
              </el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="资产转让方"
          v-if="columns[2].visible"
          align="center"
          prop="transferor"
        />
        <el-table-column
          label="产品类型"
          v-if="columns[3].visible"
          align="center"
          prop="productType"
        />
        <el-table-column
          label="竞价状态"
          v-if="columns[4].visible"
          align="center"
          prop="biddingStatus"
        />
        <el-table-column
          label="流程标题"
          v-if="columns[5].visible"
          align="center"
          prop="title"
        />
        <el-table-column
          label="投标方式"
          v-if="columns[6].visible"
          align="center"
          prop="biddingMethod"
        />
        <el-table-column
          label="报价金额（元）"
          v-if="columns[7].visible"
          align="center"
          prop="price"
        />
        <el-table-column
          label="是否用印"
          v-if="columns[8].visible"
          align="center"
          prop="isSeal"
        >
          <template #default="{ row }">
            {{ getDictLabel(isSealOption, row.isSeal) }}
          </template>
        </el-table-column>
        <el-table-column
          label="印章类型"
          v-if="columns[9].visible"
          align="center"
          prop="sealType"
        />
        <el-table-column
          label="申请人"
          v-if="columns[10].visible"
          align="center"
          prop="createBy"
        />
        <el-table-column
          label="申请时间"
          v-if="columns[11].visible"
          align="center"
        >
          <template #default="{ row }">
            {{
              row.createTime
                ? dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")
                : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="180" label="操作">
          <template #default="{ row }">
            <div>
              <el-button
                type="text"
                v-if="
                  ['待发起', '竞价失败', '已撤销'].includes(row.biddingStatus)
                "
                @click="handleAdd(row)"
                >发起竞价</el-button
              >
              <el-button
                type="text"
                v-if="['竞价待审批'].includes(row.biddingStatus)"
                @click="handleRevoke(row)"
                >撤销</el-button
              >
              <el-button
                v-if="!['待发起'].includes(row.biddingStatus)"
                type="text"
                @click="handleDetails(row)"
                >详情</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup name="StartNapeList">
import { getDictProductType } from "@/api/assets/assetside";
import { getOwners } from "@/api/assets/casemanage";
import {
  getBiddingList,
  bindDateRangeToQueryParams,
} from "@/api/conference/projectInfo";
import { fillEmptyToDash } from "@/api/conference/utils";
import {
  listAndQuery,
  getProjectIdList,
  getProjectNameList,
  getProductTypeList,
  getTransferorList,
  getCreateByList,
  getApproveList,
  getDict,
  getDictLabel,
  getCountStatus,
  getAllCountStatus,
  revokeProjectApproval,
  revokeFormAll
} from "@/api/conference/biddings";
import dayjs from "dayjs";
import { selectListBiddingAcquisition } from "@/api/dueDiligence/bidding";
import { watch } from "vue";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const data = reactive({
  queryParams: { pageNum: 1, pageSize: 10 },
});
const createTime = ref([]);
const reviewTime = ref([]);
const options = ref([]);
const projectIdOptions = ref([]);
const projectNameOptions = ref([]);
const transferorOptions = ref([]);
const createByOptions = ref([]);
const approveOptions = ref([]);
const countStatus = ref([]);
const allCountStatus = ref([]);
const selectedArr = ref([]);
const allQuery = ref(false)
const isSealOption = [
  { code: 0, dictValue: false, dictLabel: "否" },
  { code: 1, dictValue: true, dictLabel: "是" },
];
const biddingMethodOption = ref([]);
const sealTypeOption = ref([]);
const ownerOption = ref([]);

const erectNapeEnum = ref([
  { code: "", info: "待发起竞价" },
  { code: "", info: "竞价待审批" },
  { code: "", info: "竞价审批中" },
  { code: "", info: "竞价失败" },
  { code: "", info: "竞价成功" },
  { code: "", info: "已撤销" },
  { code: "", info: "全部" },
]);

const checkedType = ref([]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

const activeName = ref(6);
const total = ref(1);
// 列表数据
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const { queryParams } = toRefs(data);
const columns = ref([
  { key: 0, label: "项目ID", visible: true },
  { key: 1, label: "项目名称", visible: true },
  { key: 2, label: "资产转让方", visible: true },
  { key: 3, label: "产品类型", visible: true },
  { key: 4, label: "竞价状态", visible: true },
  { key: 5, label: "流程标题", visible: true },
  { key: 6, label: "投标方式", visible: true },
  { key: 7, label: "报价金额（元）", visible: true },
  { key: 8, label: "是否用印", visible: true },
  { key: 9, label: "印章类型", visible: true },
  { key: 10, label: "申请人", visible: true },
  { key: 11, label: "申请时间", visible: true },
]);

const tableProps = [
  'projectId',
  'projectName',
  'transferor',
  'productType',
  'biddingStatus',
  'title',
  'biddingMethod',
  'price',
  'isSeal',
  'sealType',
  'createBy',
  'createTime'
];

const dictList = [
  {
    ruleName: "bidding_method",
    targetRef: biddingMethodOption,
  },
  {
    ruleName: "seal_type",
    targetRef: sealTypeOption,
  },
];

dictList.forEach((item) => {
  getDict({ ruleName: item.ruleName })
    .then((res) => {
      item.targetRef.value = res.data;
    })
    .catch((error) => {
      console.error(`获取 ${item.ruleName} 字典数据失败:`, error);
    });
});

// 获取转让方
getOwners().then((res) => {
  ownerOption.value = res.data;
});

// //获取列表数据
// getBiddingList().then((res) => {
//   dataList.value = res.rows;
//   total.value = res.total;
//   loading.value = false;
// });

// //获取表格数据
listAndQuery({ pageNum: 1, pageSize: 10 }).then((res) => {
  dataList.value = res.rows;
  dataList.value = fillEmptyToDash(res.rows,tableProps);
  total.value = res.total;
});

//获取项目id下拉框数据
getProjectIdList().then((res) => {
  projectIdOptions.value = res.data;
});

//获取项目名称下拉框数据
getProjectNameList().then((res) => {
  projectNameOptions.value = res.data;
});

//获取产品类型下拉框数据
getProductTypeList().then((res) => {
  options.value = res.data;
});

//获取资产转让方下拉框数据
getTransferorList().then((res) => {
  transferorOptions.value = res.data;
});

//获取申请人下拉框数据
getCreateByList().then((res) => {
  createByOptions.value = res.data;
});

//获取分组数量
getCountStatus().then((res) => {
  countStatus.value = res.data;
});

//监听日期选择
watch(reviewTime, (val) => {
  if (val && val.length > 0) {
    queryParams.value.approveTimeStart = val[0];
    queryParams.value.approveTimeEnd = val[1];
  } else {
    delete queryParams.value.approveTimeStart;
    delete queryParams.value.approveTimeEnd;
  }
});

watch(() => queryParams.value.pageNum, () => {
  nextTick(() => {
    proxy.$refs.multipleTableRef?.clearSelection()
  })
})


watch(createTime, (val) => {
  if (val && val.length > 0) {
    queryParams.value.createTimeStart = val[0];
    queryParams.value.createTimeEnd = val[1];
  } else {
    delete queryParams.value.createTimeStart;
    delete queryParams.value.createTimeEnd;
  }
});

//设置tab label数量显示
watch(countStatus, (newVal) => {
  erectNapeEnum.value.forEach((tab) => {
    const baseName = tab.info.split('(')[0].trim();
    if (baseName === "全部") {
      const total = newVal.reduce((sum, item) => sum + (item.count || 0), 0);
      tab.info = `全部 (${total})`;
    } else {
      const found = newVal.find(item => item.biddingStatus === baseName);
      tab.info = found ? `${baseName} (${found.count})` : `${baseName} (0)`;
    }
  });
}, { immediate: true });

function getList() {
  listAndQuery(queryParams.value)
    .then((res) => {
      dataList.value = fillEmptyToDash(res.rows,tableProps);
      total.value = res.total;
      if (checkedType.value[0] === "本页选中") {
        checkedType.value = [];
        proxy.$refs["multipleTableRef"]?.clearSelection();
      }
    })
    .catch((err) => {
      console.error("获取表格数据失败:", err);
    });
}
function resetQuery() {
  queryParams.value = { pageNum: 1, pageSize: 10 };
  // queryParams.pageNum = 1;
  // queryParams.pageSize = 10;
  createTime.value = [];
  reviewTime.value = [];
  checkedType.value = [];
  proxy.$refs["multipleTableRef"]?.clearSelection();
  getList();
}
//搜索查询
function handleQuery() {
  queryParams.value.pageNum = 1;
  queryParams.value.status = activeName.value !== 6 ? activeName.value : "";
  getList()
    
}
//tab切换数据变换
function tabChangeList() {
  queryParams.value.status = activeName.value !== 6 ? activeName.value : "";
  getList();
}

function handleOpenDailog(refName) {
  proxy.$refs[refName].openDialog();
}

function handleDetails(row) {
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}
  const query = {
    path: route.path,
    pageType: "bidding",
    progressStatus: 2,
    isDetails: 1,
    activeTab: "1",
    rowData: JSON.stringify(rowData),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}
// 发起竞价
function handleAdd(row) {
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}
  const query = {
    path: route.path,
    pageType: "bidding",
    progressStatus: 2,
    isDetails: 0,
    activeTab: "1",
    rowData: JSON.stringify(rowData),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}


//全选类型
function checkedTypeChange(val) {
    checkedType.value.length > 1 && checkedType.value.shift(); //单选
    if (checkedType.value.length === 0) {
        //全不选
        proxy.$refs["multipleTableRef"].clearSelection();
        checkStatus.value[0].indeterminate = false;
    } else if (checkedType.value?.[0] == '搜索结果全选') {
        nextTick(() => {
            dataList.value.length > 0 &&
                dataList.value.map((item) => {
                    proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
                });
        })
    } else {
        dataList.value.length > 0 &&
            dataList.value.map((item) => {
                proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
            });
    }
    if (checkedType.value[0] == "搜索结果全选") {
        checkStatus.value[0].indeterminate = false;
        queryParams.value.allQuery = true;
        allQuery.value = true;

    } else {
        queryParams.value.allQuery = false;
        allQuery.value = false;

    }
}

//选择列表
let isCheck =false
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  isCheck = true
  if(isCheck)return
  if (
    
      checkedType.value[0] === '本页选中' &&
      selection.length !== dataList.value.length
    ) {
      checkedType.value = [];
    }
}

//表格行能否选择
function checkSelectable() {
    return !queryParams.value.allQuery;
}
//表格选中
function selectTable() {
    return new Promise((reslove, reject) => {
        try {
            dataList.value.map((item, index) => {
                proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
            });
            reslove(true);
        } catch (error) {
            reject(error);
        }
    });
}

watch(() => dataList.value, (newval, preval) => {
    if (newval.length > 0) {
        //处理禁用表格复选框时无法选中的情况
        if (queryParams.value.allQuery) {
            queryParams.value.allQuery = false;
            nextTick(() => {
                selectTable()
                    .then((res) => { })
                    .finally(() => {
                        queryParams.value.allQuery = true;
                    });
            });
        } 
      
    }
});


function handleRevoke(row) {
  // 批量撤销时，先判断是否有选中
  if (!row && (!selectedArr.value || selectedArr.value.length === 0) && !allQuery.value) {
    proxy.$modal.msgWarning("请先选择要撤销的合同！");
    return;
  }
  proxy.$modal
    .confirm("此操作将撤销选中的申请，是否继续？？", "撤销提示")
    .then(() => {
      if (allQuery.value) {
        // 搜索全选模式
        const { pageSize, pageNum, ...restQueryParams } = queryParams.value;
        revokeFormAll(restQueryParams);
      } else {
        // 普通模式
        let data = {
          approveCode: "bidding",
          allQuery: false,
          approvePageRequest: {
            pageSize:10,
            pageNum:1,
            approveData: {
              pageSize: queryParams.value.pageSize,
              pageNum: queryParams.value.pageNum,
              allQuery: false,
            }
          }
        };
        if (row) { // 行内撤销

          data.approveIds = [row.approveId=="--"?'':row.approveId];
        } else {

          data.approveIds = selectedArr.value
            .filter(item => item.approveId !== null)
            .map(item => item.approveId);
        }
        revokeProjectApproval(data);
        getList();
      }
    });
}
</script>

<style lang="scss" scoped>
  .this-page-selected {
    display: flex;
    align-items: center;
    gap: 16px; 
  }

  .item-count {
    margin-left: 16px; 
  }

  .this-danger-parent,.danger {
    font-size: 14px;
  }
</style>
