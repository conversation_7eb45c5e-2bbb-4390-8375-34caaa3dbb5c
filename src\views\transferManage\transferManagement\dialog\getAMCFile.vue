<template>
    <el-dialog title="审批AMC获取文件" v-model="open" append-to-body width="650px" :before-close="cancel">
        <div class="info-list">
            <div class="info-item">
                <div>AMC的获取API是否可用：</div>
                <div class="blue">已对接</div>
            </div>
            <div class="info-item" v-for="v in infoList" :key="v">
                <div>{{ v.label }}：</div>
                <div :class="form[v.prop] ? 'blue' : 'red'">{{ v.enum[form[v.prop]] }}</div>
                <el-switch v-show="!v.isNoSwitch" v-model="form[v.prop]" :inactive-value="0" :active-value="1" />
            </div>
        </div>
        <template #footer>
            <div class="text-center">
                <el-button @click="cancel">取消</el-button>
                <el-button @click="submit" type="primary">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
const updateStep = ref(null)
const form = ref({
    baseStatus: 1,
    reportStatus: 0,
    submitStatus: 0,
    deliveryStatus: 0,
    abutmentStatus: 0,
})
const isCreateEnum = { 0: '未生成', 1: '已生成' }
const isUsableEnum = { 0: '不可用', 1: '可用' }
const open = ref(false)
const infoList = ref([
    { label: '组包阶段-基础材料', prop: 'baseStatus', enum: { 0: '不可获取', 1: '可获取' } },
    { label: '尽调阶段-尽调报告', prop: 'reportStatus', enum: { 0: '未禁止', 1: '已禁止' } },
    { label: '确认阶段-确认书', prop: 'submitStatus', enum: isCreateEnum },
    { label: '交割阶段-交割数据', prop: 'deliveryStatus', enum: isCreateEnum },
    { label: '交割对接-案件信息接口', prop: 'abutmentStatus', enum: isUsableEnum },
    { label: '·交割对接-还款计划', prop: 'abutmentStatus', enum: isUsableEnum, isNoSwitch: true },
    { label: '·交割对接-催收记录', prop: 'abutmentStatus', enum: isUsableEnum, isNoSwitch: true },
    { label: '·交割对接-法诉情况', prop: 'abutmentStatus', enum: isUsableEnum, isNoSwitch: true },
    { label: '·交割对接-档案计划', prop: 'abutmentStatus', enum: isUsableEnum, isNoSwitch: true },
])
function submit() {
    cancel()
}
function openDialog(data) {
    open.value = true
}
function cancel() {
    open.value = false
    updateStep.value = null
}
defineExpose({ openDialog })
</script>

<style lang="scss" scoped>
.info-list {
    border: 1px solid #E8E8E8;

    .info-item {
        display: grid;
        padding: 20px;
        border-bottom: 1px solid #E8E8E8;
        grid-template-columns: 5fr 1fr 1fr;

        &:last-of-type {
            border-bottom: none;
        }
    }
}

.blue {
    color: #409eff;
}

.red {
    color: red;
}
</style>