import request from "@/utils/request";

//列表查询
export function listAndQuery(query) {
  return request({
    url: "/caseManage/acquisition/selectListMeetingDecision",
    method: "get",
    params: query,
  });
}

//项目id下拉框
export function getProjectIdList() {
  return request({
    url: "/caseManage/acquisition/selectProjectIdMeeting",
    method: "get",
  });
}

//项目名称下拉框
export function getProjectNameList() {
  return request({
    url: "/caseManage/acquisition/selectProjectNameMeeting",
    method: "get",
  });
}

//产品类型下拉框
export function getProductTypeList() {
  return request({
    url: "/caseManage/acquisition/selectProductTypeMeeting",
    method: "get",
  });
}

//资产转让方下拉框
export function getTransferorList() {
  return request({
    url: "/caseManage/acquisition/selectTransferorMeeting",
    method: "get",
  });
}

//决策文号下拉框
export function getDecisionNumberList() {
  return request({
    url: "/caseManage/acquisition/selectDecisionNumber",
    method: "get",
  });
}

//创建人下拉框
export function getCreateByList() {
  return request({
    url: "/caseManage/acquisition/selectCreateByMeeting",
    method: "get",
  });
}

//查看附件
export function getCheckFile(id) {
  return request({
    url: `/caseManage/acquisition/selectMeetingDecisionFile/${id}`,
    method: "get",
  });
}

//分组统计状态数量
export function getCountStatus() {
  return request({
    url: "/caseManage/acquisition/groupMeetingDecisionCount",
    method: "get",
  });
}

//查看详情
export function getDetail(id) {
  return request({
    url: `/caseManage/acquisition/selectMeetingDecision/${id}`,
    method: "get",
  });
}