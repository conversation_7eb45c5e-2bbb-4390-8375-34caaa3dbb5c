<template>
  <div class="white-list">
    <el-form :model="form" :rules="rules" ref="whiteFormRef" label-width="94px">
      <el-form-item label="白名单名称" prop="whitelistName">
        <el-input
          v-model="form.whitelistName"
          :disabled="state.whitelistStatus === 0"
          placeholder="请输入"
          style="width: 440px"
        ></el-input>
      </el-form-item>
      <el-form-item label="IP地址" prop="addressIp">
        <el-input
          v-model="form.addressIp"
          :disabled="state.whitelistStatus === 0"
          placeholder="示例：***************;***************"
          style="width: 440px"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="note">
        <el-input
          v-model="form.note"
          :disabled="state.whitelistStatus === 0"
          type="textarea"
          show-word-limit
          placeholder="请输入内容"
          maxlength="100"
          style="width: 440px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        注：若不知道IP地址是多少，可以自行百度“IP地址“查询自己电脑IP地址
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const validateIP = (rule, value, callback) => {
  var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入IP地址！"));
  } else {
    let arr = value.split(";");
    let res = false;
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] && arr[i] != "" && !reg.test(arr[i])) {
        res = false;
        break;
      } else {
        res = true;
      }
    }
    if (!res) {
      callback(new Error("请输入正确的IP地址！"));
    } else {
      callback();
    }
  }
};

const form = inject("white");
const state = inject("state");

const data = reactive({
  rules: {
    whitelistName: [{ required: true, message: "请输入白名单名称", trigger: "blur" }],
    addressIp: [{ required: true, validator: validateIP, trigger: "blur" }],
  },
});
const { rules } = toRefs(data);

//检验表单
function validateForm() {
  return new Promise((reslove) => {
    proxy.$refs["whiteFormRef"].validate((valid) => {
      reslove(valid);
    });
  });
}

//清空表单
function reset() {
  proxy.resetForm("whiteFormRef");
}

defineExpose({
  validateForm,
  reset,
});
</script>

<style scoped>
.white-list {
  width: 538px;
  margin: 0 auto;
}
</style>
