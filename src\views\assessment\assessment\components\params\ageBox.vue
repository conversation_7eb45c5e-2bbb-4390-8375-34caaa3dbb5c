<template>
  <div class="age-box" id="age-box">
    <div class="box-title">{{ title }}</div>
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-table :data="form.dataList">
        <el-table-column>
          <template #default="{ row, $index }">
            <el-form-item :prop="`dataList.${$index}.startNumeric`" :rules="rules.startNumeric">
              <el-input
                v-model="row.startNumeric"
                placeholder="请填写"
                @blur="checkStartAndEnd($index)"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default>
            <span>≤ 年龄区间 ＜</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template #default="{ row, $index }">
            <el-form-item :prop="`dataList.${$index}.endNumeric`" :rules="rules.endNumeric">
              <el-input
                style="width:80%"
                @blur="checkStartAndEnd($index, 1)"
                v-model="row.endNumeric"
                placeholder="请填写"
              />
              <span style="width:20%;text-align:center">岁</span>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column>
          <template #default="{ row, $index }">
            <el-tooltip content="新增" placement="top">
              <el-button type="text" icon="CirclePlus" @click="addtab($index)"></el-button>
            </el-tooltip>
            <el-tooltip content="上移" placement="top">
              <el-button
                type="text"
                icon="Top"
                :disabled="$index == 0"
                @click="moveUp($index, row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="下移" placement="top">
              <el-button
                type="text"
                :disabled="$index == form.dataList.length - 1"
                @click="moveDown($index, row)"
                icon="Bottom"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                type="text"
                icon="CircleClose"
                :disabled="form.dataList.length == 1"
                @click="remove(row, $index)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script setup>
//全局数据
const { proxy } = getCurrentInstance();
const emit = defineEmits(["update:dataList"]);
const props = defineProps({
  title: { type: String, default: "年龄区间" },
  dataList: { type: Array, required: true }
});
//表单信息
const data = reactive({
  form: {
    dataList: [
      {
        startNumeric: undefined,
        endNumeric: undefined
      }
    ]
  },
  rules: {
    startNumeric: [
      { required: false, message: "请填入开始年龄区间", trigger: "blur" },
      {
        pattern: /^[0-9]+.?[0-9]*$|\+/,
        message: "请输入数字或者符号'+'",
        trigger: "blur"
      }
    ],
    endNumeric: [
      { required: false, message: "请填入结束年龄区间", trigger: "blur" },
      {
        pattern: /^[0-9]+.?[0-9]*$|\+/,
        message: "请输入数字或者符号'+'",
        trigger: "blur"
      }
    ]
  }
});
const { form, rules } = toRefs(data);

//添加行
function addtab(index) {
  let row = {
    startNumeric: undefined,
    endNumeric: undefined
  };
  form.value.dataList.splice(index + 1, 0, row);
  nextTick(() => {
    const endAmount = dataList[dataList.length - 1].endAmount;
    dataList[dataList.length - 1].endAmount = endAmount ? endAmount : "+";
  });
  const dataList = form.value.dataList;
}
//删除行
function remove(row, index) {
  form.value.dataList.splice(index, 1);
}
//上移
function moveUp(index, row) {
  if (!row.startNumeric || !row.endNumeric) {
    return proxy.$modal.msgWarning("请输入对应的值之后再进行移动");
  }
  let upData = form.value.dataList[index - 1];
  form.value.dataList.splice(index - 1, 1);
  form.value.dataList.splice(index, 0, upData);
  checkStartAndEnd();
}
//下移
function moveDown(index, row) {
  if (!row.startNumeric || !row.endNumeric) {
    return proxy.$modal.msgWarning("请输入对应的值之后再进行移动");
  }
  let downData = form.value.dataList[index + 1];
  form.value.dataList.splice(index + 1, 1);
  form.value.dataList.splice(index, 0, downData);
  checkStartAndEnd();
}
//检测开始数字与结束数字
function checkStartAndEnd(tableIndex, focusVal) {
  proxy.$refs["formRef"].validate(valid => {
    let dataList = JSON.parse(JSON.stringify(form.value.dataList));
    dataList.forEach((item, index) => {
      if (!focusVal && item.startNumeric && item.startNumeric == "+") {
        item.startNumeric = "";
        return proxy.$modal.msgWarning('请不要在前区间输入"+"');
      }
      if (
        (item.startNumeric &&
          item.startNumeric != "+" &&
          Number(item.startNumeric) > 200) ||
        (item.startNumeric &&
          item.startNumeric != "+" &&
          Number(item.startNumeric) < 0)
      ) {
        item.startNumeric = "";
        return proxy.$modal.msgWarning("年龄不能大于200或小于0");
      }
      if (
        (item.endNumeric &&
          item.endNumeric != "+" &&
          Number(item.endNumeric) > 200) ||
        (item.endNumeric &&
          item.endNumeric != "+" &&
          Number(item.endNumeric) < 0)
      ) {
        item.endNumeric = "";
        return proxy.$modal.msgWarning("年龄不能大于200或小于0");
      }
      if (
        item.startNumeric &&
        item.endNumeric != "+" &&
        Number(item.startNumeric) >= Number(item.endNumeric)
      ) {
        item.endNumeric = "";
        return proxy.$modal.msgWarning("年龄前区间不能大于后区间");
      }
    });
    dataList = dataList.map((v, i) => ({ ...v, sort: i }));
    if (tableIndex == 0 && focusVal) {
      dataList[tableIndex + 1].startNumeric = dataList[tableIndex].endNumeric;
    }
    if (tableIndex < dataList.length - 1 && !focusVal) {
      dataList[tableIndex - 1].endNumeric = dataList[tableIndex].startNumeric;
    }
    form.value.dataList = dataList;
    props.dataList = dataList;
    emit("update:dataList", dataList);
  });
}
watch(
  () => props.dataList,
  val => {
    form.value.dataList =
      props.dataList.length > 0
        ? props.dataList
        : [
            {
              startNumeric: undefined,
              endNumeric: undefined
            }
          ];
  }
);
</script>
<style lang="scss" scoped>
:deep(.el-table thead) {
  display: none;
}

:deep(.el-table td.el-table__cell) {
  border: none;
}

:deep(.el-table__inner-wrapper::before) {
  content: none;
}

:deep(.el-icon) {
  width: 18px;
  height: 18px;
}

:deep(.el-form-item__error) {
  position: unset;
}

.age-box {
  padding: 20px 20px 40px 20px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 10px;
  margin-bottom: 20px;

  .box-title {
    margin: 10px 10px 20px;
  }
}
</style>
