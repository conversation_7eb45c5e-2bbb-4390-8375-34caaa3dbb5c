<template>
  <el-dialog
    :title="exType == 0 ? '新增分案节点' : '修改分案节点'"
    v-model="open"
    width="1000px"
    :before-close="cancel"
    append-to-body
  >
    <el-form :model="form" class="mt10" label-width="140px" :rules="rules" ref="formRef">
      <el-form-item class="mar0" label="分案节点名称" prop="nodeName">
        <el-input
          style="width: 360px"
          v-model="form.nodeName"
          clearable
          :disabled="exType == 1"
          maxlength="20"
          show-word-limit
          placeholder="请输入分案节点名称"
        />
      </el-form-item>
      <el-form-item label="分案类型" prop="allocationType">
        <el-radio-group v-model="form.allocationType">
          <el-radio :label="1">按户数</el-radio>
          <el-radio :label="2">按金额</el-radio>
          <el-radio :label="3">按数量</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="设置机构">
      </el-form-item>
       <div class="opt-rules">
          <optRules ref="optteamRef" :key="keyStatus" :type="form.allocationType" />
        </div>
      <el-form-item label="是否携带催记" prop="bringDiary" @change="changeBringDiary">
        <el-radio-group v-model="form.bringDiary">
          <el-radio :label="0">是</el-radio>
          <el-radio :label="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否智能携带催记" v-if="form.bringDiary == 0" prop="aiDringDiary">
        <el-radio-group v-model="form.aiDringDiary">
          <el-radio :label="0">是，只携带本机构历史催记</el-radio>
          <el-radio :label="1">否，携带所有机构的催记</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import optRules from "@/components/CaseOptRules";
import { addNode, editNode } from "@/api/workExamine/parameter";
//全局配置
const { proxy } = getCurrentInstance();
const emit = defineEmits(["getList"]);
//接口参数
const typeInfo = ref([addNode, editNode]);
//表单属性
const open = ref(false);
const loading = ref(false);
const exType = ref(0);
const reqId = ref(undefined);
const keyStatus = ref(+(new Date()))
//表单参数
const data = reactive({
  form: {
    nodeName: undefined,
    allocationType: 1,
    decisionNodeTeams: [],
    bringDiary: undefined,
    aiDringDiary: undefined,
    status:0
  },
  rules: {
    nodeName: [{ required: true, message: "请输入分案节点名称！", trigger: "blur" }],
    allocationType: [{ required: true, message: "请选择分案类型！", trigger: "change" }],
    bringDiary: [{ required: true, message: "请选择是否携带催记！", trigger: "change" }],
    aiDringDiary: [
      { required: true, message: "请选择是否智能携带催记！", trigger: "change" },
    ],
  },
});
const { form, rules } = toRefs(data);

//开启弹窗
function opendialog(type, row) {
  proxy.resetForm("formRef");
  open.value = true;
  exType.value = type;
  if (type == 1) {
    reqId.value = row.id;
    form.value = row;
    console.log(row.decisionNodeTeams)
    nextTick(() =>{
      let req = JSON.parse(JSON.stringify(row.decisionNodeTeams))
      console.log(req)
      proxy.$refs["optteamRef"].checkedNode(req);
    })
  }
}

//提交
function submit() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      loading.value = true;
      let req = JSON.parse(JSON.stringify(form.value));
      req.nodeName = req.nodeName.replace(/\s/g, "");
      proxy.$refs["optteamRef"].exposeData().then((res) => {
        if (res && res.length > 0) {
          req.decisionNodeTeams = res
          typeInfo.value[exType.value](req)
            .then((res) => {
              loading.value = false;
              proxy.$modal.msgSuccess("操作成功");
              cancel();
              emit("getList");
            })
            .catch(() => {
              loading.value = false;
            });
        }else{
          loading.value = false;
        }
      });
    } else {
      loading.value = false;
    }
  });
}

//重置表单
function reset() {
  form.value = {
    nodeName: undefined,
    allocationType: 1,
    decisionNodeTeams: [],
    bringDiary: undefined,
    aiDringDiary: undefined,
    status:0
  };
  keyStatus.value = +(new Date())
  proxy.resetForm("formRef");
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//是否选中催记
function changeBringDiary(){
  form.value.aiDringDiary = undefined
}

defineExpose({
  opendialog,
});
</script>
<style lang="scss" scoped>
.opt-rules{
  padding:0px 20px
}
</style>
