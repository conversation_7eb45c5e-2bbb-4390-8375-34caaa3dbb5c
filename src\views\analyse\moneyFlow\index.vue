<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container tree-height">
          <div class="home-style mb10" @click="clickAll">
            <el-icon>
              <home-filled />
            </el-icon>
            <span class="ml5">机构</span>
          </div>
          <el-tree
            :data="treeList"
            :props="{ label: 'ownName', children: 'list' }"
            :expand-on-click-node="false"
            ref="treeRef"
            show-checkbox
            @check="nodeCheck"
          >
            <template #default="{ node }">
              <el-tooltip effect="light" :content="node.label" placement="right-start">
                <span class="el-tree-node__label">{{ node.label }}</span>
              </el-tooltip>
            </template>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-row class="query-box">
          <div class="mr20" style="display:inline-block">
            <el-radio-group v-model="queryParams.period" @change="changeData">
              <el-radio-button label="本周" />
              <el-radio-button label="本月" />
              <el-radio-button label="今年" />
            </el-radio-group>
          </div>
          <div style="display:inline-block">
            <el-date-picker
              v-model="queryParams.clientOverdueStart"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 300px"
              @change="changeData"
            ></el-date-picker>
          </div>
        </el-row>
        <el-row class="curve-box">
          <curve
            v-if="queryParams.checkNodeList.length > 0"
            :num="0"
            :dataList="queryParams.checkNodeList"
            :query="queryParams"
            ref="curveAllRef"
          />
          <monTable :dataList="queryParams.checkNodeList" :query="queryParams" ref="curveTableRef" />
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script setup name="MoneyFlow">
import { transferorQuery } from "@/api/analyse/business";
import curve from "./components/curve.vue";
import monTable from "./components/monTable.vue";
//全局属性
const { proxy } = getCurrentInstance();
const router = useRouter();
//树形结构
const treeList = ref([]);
//参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    period: "本月",
    clientOverdueStart: [],
    checkNodeList: []
  }
});
const total = ref(0);
const { queryParams } = toRefs(data);

//获取权限菜单
function getTree() {
  queryParams.value.checkNodeList = [];
  transferorQuery()
    .then(res => {
      res.data.forEach((item, index) => {
        let obj = {
          id: item.ownId,
          ownName: item.ownName,
          cheched: false,
          list: []
        };
        let banNum = JSON.parse(JSON.stringify(item.list));
        banNum.forEach(v => {
          let banList = {
            id: v,
            ownName: v,
            category: item.ownName
          };
          obj.list.push(banList);
        });
        treeList.value.push(obj);
      });
      treeList.value.forEach(item => {
        let obj = {
          id: item.id,
          ownName: item.ownName,
          cheched: false,
          list: []
        };
        queryParams.value.checkNodeList.push(obj);
      });
      refreshData();
    })
    .catch(() => {});
}
getTree();

function nodeCheck(data, node) {
  queryParams.value.checkNodeList.forEach(item => {
    item.list = [];
    item.cheched = false;
  });
  node.checkedNodes.forEach((item, index) => {
    queryParams.value.checkNodeList.forEach(v => {
      if (item.category == v.ownName) {
        v.list.push(item);
        v.cheched = true;
      }
    });
    queryParams.value.checkNodeList.forEach(v => {
      if (!v.cheched) {
        v.cheched = false;
      }
    });
  });
  refreshData();
}
function changeData() {
  if (queryParams.value.clientOverdueStart == null) {
    queryParams.value.clientOverdueStart = [];
  }
  refreshData();
}
function refreshData() {
  proxy.$refs["curveAllRef"].getList();
  proxy.$refs[`curveTableRef`].getList(queryParams.value.checkNodeList);
}
</script>
<style lang="scss" scoped></style>
