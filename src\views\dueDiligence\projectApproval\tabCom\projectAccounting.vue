<template>
  <div class="mt20">
    <el-form inline label-width="auto" :model="approveData">
      <el-form-item label="项目ID">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectId"
          :options="projectIdList"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 320px"
          v-model="queryParmas.projectName"
          :options="projectNameList"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item label="资产转让方">
        <MultiSelect
          v-model="approveData.transferorId"
          style="width: 320px"
          placeholder="资产转让方"
           :options="transferorList"
        />
      </el-form-item>
      <el-form-item label="产品类型">
        <MultiSelect
          v-model="approveData.productType"
          style="width: 320px"
          placeholder="产品类型"
          :options="ductTypeList"
        />
      </el-form-item>
      <el-form-item label="付款单状态">
        <MultiSelect
          v-model="approveData.paymentStatus"
          style="width: 320px"
          placeholder="付款单状态"
          :options="optionInfo.paymentStatus"
        />
      </el-form-item>
      <el-form-item label="付款单申请流水号">
        <el-input
          v-model="approveData.paymentSerialNo"
          style="width: 320px"
          placeholder="请输入付款单申请流水号"
        />
      </el-form-item>
      <el-form-item label="收款账号">
        <el-input
          v-model="approveData.receivingAccount"
          style="width: 320px"
          placeholder="请输入收款账号"
        />
      </el-form-item>
      <el-form-item label="付款类型">
        <MultiSelect
          v-model="approveData.paymentType"
          style="width: 320px"
          placeholder="付款类型"
          :options="optionInfo.paymentType"
        />
      </el-form-item>
      <el-form-item label="申请人">
        <MultiSelect 
          style="width: 320px"
           v-model="queryParmas.applicantIdList"
          placeholder="请选择申请人"
          :options="createByList"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="approveData.applyTime"
          type="datetime"
          style="width: 320px"
          placeholder="请选择申请时间"
        />
      </el-form-item>
      <el-form-item label="收款人">
        <el-input
          v-model="approveData.payee"
          style="width: 320px"
          placeholder="请输入收款人"
        />
      </el-form-item>
      <el-form-item label="账户名">
        <el-input
          v-model="approveData.accountName"
          style="width: 320px"
          placeholder="请输入账户名"
        />
      </el-form-item>
      <el-form-item label="开户行">
        <el-input
          v-model="approveData.bank"
          style="width: 320px"
          placeholder="请输入开户行"
        />
      </el-form-item>
      <!-- <el-form-item label="处理人">
        <el-input
          v-model="approveData.processor"
          style="width: 320px"
          placeholder="请输入处理人"
        />
      </el-form-item> -->
      <!-- <el-form-item label="处理时间">
        <el-date-picker
          v-model="approveData.processTime"
          type="datetime"
          style="width: 320px"
          placeholder="请选择处理时间"
        />
      </el-form-item> -->
    </el-form>
    <div class="text-center">
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </div>
    <el-row class="mt20">
      <el-button
        type="primary"
        :disabled="!selectedArr.length"
        @click="handle(null, 0)"
        >通过</el-button
      >
      <el-button
        type="warning"
        :disabled="!selectedArr.length"
        @click="handle(null, 1)"
        >不通过</el-button
      >
    </el-row>
    <SelectedAll
      ref="selectedAllRef"
      :dataList="dataList"
      :selectedArr="selectedArr"
      v-model:allQuery="allQuery"
    />
    <el-tabs v-model="activetab" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.code"
        :label="item.info"
        :name="item.code"
      />
    </el-tabs>
    <el-table
      ref="multipleTableRef"
      :data="dataList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="44px"
        :selectable="selectable"
        align="right"
      />
      <el-table-column
        label="项目ID"
        prop="projectId"
        align="center"
        width="120"
      />
      <el-table-column
        label="项目名称"
        prop="projectName"
        align="center"
        width="120"
      />
      <el-table-column
        label="资产转让方"
        prop="transferor"
        align="center"
        width="120"
      />
      <el-table-column
        label="产品类型"
        prop="productName"
        align="center"
        width="120"
      />
      <el-table-column
        label="建账状态"
        prop="accountingStatus"
        align="center"
        width="120"
      />
      <el-table-column
        label="付款单申请流水号"
        prop="paymentSerialNumber"
        align="center"
        width="150"
      />
      <el-table-column
        label="付款单状态"
        prop="paymentStatus"
        align="center"
        width="120"
      />
      <el-table-column
        label="付款类型"
        prop="paymentType"
        align="center"
        width="120"
      />
      <el-table-column
        label="收款账号"
        prop="accountNumber"
        align="center"
        width="120"
      />
      <el-table-column
        label="账户名"
        prop="accountName"
        align="center"
        width="120"
      />
      <el-table-column label="收款人" prop="receiver" align="center" width="120" />
      <el-table-column label="开户行" prop="bankName" align="center" width="120" />
      <el-table-column
        label="合同金额"
        prop="contractAmount"
        align="center"
        width="120"
      />
      <el-table-column
        label="申请金额"
        prop="applyAmount"
        align="center"
        width="120"
      />
      <el-table-column label="备注" prop="remark" align="center" width="120" />
      <el-table-column
        label="申请人"
        prop="applicant"
        align="center"
        width="120"
      />
      <el-table-column
        label="申请时间"
        prop="applyDate"
        align="center"
        width="160"
      />
      <el-table-column
        label="处理状态"
        prop="approveStart"
        align="center"
        width="120"
      >
        <template #default="{ row }">
          {{ approveStartMap[row.approveStart] || row.approveStart }}
        </template>
      </el-table-column>
      <el-table-column
        label="处理人"
        prop="examineBy"
        align="center"
        width="120"
      />
      <el-table-column
        label="处理时间"
        prop="approveTime"
        align="center"
        width="160"
      />
      <el-table-column width="180" fixed="right" label="操作" align="center">
        <template #default="{ row }">
          <el-button v-if="!row.approveStart || row.approveStart === '--'" type="text" @click="handle(row, 0)">通过</el-button>
          <el-button v-if="!row.approveStart || row.approveStart === '--'" type="text" @click="handle(row, 1)">不通过</el-button>
          <el-button type="text" @click="handleDetails(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParmas.pageNum"
      v-model:limit="queryParmas.pageSize"
      @pagination="getList"
    />

    <Pass ref="passRef" @submit="handleSubmit" />
  </div>
</template>

<script setup>
import Pass from "../dialog/pass.vue";
import ProjectApproval from "../index.js";
import { getProductType,getProjectId, getProjectName,getUserInfo } from "@/api/dueDiligence/startNapeList";
import { assetOwnerTree } from "@/api/assets/assetside";
import { formatTime } from "@/utils/common";
import { fillEmptyToDash } from "@/api/conference/utils";
import { getPayMethodOption } from "@/api/dueDiligence/common";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

// 创建ProjectApproval实例
const projectApproval = new ProjectApproval();

// 响应式数据，用于模板绑定
const activetab = ref(projectApproval.activetab);
const total = ref(projectApproval.total);
const queryParmas = ref({
  approveCode: "accounting",
  ...projectApproval.queryParmas,
});
const approveData = ref(projectApproval.approveData);
const dataList = ref(projectApproval.dataList);
const tabList = ref(projectApproval.tabList);
const selectedArr = ref([]);
const allQuery = ref(false);
const passRef = ref(null);
const projectIdList = ref([]);
const projectNameList = ref([]);
const createByList = ref([]);//申请人
const ductTypeList = ref([]); // 产品类型
const transferorList = ref([]); // 资产转让方

const loading = ref(false);

// 处理状态映射
const approveStartMap = {
  2: '通过',
  3: '不通过',
  4: '撤销',
  5: '作废',
  6: '已退案关闭',
};

// 表格主要字段，用于空值替换
const tableProps = [
  'projectId',
  'projectName',
  'transferor',
  'productName',
  'accountingStatus',
  'paymentSerialNumber',
  'paymentStatus',
  'paymentType',
  'accountNumber',
  'accountName',
  'receiver',
  'bankName',
  'contractAmount',
  'applyAmount',
  'remark',
  'applicant',
  'applyDate',
  'approveStart',
];

// 设置审批代码
projectApproval.queryParmas.approveCode = "accounting";

// 模拟数据，可以删除或保留
const mockDataList = ref([]);

// 使用类的方法
const formatApproveStart = (value) => {
  return projectApproval.formatApproveStart(value);
};

const handleQuery = () => {
  projectApproval.queryParmas.pageNum = 1;
  getList();
};

const handle = (row = null, type) => {
  let req = {
    approveCode: "accounting",
    allQuery: false,
  };
  if (row !== null) {
    req.approveIds = [row.approveId];
    passRef.value.opendialog(req, type);
  } else {
    if (allQuery.value) {
      req.allQuery = true;
      req.approvePageRequest = JSON.parse(JSON.stringify(approveData.value));
    } else {
      req.approveIds = selectedArr.value.map((item) => item.approveId);
    }
    passRef.value.opendialog(req, type);
  }
};

const handleSubmit = (req) => {
  if (req.type == 0) {
    projectApproval.handleApprove(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        getList();
        passRef.value.close();
      }
    });
  } else {
    projectApproval.handleReject(req).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        getList();
        passRef.value.close();
      }
    });
  }
};

function handleDetails(row) {
  const query = {
    path: route.path,
    pageType: "projectAccounting",
    progressStatus: 3,
    isAppStatus: true,
    isDetails: 1,
    rowData: JSON.stringify(row),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

const selectable = (row) => {
  return projectApproval.selectable(row);
};

const resetQuery = () => {
  loading.value = true;
  projectApproval.resetQuery(({ dataList: newDataList, total: newTotal }) => {
    dataList.value = newDataList;
    total.value = newTotal;
    // 重置响应式数据
    queryParmas.value = projectApproval.queryParmas;
    approveData.value = projectApproval.approveData;
    loading.value = false;
  });
};

const getList = () => {
  projectApproval.queryParmas.approveCode = "accounting";
  loading.value = true;
  projectApproval
    .getList(({ dataList: newDataList, total: newTotal }) => {
      dataList.value = fillEmptyToDash(newDataList, tableProps);
      total.value = newTotal;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 标签页切换处理
const handleTabChange = (tabName) => {
  loading.value = true;
  projectApproval.changeTab(
    tabName,
    ({ dataList: newDataList, total: newTotal }) => {
      dataList.value = newDataList;
      total.value = newTotal;
      activetab.value = tabName;
      loading.value = false;
    }
  );
};

const handleSelectionChange = (val) => {
  selectedArr.value = val;
};

// 同步响应式数据到类实例
const syncDataToInstance = () => {
  projectApproval.updateApproveData(approveData.value);
  projectApproval.updateQueryParams(queryParmas.value);
};

// 监听数据变化，同步到类实例
watch(
  approveData,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

watch(
  queryParmas,
  () => {
    syncDataToInstance();
  },
  { deep: true }
);

function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      projectIdList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      projectNameList.value = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

// 获取申请人下拉数据
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("接口返回数据格式错误:", res.data);
        return;
      }
      console.log("获取用户信息成功:", res.data);
      const userOptions = res.data.map((item) => ({
        value: item.code,
        label: item.info,
      }));
      createByList.value = userOptions;
    })
    .catch((error) => {
      console.error("获取用户信息失败:", error);
    });
}

function getProductTypeFun() {
  getProductType().then((res) => {
    ductTypeList.value = res.data.map((item) => ({
      value: item.code,
      label: item.info,
    }));
  });
}

function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      transferorList.value = res.data.map((item) => ({
        value: item.id.split(":")[1],
        label: item.label,
      }));
    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}

const optionInfo = reactive({
  paymentStatus: [],
  paymentType: [],
})

// 获取选项
function getOptionList() {
  const projectAccounting = [
      { rule: "voucher_status", key: "paymentStatus" },
      { rule: "payment_type", key: "paymentType" },
    ]
    projectAccounting.forEach(({ rule, key }) => {
      getPayMethodOption({ ruleName: rule }).then((res) => {
        optionInfo[key] = (res.data || []).map((item) => ({
          value: item.dictLabel,
          label: item.dictLabel,
        }));
      });
    });
  
}

onMounted(() => {
  // 初始化时使用模拟数据，实际使用时可以调用getList()
  dataList.value = mockDataList.value;
  total.value = mockDataList.value.length;
  getProjectIdFun();
  getProjectNameFun();
  getUserInfoFun()
  getProductTypeFun()
  getTreeselectFun()
  getOptionList()
  // 如果需要真实数据，取消注释下面的代码
  getList();
});
</script>

<style lang="scss" scoped>
.range-scope {
  display: flex;

  span {
    margin: 0 10px;
  }
}
</style>
