<template>
  <el-select
    ref="selectRef"
    :model-value="modelValue"
    multiple
    filterable
    :placeholder="computedPlaceholder"
    style="width: 100%"
    clearable
    collapse-tags
    collapse-tags-tooltip
    @update:modelValue="handleChange"
    @visible-change="handleVisibleChange"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </el-select>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref, defineExpose } from "vue";

const props = defineProps({
  // v-model 绑定的值
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 选项数据
  options: {
    type: Array,
    default: () => [],
    required: true,
  },
  // 占位符文本（可选，用于拼接）
  placeholder: {
    type: String,
    default: "",
  },
});

const emit = defineEmits([
  "update:modelValue",
  "change",
  "focus",
  "blur",
  "visible-change",
]);

const selectRef = ref();

// 计算完整的提示语
const computedPlaceholder = computed(() => {
  const baseText = "请输入或选择";
  return props.placeholder ? `${baseText}${props.placeholder}` : baseText;
});

// 处理值变化
const handleChange = (value) => {
  emit("update:modelValue", value);
  emit("change", value);
};

// 处理下拉框显示/隐藏
const handleVisibleChange = (visible) => {
  emit("visible-change", visible);
};

// 处理获得焦点
const handleFocus = (event) => {
  emit("focus", event);
};

// 处理失去焦点
const handleBlur = (event) => {
  emit("blur", event);
};

// 预留方法：获取当前选中的选项详情
const getSelectedOptions = () => {
  return props.options.filter((option) =>
    props.modelValue.includes(option.value)
  );
};

// 预留方法：获取未选中的选项
const getUnselectedOptions = () => {
  return props.options.filter(
    (option) => !props.modelValue.includes(option.value)
  );
};

// 预留方法：清空选择
const clearSelection = () => {
  emit("update:modelValue", []);
  emit("change", []);
};

// 预留方法：全选
const selectAll = () => {
  const allValues = props.options
    .filter((option) => !option.disabled)
    .map((option) => option.value);
  emit("update:modelValue", allValues);
  emit("change", allValues);
};

// 预留方法：获取下拉框实例
const getSelectInstance = () => {
  return selectRef.value;
};

// 预留方法：手动触发下拉框显示/隐藏
const toggleDropdown = () => {
  if (selectRef.value) {
    selectRef.value.toggleMenu();
  }
};

// 预留方法：手动聚焦
const focus = () => {
  if (selectRef.value) {
    selectRef.value.focus();
  }
};

// 预留方法：手动失焦
const blur = () => {
  if (selectRef.value) {
    selectRef.value.blur();
  }
};

// 暴露方法给父组件使用
defineExpose({
  // 选择操作方法
  getSelectedOptions,
  getUnselectedOptions,
  clearSelection,
  selectAll,

  // 组件控制方法
  getSelectInstance,
  toggleDropdown,
  focus,
  blur,
});
</script>

<style lang="scss" scoped>
</style>

<!--
使用示例：

基础用法：
<MultiSelect
  v-model="selectedValues"
  :options="optionList"
  placeholder="项目名称"
/>

监听事件：
<MultiSelect
  v-model="selectedValues"
  :options="optionList"
  @change="handleChange"
  @focus="handleFocus"
  @blur="handleBlur"
/>

使用组件方法：
<MultiSelect
  ref="multiSelectRef"
  v-model="selectedValues"
  :options="optionList"
/>

// 在父组件中调用方法
const multiSelectRef = ref()



// 获取选中的选项详情
const selectedOptions = multiSelectRef.value.getSelectedOptions()

// 全选
multiSelectRef.value.selectAll()

// 清空选择
multiSelectRef.value.clearSelection()

// 手动聚焦
multiSelectRef.value.focus()

选项数据格式：
const options = [
  { value: '1', label: '选项1', disabled: false },
  { value: '2', label: '选项2', disabled: true },
  { value: '3', label: '选项3' }
]

事件说明：
- @change: 选择值变化时触发
- @focus: 获得焦点时触发
- @blur: 失去焦点时触发
- @visible-change: 下拉框显示/隐藏时触发
-->
