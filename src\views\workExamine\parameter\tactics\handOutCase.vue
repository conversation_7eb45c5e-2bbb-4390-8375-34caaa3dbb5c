<template>
  <el-table v-loading="loading" :data="handoutList">
    <el-table-column
      label="序号"
      type="index"
      align="center"
      width="50px"
      :index="tableIndex"
    ></el-table-column>
    <el-table-column
      label="策略名称"
      prop="strategyName"
      align="center"
    ></el-table-column>
    <el-table-column label="更新时间" prop="updateTime" align="center"></el-table-column>
    <el-table-column label="状态" prop="state" align="center">
     <template #default="{row}">
        <el-switch
          class="myswitch"
          v-model="row.state"
          active-color="#2ECC71"
          active-value="0"
          inactive-value="1"
          active-text="开"
          inactive-text="关"
          @change="handleState(row)"
          v-if="checkPermi(['tactics:handOutCase:status'])"
        ></el-switch>
        <span v-else>{{row.state == 0?'开启':'关闭'}}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作">
     <template #default="{row}">
        <el-button type="primary" link v-hasPermi="['tactics:handOutCase:edit']" @click="editTactic(row.id)">修改</el-button>
        <el-button type="primary" link v-hasPermi="['tactics:handOutCase:delete']" @click="removeTactic(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <!-- 添加或修改策略 -->
  <el-dialog
    :title="title"
    v-model="open"
    width="1000px"
    append-to-body
    :before-close="cancel"
  >
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略名称" prop="strategyName">
            <el-input v-model="form.strategyName" placeholder="请输入策略名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state">
            <el-switch
              class="myswitch"
              v-model="form.state"
              active-color="#2ECC71"
              active-value="0"
              inactive-value="1"
              active-text="开"
              inactive-text="关"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="wcc-flex">
            <div>设置数据规则：</div>
            <el-button type="primary" plain icon="Plus" @click="addRules">增加</el-button>
          </div>
        </el-col>
        <el-col :span="24" style="padding: 10px">
          <div v-show="isPreview">{{ formula }}</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="form.dataRulesList" border style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              align="center"
              width="60px"
            ></el-table-column>
            <el-table-column label="链接关系" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.linkage`">
                  <el-select style="width: 240px"
                    v-model="row.linkage"
                    clearable
                    :disabled="$index === 0"
                    placeholder="请选择"
                  >
                    <el-option v-if="$index === 0" label="空置" value="空置" />
                    <el-option label="并且" value="并且" />
                    <el-option label="或者" value="或者" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="左括号" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.leftBrackets`">
                  <el-select v-model="row.leftBrackets" style="width: 240px" clearable placeholder="请选择">
                    <el-option label="空置" value="空置" />
                    <el-option label="(" value="(" />
                    <el-option label="((" value="((" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="字段" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.field`">
                  <el-select v-model="row.field" style="width: 240px" clearable placeholder="请选择">
                    <el-option
                      v-for="item in fileds"
                      @click="operatorChange(item, $index)"
                      :key="item.field"
                      :label="item.info"
                      :value="item.info"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作符" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.operator`">
                  <el-select v-model="row.operator" style="width: 240px" clearable placeholder="请选择">
                    <el-option
                      v-show="!row.operatorType || row.operatorType == 0"
                      label="大于"
                      value="大于"
                    />
                    <el-option
                      v-show="!row.operatorType || row.operatorType == 0"
                      label="大于等于"
                      value="大于等于"
                    />
                    <el-option
                      v-show="!row.operatorType || row.operatorType == 0"
                      label="小于等于"
                      value="小于等于"
                    />
                    <el-option
                      v-show="!row.operatorType || row.operatorType == 0"
                      label="小于"
                      value="小于"
                    />
                    <el-option label="等于" value="等于" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="期望值" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.expectedValue`">
                  <el-input
                    v-if="row.operatorType != 2"
                    v-model="row.expectedValue"
                    placeholder="请输入期望值"
                  ></el-input>
                  <el-select
                    v-else style="width: 240px"
                    v-model="row.expectedValue"
                    clearable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in provinces"
                      :key="item.proID"
                      :label="item.name"
                      :value="item.name"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="右括号" align="center">
              <template #default="{ row, $index }">
                <el-form-item :prop="`dataRulesList.${$index}.rightBrackets`">
                  <el-select style="width: 240px" v-model="row.rightBrackets" clearable placeholder="请选择">
                    <el-option label="空置" value="空置" />
                    <el-option label=")" value=")" />
                    <el-option label="))" value="))" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" width="80">
              <template v-slot:header>
                <span @click="isPreview = !isPreview" class="my-btn">{{
                  isPreview ? "关闭预览" : "打开预览"
                }}</span>
              </template>
             <template #default="{row,$index}">
                <el-button type="primary" link @click="removeRuels($index, row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  strategyList,
  addStrategy,
  detailStrateg,
  editStrategy,
  deleteStrategy,
  getProvinces,
  getFields,
  getOperators,
} from "@/api/system/tactic";
import { checkPermi } from "@/utils/permission";
const props = defineProps({
  queryParams: {
    type: Object,
  },
});
const { proxy } = getCurrentInstance();
const total = inject("total");
const $getList = inject("getList");
const loading = ref(false);
const subloading = ref(false);
const handoutList = ref([]);
const title = ref("");
const open = ref(false);

const isPreview = ref(false); // 是否预览
const formula = computed(() => {
  //公式
  let res = "";
  form.value.dataRulesList.map((item, index) => {
    if (index > 0) {
      res += " ";
    }
    res +=
      item.linkage +
      " " +
      item.leftBrackets +
      " " +
      item.field +
      " " +
      item.operator +
      " " +
      item.expectedValue +
      " " +
      item.rightBrackets;
  });
  res = res.replace(/空置/g, "");
  return res;
});

const provinces = ref([]); //省份地区
const fileds = ref([]); //字段
const operators = ref([]); //操作符

const data = reactive({
  form: {
    strategyName: undefined,
    state: "0",
    dataRulesList: [],
  },
  rules: {
    strategyName: [{ required: true, message: "请填写策略名称", trigger: "blur" }],
  },
});
const deletedRules = ref([]);
const { form, rules } = toRefs(data);

//获取分案规则列表
function getList(query) {
  loading.value = true;
  strategyList(query)
    .then((res) => {
      total.value = res.total;
      handoutList.value = res.rows;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

//添加规则
function addRules() {
  let obj = {
    linkage: "",
    leftBrackets: "",
    field: "",
    operator: "",
    expectedValue: "",
    rightBrackets: "",
  };
  if (form.value.dataRulesList.length === 0) {
    obj.linkage = "空置";
  }
  form.value.dataRulesList.push(obj);
}

//打开修改策略弹框
function editTactic(id) {
  detailStrateg(id).then((res) => {
    form.value = res.data;
    opendialog("编辑策略");
  });
}

//删除策略
function removeTactic(row) {
  proxy.$modal
    .confirm('是否确认删除策略名称为"' + row.strategyName + '"的数据项？')
    .then(function () {
      loading.value = true;
      return deleteStrategy(row.id);
    })
    .then(() => {
      $getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {loading.value = false;})
}

//删除规则
function removeRuels(index, row) {
  if (row.hasOwnProperty("id")) {
    deletedRules.value.push(row.id);
  }
  form.value.dataRulesList.splice(index, 1);
}

//重置表单
function reset() {
  proxy.resetForm("formRef");
  form.value = {
    strategyName: undefined,
    state: "0",
    dataRulesList: [],
  };
}

//列表序号递增
function tableIndex(index) {
  return (props.queryParams.pageNum - 1) * props.queryParams.pageSize + index + 1;
}

//打开新建策略弹框
async function opendialog(tit) {
  title.value = tit;
  await getFields().then((res) => {
    fileds.value = res.data;
  });
  form.value.dataRulesList.forEach((item,index) =>{
    fileds.value.forEach((v) => {
      if(item.field == v.info){
        item.operatorType = v.type
        if (v.type == 2) {
          provinces.value = v.arr;
        }
      }
    });
  })
  open.value = true;
}

//限制字段能够选取的操作符
function operatorChange(item, index) {
  form.value.dataRulesList[index].operatorType = item.type;
  console.log(item.type)
  if (item.type == 2) {
    provinces.value = item.arr;
  }
}

//启用禁用
async function handleState(row) {
  let data = {};
  await detailStrateg(row.id).then((res) => {
    data = res.data;
  });
  data.state = data.state == "0" ? "1" : "0";
  data.deletedRules = [];
  editStrategy(data)
    .then(() => {
      // proxy.$modal.msgSuccess("修改成功！");
      // $getList();
    })
    .catch(() => {
      $getList();
    });
}

//取消添加/编辑
function cancel() {
  open.value = false;
  reset();
}

//提交表单
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let istrue = rulesListCheck();
      if (istrue) {
        subloading.value = true;
        if (form.value.hasOwnProperty("id")) {
          //编辑
          let data = JSON.parse(JSON.stringify(form.value));
          data.deletedRules = deletedRules.value;
          editStrategy(data)
            .then((res) => {
              subloading.value = false;
              proxy.$modal.msgSuccess("编辑成功！");
              cancel();
              $getList();
            })
            .catch(() => {
              subloading.value = false;
            });
        } else {
          //添加
          addStrategy(form.value)
            .then((res) => {
              subloading.value = false;
              proxy.$modal.msgSuccess("添加成功！");
              cancel();
              $getList();
            })
            .catch(() => {
              subloading.value = false;
            });
        }
      }
    }
  });
}

//策略规则校验
function rulesListCheck() {
  let arr = form.value.dataRulesList;
  if (arr.length === 0) {
    proxy.$modal.msgError("请设置数据规则!");
    return false;
  }

  let str = formula.value;
  let left = str.split("(").length - 1;
  let right = str.split(")").length - 1;
  if (left !== right) {
    proxy.$modal.msgError("设置的规则公式有误，请设置正确后提交!");
    return false;
  }

  let resarr = [];
  for (let i = 0; i < arr.length; i++) {
    let obj = arr[i];
    for (const key in obj) {
      if (i !== 0 && key != "linkage" && obj[key] === "") {
        resarr.push(i + 1);
        break;
      }
    }
  }
  if (resarr.length > 0) {
    proxy.$modal.msgWarning(`请将第${String(resarr)}行表格数据填写完整后再提交！`);
    return false;
  } else {
    return true;
  }
}

defineExpose({
  getList,
  opendialog,
});
</script>

<style scoped>
.wcc-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.my-btn {
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
