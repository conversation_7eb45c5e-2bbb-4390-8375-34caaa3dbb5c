<template>
  <div class="app-container">
    <!-- 筛选表达 -->
    <el-form
      inline
      label-width="100px"
      :class="{ 'form-h50': !showSearch }"
      ref="queryRef"
      class="search-form"
    >
      <el-form-item prop="projectId" label="项目ID">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.projectId"
          :options="searchOptions.projectId"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.projectName"
          :options="searchOptions.projectName"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item prop="productId" label="产品类型">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.productId"
          :options="searchOptions.productId"
          placeholder="产品类型"
        />
      </el-form-item>
      <el-form-item prop="transferor" label="资产转让方">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.transferor"
          :options="searchOptions.transferor"
          placeholder="资产转让方"
        />
      </el-form-item>
      <el-form-item prop="biddingMethod" label="投标方式">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.biddingMethod"
          :options="biddingMethodList"
          placeholder="投标方式"
        />
      </el-form-item>
      <el-form-item prop="totalDebt" label="债权总金额">
        <div class="range-area" style="width: 230px">
          <el-input v-model="queryParams.totalDebt1" />
          <span>—</span>
          <el-input v-model.number="queryParams.totalDebt2" />
        </div>
      </el-form-item>
      <el-form-item prop="principalAmount" label="债权本金">
        <div class="range-area" style="width: 230px">
          <el-input v-model="queryParams.principal1" />
          <span>—</span>
          <el-input v-model.number="queryParams.principal2" />
        </div>
      </el-form-item>
      <el-form-item prop="createById" label="立项人">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.createById"
          :options="searchOptions.createById"
          placeholder="立项人"
        />
      </el-form-item>
      <el-form-item prop="createTime" label="立项时间">
        <el-date-picker
          style="width: 230px"
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item prop="baseDate" label="基准日">
        <el-date-picker
          v-model="queryParams.baseDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 230px"
        />
      </el-form-item>
      <el-form-item prop="planBiddingDate" label="预计竞价日期">
        <el-date-picker
          v-model="queryParams.planBiddingDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 230px"
        />
      </el-form-item>
    </el-form>
    <!-- 过滤按钮 -->
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <!-- 操作按钮 -->
    <div class="operation-revealing-area mb20">
      <el-button type="primary" @click="handleOpenDailog('submit')"
        >提交立项</el-button
      >
      <el-button type="primary" @click="handleAdd()">新增立项</el-button>
      <el-button type="primary" @click="handleOpenDailog('delete')"
        >删除</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <!-- 全选功能 -->
    <SelectedAll
      :dataList="dataList"
      v-model:selectedArr="selectedArr"
      v-model:allQuery="queryParams.allQuery"
    >
      <template #content>
        <div class="ml20">
          <span
            >项目量（件）：<i class="danger">{{ total }}</i></span
          >
        </div>
      </template>
    </SelectedAll>
    <!-- 切换标签 -->
    <el-tabs v-model="tabActive" @tab-change="tabChange()">
      <el-tab-pane
        v-for="v in tabList"
        :key="v"
        :label="`${v.label}(${v.count || 0})`"
        :name="v.value"
      />
    </el-tabs>
    <div class="table-box">
      <!-- 表单 -->
      <el-table
        v-loading="loading"
        ref="multipleTableRef"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :selectable="selectable"
          width="30px"
          align="right"
        />
        <el-table-column
          label="项目ID"
          v-if="columns[0].visible"
          width="160"
          align="center"
          prop="projectId"
        />
        <el-table-column
          label="项目名称"
          v-if="columns[1].visible"
          width="200"
          align="center"
          prop="projectName"
        />
        <el-table-column
          label="产品类型"
          v-if="columns[2].visible"
          width="120"
          align="center"
          prop="productName"
        />
        <el-table-column
          label="立项状态"
          v-if="columns[3].visible"
          width="120"
          align="center"
          prop="projectStatus"
        />
        <el-table-column
          label="资产转让方"
          v-if="columns[4].visible"
          width="120"
          align="center"
          prop="transferor"
        />
        <el-table-column
          label="债权总金额"
          v-if="columns[5].visible"
          width="120"
          align="center"
          prop="totalDebt"
        />
        <el-table-column
          label="债权本金"
          v-if="columns[6].visible"
          width="120"
          align="center"
          prop="principal"
        />
        <el-table-column
          label="户数"
          v-if="columns[7].visible"
          width="120"
          align="center"
          prop="householdCount"
        />
        <el-table-column
          label="基准日"
          v-if="columns[8].visible"
          width="120"
          align="center"
          prop="baseDate"
        />
        <el-table-column
          label="预计竞价日期"
          v-if="columns[9].visible"
          width="120"
          align="center"
          prop="planBiddingDate"
        />
        <el-table-column
          label="投标方式"
          v-if="columns[10].visible"
          width="120"
          align="center"
          prop="biddingMethod"
        />
        <el-table-column
          label="报价上限"
          v-if="columns[11].visible"
          width="120"
          align="center"
          prop="priceLimit"
        />
        <el-table-column
          label="资产评估表"
          v-if="columns[12].visible"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 1)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="立项报告"
          v-if="columns[13].visible"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 0)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="其他附件"
          v-if="columns[14].visible"
          width="120"
          align="center"
          prop="surface"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 2)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="立项人"
          v-if="columns[15].visible"
          width="120"
          align="center"
          prop="createBy"
        />
        <el-table-column
          label="立项时间"
          v-if="columns[16].visible"
          width="160"
          align="center"
          prop="createTime"
        />
        <el-table-column fixed="right" width="220" label="操作">
          <template #default="{ row }">
            <div>
              <el-button
                v-if="row.projectStatus == '新增立项'"
                type="text"
                @click="handleOpenDailog('submit', row)"
                >提交</el-button
              >
              <el-button type="text" @click="handleDetails(row)"
                >详情</el-button
              >
              <el-button
                v-if="row.projectStatus == '新增立项'"
                type="text"
                @click="handleOpenDailog('delete', row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- <perviewFile ref="perviewFileRef" /> -->
    <CheckFileDialog ref="checkFileDialogRef" />
  </div>
</template>

<script setup name="StartNapeList">
import perviewFile from "../startNapeList copy/dialog/perviewFile";
import {
  getNapeListByIdAndType,
  deleteNapeList,
  submitNapeList,
  getProjectId,
  getProjectName,
  getUserInfo,
  getTenderType,
  getNapeList,
  getProjectStateCount,
} from "@/api/dueDiligence/startNapeList";
import { assetOwnerTree } from "@/api/assets/assetside";
import { ArraysToStrings } from "@/utils/index";
import { fillEmptyToDash } from "@/api/conference/utils";
import CheckFileDialog from "../page/checkFile.vue";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 搜索参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
  projectId: [], // 项目ID
  projectName: [], // 项目名称
  productId: [], // 产品类型，二维路径数组
  transferor: [], // 资产转让方
  biddingMethod: [], // 投标方式
  totalDebt1: undefined, // 债权总金额最小值
  totalDebt2: undefined, // 债权总金额最大值
  principal1: undefined, // 债权本金最小值
  principal2: undefined, // 债权本金最大值
  createById: [], // 立项人
  createTime: [], // 立项时间
  baseDate: [], // 基准日
  planBiddingDate: [], // 预计竞价日期
  projectStatus: "",
});

// 选项数据
const searchOptions = reactive({
  projectId: [],
  projectName: [],
  productId: [],
  transferor: [],
  biddingMethod: [],
  createById: [],
});

const biddingMethodList = ref([
  { value: "邀请招标", label: "邀请招标" },
  { value: "线下公开", label: "线下公开" },
  { value: "线上公开", label: "线上公开" },
  { value: "公开拍卖", label: "公开拍卖" },
]);

const total = ref(1);

// 表单数据
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const selectedArr = ref([]);
// 表单列数据
const columns = ref([
  { key: 0, label: "项目ID", visible: true },
  { key: 1, label: "项目名称", visible: true },
  { key: 2, label: "产品类型", visible: true },
  { key: 3, label: "立项状态", visible: true },
  { key: 4, label: "资产转让方", visible: true },
  { key: 5, label: "债权总金额", visible: true },
  { key: 6, label: "债权本金", visible: true },
  { key: 7, label: "户数", visible: true },
  { key: 8, label: "基准日", visible: true },
  { key: 9, label: "预计竞价日期", visible: true },
  { key: 10, label: "投标方式", visible: true },
  { key: 11, label: "报价上限", visible: true },
  { key: 12, label: "资产评估表", visible: true },
  { key: 13, label: "立项报告", visible: true },
  { key: 14, label: "其他附件", visible: true },
  { key: 15, label: "立项人", visible: true },
  { key: 16, label: "立项时间", visible: true },
]);

// 表格主要字段，用于空值替换
const tableProps = [
  'projectId',
  'projectName',
  'productName',
  'projectStatus',
  'transferor',
  'totalDebt',
  'principal',
  'householdCount',
  'baseDate',
  'planBiddingDate',
  'biddingMethod',
  'priceLimit',
  'createBy',
  'createTime',
];


// 处理查询参数
function handlingQueries(queryParams) {
  let params = ArraysToStrings(queryParams);

  const getRange = (arr, idx) =>
    Array.isArray(arr) ? arr[idx] ?? undefined : undefined;
    if (params.productId) {
    params.productId = params.productId
      .split(',')
      .filter(item => item.startsWith('proud:'))
      .map(item => item.split(':')[1])
      .join(',');
  }

    params.transferorId  = params.transferor,
    delete params.transferor
  params = {
    ...params,
    createTime1: getRange(queryParams.createTime, 0),
    createTime2: getRange(queryParams.createTime, 1),
    baseDate1: getRange(queryParams.baseDate, 0),
    baseDate2: getRange(queryParams.baseDate, 1),
    planBiddingDate1: getRange(queryParams.planBiddingDate, 0),
    planBiddingDate2: getRange(queryParams.planBiddingDate, 1),
  };

  delete params.baseDate;
  delete params.createTime;
  delete params.planBiddingDate;

  return params;
}

// 获取列表数据
function getList() {
  loading.value = true;
  let params = handlingQueries(queryParams);

  getNapeList(params)
    .then((res) => {
      loading.value = false;
      total.value = res.total;
      dataList.value = fillEmptyToDash(res.rows, tableProps);
    })
    .catch((error) => {
      console.error("获取列表数据失败:", error);
    });
  delete params.projectStatus;
  getProjectStateCount(params).then((res) => {
    if (res.code == 200 && res.data.length == 2) {
      tabList.value[0].count = res.data.find(
        (item) => item.projectState == "新增立项"
      ).number;
      tabList.value[1].count = res.data.find(
        (item) => item.projectState == "已提交"
      ).number;
      tabList.value[2].count = tabList.value[0].count + tabList.value[1].count;
    } else {
      switch (res.data[0].projectState) {
        case "新增立项":
          tabList.value[0].count = res.data[0].number;
          tabList.value[1].count = 0;
          tabList.value[2].count = res.data[0].number;
          break;
        case "已提交":
          tabList.value[0].count = 0;
          tabList.value[1].count = res.data[0].number;
          tabList.value[2].count = res.data[0].number;
          break;
        default:
          break;
      }
    }
  });
}

// 重置查询条件
function resetQuery() {
  // 重置所有查询参数
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    allQuery: false,
    projectId: [],
    projectName: [],
    productId: [],
    transferor: [],
    biddingMethod: [],
    totalDebt1: null,
    totalDebt2: null,
    principal1: null,
    principal2: null,
    createById: [],
    createTime: "",
    baseDate: "",
    planBiddingDate: "",
  });

  // 重新获取数据
  getList();
}

const selectedRows = ref([]);

// 表格选择事件
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 搜索查询
function handleQuery() {
  // 重置页码到第一页
  queryParams.pageNum = 1;
  getList();
}

//
function handleOpenDailog(type, row = {}) {
  switch (type) {
    case "submit":
      if (!row && selectedRows.value.length == 0) {
        proxy.$modal.msgWarning("请选择要提交的项目");
        return;
      }
      proxy.$modal
        .confirm("该操作将会提交所选项目立项，确认进行操作吗？", "提交立项")
        .then(function () {
          handlesubmitNape(row);
        });
      break;

    case "delete":
      proxy.$modal
        .confirm("此操作将删除选中的项目，是否继续？", "删除提示")
        .then(function () {
          handleDeleteNape(row);
        });
      break;
    default:
      console.warn(`未知的弹窗类型: ${type}`);
  }
}

// 提交立项
function handlesubmitNape(row = null) {
  let params = handlingQueries(queryParams);
  params.ids = row ? [row.id] : selectedRows.value.map((item) => item.id);
  submitNapeList(params).then((res) => {
    proxy.$modal.msgSuccess("提交成功");
    getList();
  });
}
// 删除立项
function handleDeleteNape(row = null) {
  let params = handlingQueries(queryParams);
  params.ids = row ? [row.id] : selectedRows.value.map((item) => item.id);
  deleteNapeList(params).then((res) => {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  });
}

// function selectable() {
//   return !queryParams.allQuery;
// }

function handleDetails(row) {
  const query = {
    path: route.path,
    pageType: "startNapeList",
    progressStatus: 0,
    isDetails: 1,
    rowData: JSON.stringify(row),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}
function handleAdd(row) {
  const query = {
    path: route.path,
    pageType: "startNapeList",
    progressStatus: 0,
    isDetails: 0,
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}
// 查看
function handleCheck(row, fileType) {
  const query = {
    id: row.id,
    fileType: fileType,
  };
  getNapeListByIdAndType(query).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      proxy.$refs["checkFileDialogRef"].openDialog(res.data);
    } else {
      proxy.$modal.msgWarning("未上传文件");
    }
  });
}

// 切换tab
const tabActive = ref(2);

const tabList = ref([
  { label: "新增立项", count: 0, value: 0 },
  { label: "已提交", count: 0, value: 1 },
  { label: "全部", count: 0, value: 2 },
]);

//切换
function tabChange(tab) {
  switch (tabActive.value) {
    case 0:
      queryParams.projectStatus = "新增立项";
      break;
    case 1:
      queryParams.projectStatus = "已提交";
      break;
    case 2:
      queryParams.projectStatus = "";
      break;
  }
  getList();
}

// 数据请求

// 项目ID下拉
function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      searchOptions.projectId = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      searchOptions.projectName = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

function getTenderTypeFun() {
  getTenderType()
    .then((res) => {
      searchOptions.biddingMethod = res.data.map((item) => ({
        value: item.dictLabel || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}

function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      if (!res.data || !Array.isArray(res.data)) {
        console.error("assetOwnerTree返回数据格式错误:", res.data);
        return;
      }
      // 资产转让方下拉
      searchOptions.transferor = res.data.map((item) => ({
        value: item.id.split(":")[1],
        label: item.label,
      }));
      // 产品类型级联
      searchOptions.productId = res.data.flatMap(owner => {
        const parent = {
          value: owner.id,
          label: owner.label,
          disabled: true // 父项不可选
        };

        const children = (owner.children || []).map(child => ({
          value: child.id,
          label: child.label,
          disabled: false // 子项可选
        }));

        return [parent, ...children]; // 父项在前，子项紧跟其后
      });

    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}

function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      searchOptions.createById = res.data.map((item) => ({
        // value: `${item.code} - ${item.info}` || "",
        value: item.code,
        label: item.info,
      }));
    })
    .catch((error) => {
      console.error("获取立项人失败:", error);
    });
}

// 数据初始化
onMounted(() => {
  getProjectIdFun();
  getProjectNameFun();
  getTenderTypeFun();
  getTreeselectFun();
  getUserInfoFun();
  getList();
});
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-bottom: 32px;
  }
  :deep(.el-form.form-h50) {
    height: 62px;
  }
</style>
