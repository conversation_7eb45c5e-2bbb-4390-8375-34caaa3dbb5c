<template>
  <div style="height: calc(100vh - 203px); overflow: auto">
    <div class="title">回款跟进</div>
    <div class="pd20">
      <div class="flex">
        <el-radio-group v-model="radioType" @change="radioTypeChange">
          <el-radio-button label="0" name="0">本周</el-radio-button>
          <el-radio-button label="1" name="1">本月</el-radio-button>
          <el-radio-button label="2" name="2">今年</el-radio-button>
          <el-radio-button label="3" name="3">自定义</el-radio-button>
        </el-radio-group>
        <div style="width: 240px" v-if="radioType === '3'">
          <el-date-picker
            v-model="time"
            value-format="YYYY-MM-DD"
            type="daterange"
            clearable
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="请选择时间范围"
            style="width: 240px; margin-left: 10px"
            @change="getAnalysisData"
          />
        </div>
      </div>
      <!-- echarts -->
      <div id="repayFollow"></div>
      <el-table
        class="mt20"
        style="width: 100%"
        :data="tableData"
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column label="排名" type="index" align="center" width="60">
          <template #default="{$index}">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="转让方产品" prop="productName" align="center" />
        <el-table-column label="案件总量" prop="caseAmount" align="center"
        >
          <template #default="{ row }">
            {{ proxy.formatAmountWithComma(row.caseAmount || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="委案金额（元）" prop="caseMoney" align="center">
          <template #default="{ row }">
            {{ numFilter(row.caseMoney || 0) }}
          </template>
        </el-table-column>
        <el-table-column
          label="累计回款金额（元）"
          prop="accumulativeRepaymentMoney"
          align="center"
        >
          <template #default="{ row }">
            {{ numFilter(row.accumulativeRepaymentMoney || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="回款占比" prop="repaymentMoneyProportion" align="center">
          <template #default="{ row }">
            {{ row.repaymentMoneyProportion + "%" }}
          </template>
        </el-table-column>
        <el-table-column label="案件占比" prop="caseProportion" align="center">
          <template #default="{ row }">
            {{ row.caseProportion + "%" }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { numFilter } from "@/utils/common";
import { getWeekRange, getMonthRange, getYearRange } from "@/utils/getTime";
import { operationalAnalysis } from "@/api/team/teamlist/operate";

const route = useRoute();
const { proxy } = getCurrentInstance();

const radioType = ref("0");
const time = ref([]);
const apis = [getWeekRange, getMonthRange, getYearRange];

const rateChart = ref();
const repayFollowData = ref({});
const echartsOptions = ref({
  title: {
    text: "回款跟进",
    x: "center",
    textStyle: {
      textAlign: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: {
        color: "#999",
      },
      label: {
        backgroundColor: "#6a7985",
      },
    },
  },
  toolbox: {
    feature: {
      restore: { show: true },
      saveAsImage: { show: true },
    },
  },
  legend: {
    bottom: "10%",
    data: [],
  },
  dataZoom: [
    {
      show: true,
      realtime: true,
      start: 65,
      end: 85,
    },
    {
      type: "inside",
      realtime: true,
      start: 65,
      end: 85,
    },
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "15%",
    containLabel: true,
  },
  xAxis: [
    {
      type: "category",
      data: [],
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        interval: 0,
        rotate: -30,
      },
    },
  ],
  yAxis: [
    {
      type: "value",
    },
  ],
  series: [],
});

const tableData = ref([]);
//radios change
function radioTypeChange() {
  if (radioType.value === "3") {
    time.value = [];
  } else {
    time.value = apis[radioType.value]();
    getAnalysisData();
  }
}

//获取数据
function getAnalysisData() {
  if (time.value.length === 0) {
    time.value = apis[radioType.value]();
  }
  let req = {
    outsourcingTeamId: route.params.teamId || undefined,
    time: time.value,
  };
  operationalAnalysis(proxy.addFieldsRange(req, ["time"])).then((res) => {
    repayFollowData.value = res.data.lineChart;
    tableData.value = res.data.table;
  });
}
getAnalysisData();

//初始化echarts
function ecRepayFollowInit() {
  if (rateChart.value != null && rateChart.value != "" && rateChart.value != undefined) {
    rateChart.value.dispose(); //销毁
  }
  var chartDom = document.getElementById("repayFollow");
  var myChart = echarts.init(chartDom);
  rateChart.value = myChart;
  myChart.showLoading({ text: "加载中...", color: "#8870eb" });
  myChart.setOption(echartsOptions.value);
  window.onresize = function () {
    myChart.resize();
  };
  myChart.hideLoading();
}

watch(
  () => repayFollowData.value,
  (newval) => {
    echartsOptions.value.legend.data = newval.legendData || [];
    echartsOptions.value.xAxis[0].data = newval.xaxisData || [];
    let series = [];
    let seriesData = newval.seriesData || [];
    for (let i = 0; i < seriesData.length; i++) {
      let item = seriesData[i];
      let obj = {
        name: item.label,
        type: "line",
        stack: "wtf",
        emphasis: {
          focus: "series",
        },
        data: item.values || [],
      };
      series.push(obj);
    }
    echartsOptions.value.series = series;
    nextTick(() => {
      ecRepayFollowInit();
    });
  }
);

//合计
function getSummaries(param) {
  //需要取小数点的字段
  var fiexd_field = [
    "caseMoney",
    "accumulativeRepaymentMoney",
    "repaymentMoneyProportion",
    "caseProportion",
  ];
  //需要加百分比的字段
  var caseProportion = ["repaymentMoneyProportion", "caseProportion"];
  const { columns, data } = param;
  let sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }
    let values = data.map((item) => Number(item[column.property]));
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      if (fiexd_field.indexOf(column.property) > -1) {
        sums[index] = numFilter(sums[index]);
      }
      if (caseProportion.indexOf(column.property) > -1) {
        sums[index] += "%";
      }
    } else {
      sums[index] = "";
    }
  });

  return sums;
}
</script>

<style lang="scss" scoped>
.title {
  color: #888888;
  background-color: #f9f9f9;
  line-height: 38px;
  padding-left: 20px;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
#repayFollow {
  width: 100%;
  height: 500px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f9f9f9;
}
</style>
