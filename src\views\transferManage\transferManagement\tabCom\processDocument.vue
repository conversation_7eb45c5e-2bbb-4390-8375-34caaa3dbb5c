<template>
    <div>
        <el-table :data="dataList" v-loading="loading">
            <el-table-column label="资产编号" align="center" min-width="120" prop="assetsNo" />
            <el-table-column label="资产当时状态" align="center" min-width="120" prop="status" />
            <el-table-column label="提交时间" align="center" min-width="120" prop="createTime" />
            <el-table-column label="文件" align="center" min-width="120" prop="file" />
            <el-table-column label="备注信息" align="center" min-width="120" prop="remarks" />
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
const dataList = ref([
    {
        assetsNo: 'CITIC202502CC0002',
        status: '交割确认',
        createTime: '2025-06-09 09:47:40',
        file: '资产凭证.png\n交割确认书.pdf',
        remarks: '交割已完成',
    }
])
const loading = ref(false)

const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(1)
</script>

<style lang="scss" scoped></style>