<template>
  <div>
    <stamp :edit="false" />
  </div>
</template>

<script setup>
import stamp from "@/views/team/components/stamp/index.vue";
import { teamInfo, editLabel } from "@/api/team/team";
const route = useRoute();
const labels = ref([]);
const teamId = ref(undefined);
//获取机构信息
function getTeamInfo() {
  teamInfo(route.params.teamId).then((res) => {
    labels.value = res.data.labels || [];
    teamId.value = res.data.create.id;
  });
}
getTeamInfo();

provide(
  "labels",
  computed(() => labels.value)
);
provide("getTeamInfo", getTeamInfo);
</script>

<style scoped></style>
