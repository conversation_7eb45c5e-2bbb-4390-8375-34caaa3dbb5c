<template>
  <div class="app-container">
    <!-- 筛选表达 -->
    <el-form
      inline
      label-width="100px"
      :class="{ 'form-h50': !showSearch }"
      ref="queryRef"
      class="search-form"
    >
      <el-form-item prop="projectId" label="项目ID">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.projectId"
          :options="searchOptions.projectId"
          placeholder="项目ID"
        />
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.projectName"
          :options="searchOptions.projectName"
          placeholder="项目名称"
        />
      </el-form-item>
      <el-form-item prop="productId" label="产品类型">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.productId"
          :options="searchOptions.productId"
          placeholder="产品类型"
        />
      </el-form-item>
      <el-form-item prop="transferor" label="资产转让方">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.transferor"
          :options="searchOptions.transferor"
          placeholder="资产转让方"
        />
      </el-form-item>
      <el-form-item prop="biddingMethod" label="投标方式">
        <MultiSelect
          style="width: 230px"
          v-model="queryParams.biddingMethod"
          :options="searchOptions.biddingMethod"
          placeholder="投标方式"
        />
      </el-form-item>
      <el-form-item prop="totalDebt" label="债权总金额">
        <div class="range-area" style="width: 230px">
          <el-input v-model="queryParams.totalDebt1" />
          <span>—</span>
          <el-input v-model.number="queryParams.totalDebt2" />
        </div>
      </el-form-item>
      <el-form-item prop="principalAmount" label="债权本金">
        <div class="range-area" style="width: 230px">
          <el-input v-model="queryParams.principal1" />
          <span>—</span>
          <el-input v-model.number="queryParams.principal2" />
        </div>
      </el-form-item>
      <el-form-item prop="createById" label="立项人">
        <MultiSelect
          v-model="queryParams.createById"
          :options="searchOptions.createById"
          placeholder="立项人"
          style="width: 230px"
        />
      </el-form-item>
      <el-form-item prop="createTime" label="立项时间">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 230px"
        />
      </el-form-item>
      <el-form-item prop="baseDate" label="基准日">
        <el-date-picker
          v-model="queryParams.baseDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 230px"
        />
      </el-form-item>
      <el-form-item prop="planBiddingDate" label="预计竞价日期">
        <el-date-picker
          v-model="queryParams.planBiddingDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 230px"
        />
      </el-form-item>
    </el-form>
    <!-- 过滤按钮 -->
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <!-- 新增按钮 -->
    <div class="operation-revealing-area mb20">
      <el-button type="primary" plain @click="handleRevoke(row)"
        >撤销</el-button
      >
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @queryTable="getList"
      />
    </div>
    <!-- 全选功能 -->
    <SelectedAll
      :dataList="dataList"
      v-model:selectedArr="selectedArr"
      v-model:allQuery="queryParams.allQuery"
    >
      <template #content>
        <div class="ml20">
          <span
            >项目量（件）：<i class="danger">{{ total }}</i></span
          >
        </div>
      </template>
    </SelectedAll>
    <!-- 切换标签 -->
    <el-tabs v-model="tabActive" @tab-change="tabChange()">
      <el-tab-pane
        v-for="v in tabList"
        :key="v"
        :label="`${v.label}(${v.count || 0})`"
        :name="v.value"
      />
    </el-tabs>
    <div class="table-box">
      <!-- 表单 -->
      <el-table
        v-loading="loading"
        ref="multipleTableRef"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :selectable="selectable"
          width="30px"
          align="right"
        />
        <el-table-column
          label="项目ID"
          v-if="columns[0].visible"
          width="160"
          align="center"
          prop="projectId"
        />
        <el-table-column
          label="项目名称"
          v-if="columns[1].visible"
          width="200"
          align="center"
          prop="projectName"
        >
          <template #default="{ row }">
            <el-button type="text" @click="handleDetails(row)">{{
              row.projectName
            }}</el-button>
          </template>
        </el-table-column>

        <el-table-column
          label="产品类型"
          v-if="columns[2].visible"
          width="120"
          align="center"
          prop="productName"
        />
        <el-table-column
          label="项目状态"
          v-if="columns[3].visible"
          width="120"
          align="center"
          prop="examineState"
        >
          <template #default="{ row }">
            <el-popover
              placement="bottom"
              width="400"
              trigger="click"
              :show-arrow="true"
            >
              <template #reference>
                <span
                  style="cursor: pointer; color: #409eff"
                  @click="changeStatus(row)"
                >
                  {{ approveStateMap[row.examineState] }}
                </span>
              </template>
              <ProjectStatus :rowData="row" :formData="formData" />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="资产转让方"
          v-if="columns[4].visible"
          width="120"
          align="center"
          prop="transferor"
        />
        <el-table-column
          label="债权总金额"
          v-if="columns[5].visible"
          width="120"
          align="center"
          prop="totalDebt"
        />
        <el-table-column
          label="债权本金"
          v-if="columns[6].visible"
          width="120"
          align="center"
          prop="principal"
        />
        <el-table-column
          label="户数"
          v-if="columns[7].visible"
          width="120"
          align="center"
          prop="householdCount"
        />
        <el-table-column
          label="基准日"
          v-if="columns[8].visible"
          width="120"
          align="center"
          prop="baseDate"
        />
        <el-table-column
          label="预计竞价日期"
          v-if="columns[9].visible"
          width="120"
          align="center"
          prop="planBiddingDate"
        />
        <el-table-column
          label="投标方式"
          v-if="columns[10].visible"
          width="120"
          align="center"
          prop="biddingMethod"
        />
        <el-table-column
          label="报价上限"
          v-if="columns[11].visible"
          width="120"
          align="center"
          prop="priceLimit"
        />
        <el-table-column
          label="资产评估表"
          v-if="columns[12].visible"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 1)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="立项报告"
          v-if="columns[13].visible"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 0)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="其他附件"
          v-if="columns[14].visible"
          width="120"
          align="center"
          prop="surface"
        >
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleCheck(row, 2)"
                >查看</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="立项人"
          v-if="columns[15].visible"
          width="120"
          align="center"
          prop="createBy"
        />
        <el-table-column
          label="立项时间"
          v-if="columns[16].visible"
          width="160"
          align="center"
          prop="createTime"
        />
        <el-table-column
          label="审核时间"
          v-if="columns[17].visible"
          width="160"
          align="center"
          prop="examineTime"
        />

        <el-table-column fixed="right" width="220" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" @click="handleDetails(row)"
                >详情</el-button
              >
              <el-button type="text" @click="handleRevoke(row)">撤销</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <perviewFile ref="perviewFileRef" />
    <CheckFileDialog ref="checkFileDialogRef" />
  </div>
</template>

<script setup name="StartNapeList">
import perviewFile from "../startNapeList copy/dialog/perviewFile";
import {
  getNapeListByIdAndType,
  getProjectId,
  getProjectName,
  getProductType,
  getUserInfo,
  getTenderType,
  getApprovalProcess,
} from "@/api/dueDiligence/startNapeList";
import {
  revokeProjectApproval,
  selectApprovalList,
  groupStatisticsState,
} from "@/api/dueDiligence/schemeApproval";
import { assetOwnerTree } from "@/api/assets/assetside";
import { ArraysToStrings } from "@/utils/index";
import CheckFileDialog from "../page/checkFile.vue";
import ProjectStatus from "../page/projectStatus.vue";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const formData = ref({});

// 搜索参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  allQuery: false,
  projectId: [], // 项目ID
  projectName: [], // 项目名称
  productId: [], // 产品类型
  transferor: [], // 资产转让方
  biddingMethod: [], // 投标方式
  totalDebt1: null, // 债权总金额最小值
  totalDebt2: null, // 债权总金额最大值
  principal1: null, // 债权本金最小值
  principal2: null, // 债权本金最大值
  createById: [], // 立项人
  createTime: "", // 立项时间
  baseDate: "", // 基准日
  planBiddingDate: "", // 预计竞价日期
  examineState: "",
});

// 选项数据
const searchOptions = reactive({
  projectId: [],
  projectName: [],
  productId: [],
  transferor: [],
  biddingMethod: [],
  createById: [],
});

const total = ref(1);

// 表单数据
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const selectedArr = ref([]);
// 表单列数据
const columns = ref([
  { key: 0, label: "项目ID", visible: true },
  { key: 1, label: "项目名称", visible: true },
  { key: 2, label: "产品类型", visible: true },
  { key: 3, label: "项目状态", visible: true },
  { key: 4, label: "资产转让方", visible: true },
  { key: 5, label: "债权总金额", visible: true },
  { key: 6, label: "债权本金", visible: true },
  { key: 7, label: "户数", visible: true },
  { key: 8, label: "基准日", visible: true },
  { key: 9, label: "预计竞价日期", visible: true },
  { key: 10, label: "投标方式", visible: true },
  { key: 11, label: "报价上限", visible: true },
  { key: 12, label: "资产评估表", visible: true },
  { key: 13, label: "立项报告", visible: true },
  { key: 14, label: "其他附件", visible: true },
  { key: 15, label: "立项人", visible: true },
  { key: 16, label: "立项时间", visible: true },
  { key: 17, label: "审核时间", visible: true },
]);

// 处理查询参数
function handlingQueries(queryParams) {
  let params = ArraysToStrings(queryParams);
  params = {
    ...params,
    createTime1: queryParams.createTime[0],
    createTime2: queryParams.createTime[1],
    baseDate1: queryParams.baseDate[0],
    baseDate2: queryParams.baseDate[1],
    planBiddingDate1: queryParams.planBiddingDate[0],
    planBiddingDate2: queryParams.planBiddingDate[1],
  };
  return params;
}

// 获取列表数据
function getList() {
  loading.value = true;
  let params = handlingQueries(queryParams);

  const countParams = { ...params };
  delete countParams.examineState;
  Promise.allSettled([
    groupStatisticsState(countParams),
    selectApprovalList(params),
  ])
    .then(([countRes, listRes]) => {
      loading.value = false;
      total.value = listRes.value.total;
      dataList.value = listRes.value.rows;
      updateTabCounts(countRes.value.data);
      if (countRes.value.code !== 200) console.error("统计数据接口异常");
      if (listRes.value.code !== 200) console.error("列表数据接口异常");
    })
    .catch((error) => {
      proxy.$modal.msgError("获取数据失败");
      loading.value = false;
      console.error("获取数据失败:", error);
    });
}

// 重置查询条件
function resetQuery() {
  // 重置所有查询参数
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    allQuery: false,
    projectId: [],
    projectName: [],
    productId: [],
    transferor: [],
    biddingMethod: [],
    totalDebt1: null,
    totalDebt2: null,
    principal1: null,
    principal2: null,
    createById: [],
    createTime: "",
    baseDate: "",
    planBiddingDate: "",
  });

  // 重新获取数据
  getList();
}

const selectedRows = ref([]);

// 表格选择事件
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 搜索查询
function handleQuery() {
  // 重置页码到第一页
  queryParams.pageNum = 1;
  getList();
}

// 撤销
function handleRevoke(row = null) {
  proxy.$modal
    .confirm("此操作将撤销选中的申请，是否继续？？", "撤销提示")
    .then(function () {
      loading.value = true;
      if (!row && selectedRows.value.length == 0) {
        proxy.$modal.msgWarning("请选择要撤销的项目");
        loading.value = false;
        return;
      }
      let params = handlingQueries(queryParams);
      params.ids = row ? [row.id] : selectedRows.value.map((item) => item.id);
      revokeProjectApproval(params)
        .then((res) => {
          loading.value = false;
          proxy.$modal.msgSuccess("撤销成功");
          getList();
        })
        .catch((error) => {
          loading.value = false;
          proxy.$modal.msgError("撤销失败");
          console.error("撤销失败:", error);
        });
    });
}

// function selectable() {
//   return !queryParams.allQuery;
// }

function handleDetails(row) {
  const query = {
    path: route.path,
    pageType: "schemeApproval",
    progressStatus: 1,
    isDetails: 1,
    rowData: JSON.stringify(row),
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

// 查看
function handleCheck(row, fileType) {
  const query = {
    id: row.id,
    fileType: fileType,
  };
  getNapeListByIdAndType(query).then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      proxy.$refs["checkFileDialogRef"].openDialog(res.data);
    } else {
      proxy.$modal.msgWarning("未上传文件");
    }
  });
}

// 切换tab
const tabActive = ref(5);

const tabList = ref([
  { label: "待方案审批", count: 0, value: 0 },
  { label: "方案审批中", count: 0, value: 1 },
  { label: "方案审批成功", count: 0, value: 2 },
  { label: "方案审批失败", count: 0, value: 3 },
  { label: "已撤销", count: 0, value: 4 },
  { label: "全部", count: 0, value: 5 },
]);

const approveStateMap = {
  0: "待方案审批",
  1: "方案审批中",
  2: "方案审批成功",
  3: "方案审批失败",
  4: "已撤销",
  5: "已作废",
  6: "已退案关闭",
};

function updateTabCounts(resData) {
  // console.log('updateTabCounts :>> ', resData);
  tabList.value.forEach((tab) => {
    tab.count = 0;
  });
  const stateMap = {};
  resData.forEach((item) => {
    stateMap[approveStateMap[item.approveState]] = item.number;
  });

  tabList.value.forEach((tab) => {
    if (tab.label !== "全部") {
      tab.count = stateMap[tab.label] || 0;
    }
  });
  tabList.value.find((tab) => tab.label === "全部").count = tabList.value
    .filter((tab) => tab.label !== "全部")
    .reduce((sum, tab) => sum + tab.count, 0);
}

//切换
function tabChange(tab) {
  queryParams.examineState = tabActive.value;
  if (tabActive.value == 5) {
    queryParams.examineState = "";
  }
  getList();
}

// 数据请求

// 项目ID下拉
function getProjectIdFun() {
  getProjectId()
    .then((res) => {
      searchOptions.projectId = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目ID失败:", error);
    });
}

// 项目名称下拉
function getProjectNameFun() {
  getProjectName()
    .then((res) => {
      searchOptions.projectName = res.data.map((item) => ({
        value: item,
        label: item,
      }));
    })
    .catch((error) => {
      console.error("获取项目名称失败:", error);
    });
}

// 产品类型下拉
function getProductTypeFun() {
  getProductType().then((res) => {
    searchOptions.productId = res.data.map((item) => ({
      // value: `${item.code} - ${item.info}` || "",
      value: item.code,
      label: item.info,
    }));
  });
}

// 投标方式下拉
function getTenderTypeFun() {
  getTenderType()
    .then((res) => {
      searchOptions.biddingMethod = res.data.map((item) => ({
        value: item.dictLabel || "",
        label: item.dictLabel,
      }));
    })
    .catch((error) => {
      console.error("获取投标方式失败:", error);
    });
}

// 资产转让方下拉
function getTreeselectFun() {
  assetOwnerTree()
    .then((res) => {
      searchOptions.transferor = res.data.map((item) => ({
        // value: String(String(item.id).replace(/[^0-9]/g, '')) + '-' + item.label,
        value: item.id,
        label: item.label,
      }));
    })
    .catch((error) => {
      console.error("获取资产转让方失败:", error);
    });
}

// 立项人下拉
function getUserInfoFun() {
  getUserInfo()
    .then((res) => {
      searchOptions.createById = res.data.map((item) => ({
        // value: `${item.code} - ${item.info}` || "",
        value: item.code,
        label: item.info,
      }));
    })
    .catch((error) => {
      console.error("获取立项人失败:", error);
    });
}

// 建账状态
function changeStatus(row) {
  formData.value = {};
  getApprovalProcess({ approveId: row.approveId }).then((res) => {
    formData.value = res.data;
  });
}

// 数据初始化
onMounted(() => {
  getProjectIdFun();
  getProjectNameFun();
  getProductTypeFun();
  getTenderTypeFun();
  getTreeselectFun();
  getUserInfoFun();
  getList();
});
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-bottom: 32px;
  }
  :deep(.el-form.form-h50) {
    height: 62px;
  }
</style>