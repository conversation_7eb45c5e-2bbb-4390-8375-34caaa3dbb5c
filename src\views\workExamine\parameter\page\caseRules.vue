<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="96px" @submit.native.prevent>
      <el-form-item label="分案规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          clearable
          @keyup.enter="handleQuery"
          placeholder="请输入分案规则名称"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row class="mb10" :gutter="10">
      <el-button type="primary" plain @click="add(0)" v-if="checkPermi(['parameter:rules:add'])">新增分案规则</el-button>
    </el-row>
    <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column label="序号" align="center" :width="80" sortable>
       <template #default="scope">
          <span>{{ Number((queryParams.pageNum-1)*queryParams.pageSize + scope.$index + 1) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="分案规则名称"
        align="center"
        key="ruleName"
        prop="ruleName"
        sortable
        :width="180"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="分案规则内容"
        align="center"
        key="ruleContent"
        prop="ruleContent"
        sortable
        :show-overflow-tooltip="true"
      >
        <template #default="{ row }">
         <el-tooltip placement="top" v-if="row?.ruleContent">
            <template #content>
              <p style="max-width: 300px">{{ row.ruleContent }}</p>
            </template>
            <div>
              <span>{{
                row.ruleContent?.length > 30
                  ? `${row.ruleContent?.substring(0, 30)}...`
                  : row.ruleContent
              }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        key="status"
        prop="status"
        sortable
        :width="120"
      >
        <template #default="{ row }">
          <el-button type="primary" link @click="stateChange(row)" v-if="checkPermi(['parameter:rules:status'])">{{row.status == 0?'启用':'关闭'}}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        key="createBy"
        prop="createBy"
        sortable
        :width="180"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        key="createTime"
        prop="createTime"
        sortable
        :width="180"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" fixed="right" width="180px">
        <template #default="{ row,$index }">
          <el-button type="primary" link @click="remove(row)" v-if="checkPermi(['parameter:rules:remove'])">删除</el-button>
          <el-button type="primary" link @click="add(1, row)" v-if="checkPermi(['parameter:rules:edit'])">编辑</el-button>
          <el-popover
              v-if="checkPermi(['parameter:rules:editLog'])"
              placement="bottom"
              :width="700"
              :ref="`popover-${$index}`"
              title="修改历史"
              trigger="click"
            >
              <template #reference>
                <el-button type="primary" link @click="editLog(row)"
                  >修改记录</el-button
                >
              </template>
              <el-table :data="gridData">
              <el-table-column
                label="修改日期"
                align="center"
                key="createTime"
                prop="createTime"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="操作"
                align="center"
                key="operate"
                prop="operate"
                :width="180"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="操作人"
                align="center"
                key="createBy"
                prop="createBy"
                :width="180"
                :show-overflow-tooltip="true"
              />
            </el-table>
            <pagination
              v-show="gridDataTotal > 0"
              :total="gridDataTotal"
              v-model:page="gradParams.pageNum"
              v-model:limit="gradParams.pageSize"
              @pagination="getGrid"
            />
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增决策规则 -->
    <addCaseRules ref="addCaseRulesRef" @getList="getList" />
  </div>
</template>

<script setup name="CaseNode">
import {
  getSelectRule,
  removeRule,
  selectListWithRuleDetail,
  editWithStatus,
  selectInfoWithLog
} from "@/api/workExamine/parameter";
import addCaseRules from "../dialog/addCaseRules.vue";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const gridData = ref([]);
const gridDataTotal = ref(0);
//请求参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleName:undefined
  },
});
const gradParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const { queryParams } = toRefs(data);
const keyStatus = ref(+new Date());

//获取列表
function getList() {
  loading.value = false;
  getSelectRule(queryParams.value)
    .then((res) => {
      loading.value = false;
      dataList.value = res.rows;
      total.value = res.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
getList();

//新增决策规则
function add(type, row) {
  let req = type == 1 ? JSON.parse(JSON.stringify(row)) : undefined;
  if(type == 1){
    let reqData = {
      id:row.id
    }
    selectListWithRuleDetail(reqData).then((res) =>{
      req.detailList = res.data;
      proxy.$refs["addCaseRulesRef"].opendialog(type, req);
    }).catch((error) =>{
      req.detailList = [];
    })
  }else{
    proxy.$refs["addCaseRulesRef"].opendialog(type, req);
  }
}

//删除签名
function remove(row) {
  let req = {
    id: row.id,
  };
  proxy.$modal
    .confirm("删除后相关信息将无法恢复，是否确定删除?")
    .then(function () {
      loading.value = true;
      return removeRule(req);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("操作成功！");
    })
    .catch((err) => {
      loading.value = false;
    });
}

//查询
function handleQuery(){
  getList()
}

//重置
function resetQuery(){
  proxy.$refs["queryRef"].resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    ruleName:undefined
  };
  getList();
}

//状态修改
function stateChange(row) {
  loading.value = true;
  let req = {
    id: row.id,
    status: row.status == "0"?"1":"0"
  }
  editWithStatus(req)
    .then((res) => {
      getList();
      proxy.$modal.msgSuccess("修改成功");
    })
    .catch(() => {
      getList();
      //   row.state = row.state === "0" ? "1" : "0";
    });
}

//翻页
function getGrid() {
  let req = JSON.parse(JSON.stringify(gradParams.value));
  selectInfoWithLog(req)
    .then((res) => {
      gridData.value = res.rows;
      gridDataTotal.value = res.total;
    })
    .catch(() => {});
}

//修改记录
function editLog(row) {
  gradParams.value.id = row.id;
  getGrid();
}
</script>

<style lang="scss" scoped></style>
