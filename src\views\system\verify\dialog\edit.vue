<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="950px"
    append-to-body
    :before-close="cancel"
  >
    <div class="dispostWin">
      <div class="item">
        <div class="icon">1</div>
        <div class="item-tit">发起人</div>
        <div class="item-content">所有人</div>
      </div>
      <div
        style="display: inline-block"
        v-for="(item, index) in form.procedures"
        :key="index"
      >
        <div class="item-line"></div>
        <div class="item">
          <div class="icon">{{ index + 2 }}</div>
          <div class="item-tit">{{ CNnumber[index] }}级审批</div>
          <div class="item-content">
            <Tooltip
              :content="item.userNames || '请配置'"
              :length="16"
              width="500"
            />
          </div>
        </div>
      </div>
    </div>

    <el-row :gutter="10" class="mb10 mt10">
      <el-col :span="2">
        <el-button
          v-if="approveCode != 0"
          :disabled="form.procedures.length > 15"
          type="primary"
          plain
          icon="Plus"
          @click="add"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-form :model="form" :rules="rules" ref="formRef">
      <el-table :data="form.procedures" :max-height="300">
        <el-table-column label="步骤" width="55px" align="center">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="审批人">
          <template #default="{ row, $index }">
            <el-form-item
              :prop="`procedures.${$index}.userIds`"
              :rules="rules.userIds"
            >
              <el-select
                style="width: 300px"
                v-model="row.userIds"
                :disabled="row.userIds.includes('-1')"
                :multiple="approveCode == '0' ? false : true"
                filterable
                collapse-tags-tooltip
                collapse-tags
                @visible-change="
                  (val) => openapproval(val, row.roleId, $index, val)
                "
                @change="approvalChange(row.userIds, $index)"
                class="tupot-select"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in row.verifymans"
                  :key="item.code"
                  :label="item.info"
                  :value="item.code"
                  :disabled="item.disabled"
                  v-show="item.code != -1"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row, $index }">
            <el-button
              type="primary"
              link
              :disabled="
                $index === 0 ||
                ($index === 1 && !unpassCollect.includes(approveCode))
              "
              @click="moveUp($index)"
              >上移</el-button
            >
            <el-button
              type="primary"
              link
              :disabled="
                ($index == 0 && !unpassCollect.includes(approveCode)) ||
                $index == form.procedures.length - 1
              "
              @click="moveDown($index)"
              >下移</el-button
            >
            <el-button
              type="primary"
              link
              @click="remove(row, $index)"
              :disabled="
                ($index === 0 && !unpassCollect.includes(row.approveCode)) ||
                $index === 0
              "
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          :loading="subloading"
          :disabled="
            (form.procedures.length <= 1 &&
              !unpassCollect.includes(approveCode)) ||
            (form.procedures.length <= 0 && unpassCollect.includes(approveCode))
          "
          @click="submit"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getNewProcedures, editNewProcedure } from "@/api/system/verify";
import { getMyRoleOptions } from "@/api/system/role";
import { userOptions } from "@/api/system/user";
import { nextTick } from "vue";

const { proxy } = getCurrentInstance();

const emits = defineEmits(["getList"]);

const data = reactive({
  form: {
    procedures: [],
  },
  rules: {
    roleId: [{ required: true, message: "请选择审批角色", trigger: "blur" }],
    userIds: [{ required: true, message: "请选择审批人", trigger: "blur" }],
  },
});
const { form, rules } = toRefs(data);

const title = ref("");
const open = ref(false);
const subloading = ref(false);
const verifyroles = ref([]); //审批角色下拉
const verifymans = ref([]); //审批人下拉
const selectLoading = ref(false);
const deleteds = ref([]); //被删除的步骤
const approveCode = ref(); //审批类型
//级数
const CNnumber = [
  "一",
  "二",
  "三",
  "四",
  "五",
  "六",
  "七",
  "八",
  "九",
  "十",
  "十一",
  "十二",
  "十三",
  "十四",
  "十五",
];
//不通过催收端审批
const unpassCollect = ref([
  "6",
  "assest_import",
  "template",
  "refund",
  "company_fee",
  "initiation",
  "bidding",
  "contract",
  "accounting",
]);

// 获取总审批数据
const allVerifyData = ref([]);

// 转化成数组对象
function changeArrObj(data) {
  if (data.length > 0) {
    // 方法 1: 使用 flat() 解构嵌套数组
    const flatArray1 = data.flat();
    return flatArray1;
  }
}

function uniqueByMap(arr, key) {
  const map = new Map();
  return arr.filter((item) => !map.has(item[key]) && map.set(item[key], true));
}

//修改
function openDialog(row) {
  approveCode.value = row.approveCode;
  open.value = true;
  Promise.all([
    getMyRoleOptions(),
    userOptions(),
    getNewProcedures({ approveCode: row.approveCode }),
  ]).then((res) => {
    verifyroles.value = res[0].data;
    verifymans.value = res[1].data;
    if (!unpassCollect.value.includes(approveCode.value)) {
      if (approveCode.value == 0) {
        form.value.procedures.unshift({
          userIds: "-1",
          approveMethod: 1,
        });
      } else {
        form.value.procedures.unshift({
          userIds: ["-1"],
          approveMethod: 1,
        });
      }
    } else if (
      !unpassCollect.value.includes(approveCode.value) &&
      res[2].data.length == 0
    ) {
      form.value.procedures.unshift({
        userIds: ["1"],
        approveMethod: 1,
      });
    }
    form.value.procedures.map((item) => {
      item.verifymans = res[1].data;
    });
    if (res[2].data.length > 0) {
      allVerifyData.value = res[2].data;
      let mapNew = res[2].data.map((item) => item.zcApproveSetupInfoDtoList);
      mapNew.forEach((item, index) => {
        if (item.length > 0) {
          if (approveCode.value == 0) {
            var req = {
              roleId: String(item[0].roleId),
              userIds: item.map((item) => String(item.userId))[0],
            };
          } else {
            var req = {
              roleId: String(item[0].roleId),
              userIds: item.map((item) => String(item.userId)),
            };
          }
          form.value.procedures.push(req);
        }
      });
      let filterArr = form.value.procedures.filter(
        (item) => !item.userIds.includes("-1")
      );
      res[2].data.forEach((item1) => {
        filterArr.forEach((item2) => {
          if (item2.userIds.length > 0) {
            if (
              item2.userIds.includes(
                String(item1.zcApproveSetupInfoDtoList[0].userId)
              )
            ) {
              item2.approveMethod = item1.approveMethod;
            }
          }
        });
      });
      form.value.procedures.map((item) => {
        item.verifymans = res[1].data;
      });
      form.value.procedures.forEach((item1, index1) => {
        item1.userInfos = [];
        item1.verifymans.forEach((item3) => {
          if (approveCode.value == 0) {
            if (item1.userIds == item3.code) {
              item1.userInfos.push({
                userId: item3.code,
                userName: item3.info,
              });
            }
          } else {
            item1.userIds.forEach((item4) => {
              if (item3.code == item4) {
                item1.userInfos.push({
                  userId: item3.code,
                  userName: item3.info,
                });
              }
            });
          }
        });
        item1.userNames = item1.userInfos
          .map((item) => item.userName)
          .join(" ");
      });
      form.value.procedures = uniqueByMap(form.value.procedures, "userNames");
    }
  });
}

//新增审批流程
function add() {
  if (
    form.value.procedures.length == 0 &&
    !unpassCollect.value.includes(approveCode.value)
  ) {
    const obj = { roleId: "-1", userIds: ["-1"] };
    form.value.procedures.push(obj);
    nextTick(() => {
      roleChange("", 0);
    });
  } else {
    const obj = { roleId: "", userIds: [] };
    form.value.procedures.push(obj);
  }
}

//删除审批流程
function remove(row, index) {
  if (row.id) {
    deleteds.value.push(row);
  }
  form.value.procedures.splice(index, 1);
}

//选择角色
function roleChange(roleId, index, isRoleChange) {
  if (isRoleChange) {
    form.value.procedures[index].userIds = [];
    form.value.procedures[index].userInfos = [];
    form.value.procedures[index].userName = "";
    form.value.procedures[index].userNames = "";
  }
  selectLoading.value = true;
  userOptions({ roleId: roleId || undefined })
    .then((res) => {
      verifymans.value = res.data;
      form.value.procedures[index].verifymans = res.data;
      // //添加选择角色禁用
      form.value.procedures.forEach((item1, i) => {
        if (i != index) {
          verifymans.value.forEach((item2) => {
            if (approveCode.value == 0) {
              if (item1.userIds == item2.code) {
                item2.disabled = true;
              }
            } else {
              item1.userIds.forEach((item3) => {
                if (item3 == item2.code) {
                  item2.disabled = true;
                }
              });
            }
          });
        }
      });
      form.value.procedures.forEach((item1, index1) => {
        item1.userInfos = [];
        if (index == index1) {
          item1.verifymans.forEach((item3) => {
            if (approveCode.value == 0) {
              if (item1.userIds == item3.code) {
                item1.userInfos.push({
                  userId: item3.code,
                  userName: item3.info,
                });
              }
            } else {
              item1.userIds.forEach((item4) => {
                if (item3.code == item4) {
                  item1.userInfos.push({
                    userId: item3.code,
                    userName: item3.info,
                  });
                }
              });
            }
          });
        }
      });
      form.value.procedures[index].userNames = form.value.procedures[
        index
      ].userInfos
        .map((item) => item.userName)
        .join(" ");
    })
    .catch((error) => {
      console.log(error, "error");
      form.value.procedures[index].verifymans = [];
    })
    .finally(() => {
      selectLoading.value = false;
    });
}

//显示审批人下拉
function openapproval(val, roleId, index) {
  if (!val) return;
  // roleChange(roleId, index);
  roleChange("", index);
}

//获取审批人
function approvalChange(userId, index) {
  form.value.procedures[index].userInfos = [];
  form.value.procedures.forEach((item1) => {
    verifyroles.value.forEach((item2) => {
      if (item2.code == item1.roleId) {
        item1.roleName = item2.info;
      }
    });
  });

  form.value.procedures.forEach((item1, index1) => {
    if (index == index1) {
      item1.verifymans.forEach((item3) => {
        if (approveCode.value == 0) {
          if (item1.userIds == item3.code) {
            item1.userInfos.push({
              userId: item3.code,
              userName: item3.info,
            });
          }
        } else {
          item1.userIds.forEach((item4) => {
            if (item3.code == item4) {
              item1.userInfos.push({
                userId: item3.code,
                userName: item3.info,
              });
            }
          });
        }
      });
    }
  });
  form.value.procedures[index].userNames = form.value.procedures[
    index
  ].userInfos
    .map((item) => item.userName)
    .join(" ");
}

function changeMode(row, type) {
  row.approveMethod = type;
}

//上移
function moveUp(index) {
  let upData = form.value.procedures[index - 1];
  form.value.procedures.splice(index - 1, 1);
  form.value.procedures.splice(index, 0, upData);
}

//下移
function moveDown(index) {
  let downData = form.value.procedures[index + 1];
  form.value.procedures.splice(index + 1, 1);
  form.value.procedures.splice(index, 0, downData);
}

//取消配置
function cancel() {
  proxy.resetForm("formRef");
  open.value = false;
  form.value = {
    procedures: [],
  };
}

//提交
function submit() {
  subloading.value = true;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let req_form = JSON.parse(JSON.stringify(form.value));
      if (deleteds.value.length) {
        //删除的id集合
        req_form.deleteds = deleteds.value;
      }
      req_form.procedures.map((item, index) => {
        item.sort = index + 1;
        item.approveCode = approveCode.value;
        delete item.verifymans;
        delete item.userName;
      });
      let req = {
        approveCode: approveCode.value,
        zcApproveSetupNodeDtoList: [],
      };
      req_form.procedures.forEach((item) => {
        if (approveCode.value == 0) {
          if (item.userIds) {
            item.userInfo = [];
            let req = {
              approveCode: item.approveCode,
              userId: item.userIds,
              sort: 1,
              infoSort: 1,
            };
            item.userInfo.push(req);
          }
        } else {
          if (item.userIds.length > 0) {
            item.userInfo = [];
            item.userIds.forEach((item2, index) => {
              let req = {
                approveCode: item.approveCode,
                userId: item2,
                sort: index,
                infoSort: index,
              };
              item.userInfo.push(req);
            });
          }
        }
      });
      let changeMap = req_form.procedures.map((item) => item.userInfo);
      if (req_form.procedures.length > 0) {
        for (let i = 0; i < req_form.procedures.length; i++) {
          let changeValue = {
            approveCode: approveCode.value,
            approvePlatform: req_form.procedures[i].userIds.includes("-1")
              ? "JG"
              : "ZC",
            approveMethod: 1,
            approveObjectType: 1,
            platformId: 0,
            platformSort: i + 1,
            zcApproveSetupInfoDtoList: changeMap[i],
          };
          req.zcApproveSetupNodeDtoList.push(changeValue);
        }
        let newMap1 = req.zcApproveSetupNodeDtoList.filter(
          (item) => item.approvePlatform == "JG"
        );
        let newMap2 = req.zcApproveSetupNodeDtoList.filter(
          (item) => item.approvePlatform == "ZC"
        );
        for (let i = 0; i < newMap1.length; i++) {
          newMap1[i].platformSort = i;
        }
        for (let i = 0; i < newMap2.length; i++) {
          newMap2[i].platformSort = i;
        }
        req.zcApproveSetupNodeDtoList = [...newMap1, ...newMap2];
      }
      editNewProcedure(req)
        .then((res) => {
          proxy.$modal.msgSuccess("设置成功！");
          emits("getList");
          cancel();
        })
        .finally(() => {
          subloading.value = false;
        });
    } else {
      subloading.value = false;
    }
  });
}

defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.dispostWin {
  width: 100%;
  // text-align: center;
  overflow: hidden;
}

.dispostWin .item {
  line-height: 20px;
  display: inline-block;
  text-align: center;
}

.dispostWin .item .icon {
  margin: 0 auto 10px;
  width: 30px;
  color: #ffffff;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: var(--theme);
}

.item-tit {
  color: #3f3f3f;
  font-size: 14px;
}
.item-content {
  font-size: 12px;
  color: var(--theme);
}
.dispostWin .item-line {
  margin-bottom: 60px;
  display: inline-block;
  width: 60px;
  height: 1px;
  background: #e8e8e8;
}
:deep(.el-popper .el-select-dropdown__wrap) {
  max-height: 180px;
  height: 180px;
}
.modeMain {
  display: flex;
  gap: 20px;
  .modeBox {
    font-size: 14px;
    cursor: pointer;
  }

  .disabel {
    pointer-events: none;
    cursor: not-allowed;
    color: #c0c4cc; // 禁用态颜色
    background: #f5f7fa; // 可选：禁用态背景
  }
}
</style>
<style>
.tupot-select .el-input__inner {
  height: 32px !important;
}
</style>
