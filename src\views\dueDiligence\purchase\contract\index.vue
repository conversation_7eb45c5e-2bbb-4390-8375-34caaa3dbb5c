<template>
  <div class="app-container">
    <el-form inline label-width="100px" ref="queryRef">
      <el-form-item prop="projectId" label="项目ID">
        <el-select style="width: 320px" v-model="queryParams.projectId" placeholder="请输入或选择项目ID" multiple filterable
          :reserve-keyword="false">
          <el-option v-for="item in projectIdOptions" :key="Math.random()" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item prop="projectName" label="项目名称">
        <el-select style="width: 320px" v-model="queryParams.projectName" placeholder="请输入或选择项目名称" multiple filterable
          :reserve-keyword="false">
          <el-option v-for="item in projectNameOptions" :key="Math.random()" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item prop="transferorId" label="资产转让方">
        <el-select v-model="queryParams.transferorId" collapse-tags collapse-tags-tooltip multiple
          placeholder="请输入或选择资产转让方" clearable filterable :reserve-keyword="false" style="width: 320px">
          <el-option v-for="item in transferorOptions" :key="item.code" :label="item.info" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select v-model="queryParams.productType" filterable :reserve-keyword="false" placeholder="请输入或选择产品类型"
          multiple style="width: 320px">
          <el-option v-for="item in options" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <template v-if="showSearch">
        <el-form-item label="合同状态" prop="contractStatus">
          <el-select v-model="queryParams.contractStatus" collapse-tags collapse-tags-tooltip multiple
            placeholder="请选择合同状态" clearable :reserve-keyword="false" style="width: 320px">
            <el-option v-for="item in contractStatusOptions" :key="item.dictLabel" :label="item.dictLabel"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
        <el-form-item label="合同名称" prop="contractName">
          <el-input style="width: 320px" v-model="queryParams.contractName" placeholder="请输入合同名称" />
        </el-form-item>
        <el-form-item label="合同编号" prop="contractNumber">
          <el-select v-model="queryParams.contractNumber" filterable :reserve-keyword="false" placeholder="请输入或选择合同编号"
            clearable style="width: 320px">
            <el-option v-for="item in contractNumberOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="合同类型" prop="contractType">
          <el-input style="width: 320px" v-model="queryParams.contractType" placeholder="请输入合同类型" />
        </el-form-item>
        <el-form-item prop="createById" label="申请人">
          <el-select v-model="queryParams.createById" collapse-tags collapse-tags-tooltip multiple
            placeholder="请输入或选择申请人" clearable filterable :reserve-keyword="false" style="width: 320px">
            <el-option v-for="item in createByOptions" :key="item.code" :label="item.info" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item prop="createTime" label="申请时间">
          <el-date-picker v-model="createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange" clearable
            unlink-panels range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 320px" />
        </el-form-item>
        <el-form-item label="发起方式" prop="initiatorType">
          <el-select v-model="queryParams.initiatorType" :reserve-keyword="false" placeholder="请选择发起方式" clearable
            style="width: 320px">
            <el-option v-for="item in startMethodOptions" :key="item.dictLabel" :label="item.dictLabel"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
        <el-form-item label="印章类型" prop="sealType">
          <el-select v-model="queryParams.sealType" filterable :reserve-keyword="false" placeholder="请输入或选择印章类型"
            clearable multiple style="width: 320px">
            <el-option v-for="item in sealTypeOption" :key="item.dictLabel" :label="item.dictLabel"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <div class="text-center">
      <el-button @click="antiShake(resetQuery)">重置</el-button>
      <el-button type="primary" @click="antiShake(handleQuery)">搜索</el-button>
    </div>
    <div class="operation-revealing-area mb20">
      <el-button type="primary" @click="handleRevoke()">撤销</el-button>
      <!-- 全选功能 -->
        <div class="this-page-selected" style="flex">
            <el-checkbox-group v-model="checkedType" @change="checkedTypeChange" :disabled="dataList.length === 0">
                <el-checkbox v-for="item in checkStatus" :key="item.label" :label="item.label"
                    :indeterminate="item.indeterminate" />
            </el-checkbox-group>
            <span class="this-danger-parent">项目量（件）：<i class="danger">{{ total }}</i></span>
        </div>
    
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getList" />
    </div>
    <el-tabs v-model="activeName" @tab-change="antiShake(tabChangeList)">
      <el-tab-pane v-for="(item, index) in erectNapeEnum" :key="index" :label="item.info" :name="index" />
    </el-tabs>
    <div class="table-box">
      <el-table :data="dataList" :loading="loading" ref="multipleTableRef" @selection-change="handleSelectionChange" 
      :class="{ 'table-select-all-disabled': checkedType[0] === '搜索结果全选' }" @select-all="handleSelectAll">
        <el-table-column type="selection" width="30px" align="right" :selectable="checkSelectable"/>
        <el-table-column label="项目ID" v-if="columns[0].visible" align="center" prop="projectId" />
        <el-table-column label="项目名称" v-if="columns[1].visible" align="center" prop="projectName">
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              :content="row.projectName"
              placement="top"
              :disabled="!(row.projectName && row.projectName.length > 20)"
            >
              <el-link
                type="primary"
                :underline="false"
                @click="handleDetails(row)"
              >
                {{
                  row.projectName
                    ? row.projectName.length > 20
                      ? row.projectName.slice(0, 20) + '...'
                      : row.projectName
                    : ''
                }}
              </el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="资产转让方" v-if="columns[2].visible" align="center" prop="transferor" />
        <el-table-column label="产品类型" v-if="columns[3].visible" align="center" prop="productType" />
        <el-table-column label="合同状态" v-if="columns[4].visible" align="center" prop="contractApproveStatus" />
        <el-table-column label="合同名称" v-if="columns[5].visible" align="center" prop="contractName">
          <template #default="{ row }">
              <span v-if="row.contractName && row.contractName.length > 20">
                <el-tooltip effect="dark" :content="row.contractName" placement="top">
                  <span>
                    {{ row.contractName.slice(0, 20) + '...' }}
                  </span>
                </el-tooltip>
              </span>
              <span v-else>
                {{ row.contractName}}
              </span>
          </template>
        </el-table-column>
        <el-table-column label="合同编号" v-if="columns[6].visible" align="center" prop="contractNumber">
          <template #default="{ row }">
              <span v-if="row.contractNumber && row.contractNumber.length > 20">
                <el-tooltip effect="dark" :content="row.contractNumber" placement="top">
                  <span>
                    {{ row.contractNumber.slice(0, 20) + '...' }}
                  </span>
                </el-tooltip>
              </span>
              <span v-else>
                {{ row.contractNumber}}
              </span>
          </template>
        </el-table-column>
        <el-table-column label="合同类型" v-if="columns[7].visible" align="center" prop="contractType" />
        <!-- <el-table-column label="发起方式" v-if="columns[8].visible" align="center" prop="initiatorType" /> -->
        <el-table-column label="发起方式" v-if="columns[8].visible" align="center" prop="initiatorType">
          <template #default="{ row }">
            {{ getDictLabel(startMethodOptions, row.initiatorType) }}
          </template>
        </el-table-column>
        <el-table-column label="签署份数" v-if="columns[9].visible" align="center" prop="signCount" />
        <el-table-column label="印章类型" v-if="columns[10].visible" align="center" prop="sealType">
          <template #default="{ row }">
            {{ getDictLabel(sealTypeOption, row.sealType) }}
          </template>
        </el-table-column>
        <el-table-column label="申请人" v-if="columns[11].visible" align="center" prop="createBy" />
        <el-table-column label="申请时间" v-if="columns[12].visible" align="center">
          <template #default="{ row }">
            {{ row.createTime
              ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
              : '--' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="180" label="操作">
          <template #default="{ row }">
            <div>
              <el-button type="text" v-if="['待发起合同', '合同发起失败', '已撤销'].includes(row.contractApproveStatus)"
                @click="handleAdd(row)">新增合同</el-button>
              <el-button type="text" v-if="['合同待审批'].includes(row.contractApproveStatus)"
                @click="handleRevoke(row)">撤销</el-button>
              <el-button v-if="!['待发起合同'].includes(row.contractApproveStatus)" type="text"
                @click="handleDetails(row)">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>

<script setup name="StartNapeList">
import { getDictProductType } from "@/api/assets/assetside";
import { getOwners } from "@/api/assets/casemanage";
import { listAndQuery, getProjectIdList, getProjectNameList, getProductTypeList, getTransferorList, getContractNumberList, getCreateByList, getCountStatus,revokeFormAll } from "@/api/conference/contract";
import dayjs from 'dayjs';
import { selectListContract } from "@/api/dueDiligence/contract";
import { getDict, getDictLabel, revokeProjectApproval } from "@/api/conference/biddings";
import { watch } from "vue";
import { fillEmptyToDash } from "@/api/conference/utils"
// import { erectNapeEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const data = reactive({
  queryParams: { pageNum: 1, pageSize: 10 },
});

const activeName = ref(5);

const options = ref([]);

// const ownerOption = ref([]);
const projectIdOptions = ref([]);
const projectNameOptions = ref([]);
const transferorOptions = ref([]);
const contractNumberOptions = ref([]);
const createByOptions = ref([]);
const startMethodOptions = ref([]);
const sealTypeOption = ref([]);
const contractStatusOptions = ref([]);
const countStatus = ref([]);
const selectedArr = ref([]);
const allQuery = ref(false)
const dictList = [
  { ruleName: 'contract_status', targetRef: contractStatusOptions },
  { ruleName: 'start_method', targetRef: startMethodOptions },
  { ruleName: 'seal_type', targetRef: sealTypeOption }
];


dictList.forEach(item => {
  getDict({ ruleName: item.ruleName }).then(res => {
    item.targetRef.value = res.data;
  }).catch(error => {
    console.error(`获取 ${item.ruleName} 字典数据失败:`, error);
  });
});
const tableProps = [
  'projectId',
  'projectName',
  'transferor',
  'productType',
  'contractApproveStatus',
  'contractName',
  'contractNumber',
  'contractType',
  'initiatorType',
  'signCount',
  'sealType',
  'createBy',
  'createTime'
];



const erectNapeEnum = ref([
  { code: '', info: "待发起合同" },
  { code: '', info: "合同待审批" },
  { code: '', info: "合同审批中" },
  { code: '', info: "合同申请失败" },
  { code: '', info: "合同申请成功" },
  { code: '', info: "全部" },
])

const createTime = ref([]);
const total = ref(1);
const dataList = ref([]);
const loading = ref(false);
const showSearch = ref(false);
const { queryParams } = toRefs(data);
const columns = ref([
  { "key": 0, "label": "项目ID", "visible": true },
  { "key": 1, "label": "项目名称", "visible": true },
  { "key": 2, "label": "资产转让方", "visible": true },
  { "key": 3, "label": "产品类型", "visible": true },
  { "key": 4, "label": "合同状态", "visible": true },
  { "key": 5, "label": "合同名称", "visible": true },
  { "key": 6, "label": "合同编号", "visible": true },
  { "key": 7, "label": "合同类型", "visible": true },
  { "key": 8, "label": "发起方式", "visible": true },
  { "key": 9, "label": "签署份数", "visible": true },
  { "key": 10, "label": "印章类型", "visible": true },
  { "key": 11, "label": "申请人", "visible": true },
  { "key": 12, "label": "申请时间", "visible": true }
]);

const checkedType = ref([]);
const checkStatus = ref([
  { label: "本页选中", is_settle: "1", indeterminate: false },
  { label: "搜索结果全选", is_settle: "2", indeterminate: false },
]);

// // 获取转让方
// getOwners().then((res) => {
//   ownerOption.value = res.data;
// });
//监听时间

watch(() => queryParams.value.pageNum, () => {
  nextTick(() => {
    proxy.$refs.multipleTableRef?.clearSelection()
  })
})

watch(createTime, (val) => {
  if (val && val.length > 0) {
    queryParams.value.createTimeStart = val[0];
    queryParams.value.createTimeEnd = val[1];
  } else {
    delete queryParams.value.createTimeStart;
    delete queryParams.value.createTimeEnd;
  }
});

//设置tab label数量显示
watch(countStatus, (newVal) => {
  erectNapeEnum.value.forEach((tab) => {
    const baseName = tab.info.split('(')[0].trim();
    if (baseName === "全部") {
      const total = newVal.reduce((sum, item) => sum + (item.count || 0), 0);
      tab.info = `全部 (${total})`;
    } else {
      const found = newVal.find(item => item.contractApproveStatus === baseName);
      tab.info = found ? `${baseName} (${found.count})` : `${baseName} (0)`;
    }
  });
}, { immediate: true });




//获取表格数据
listAndQuery({ pageNum: 1, pageSize: 10 }).then(res => {
  dataList.value = res.rows;
  dataList.value = fillEmptyToDash(res.rows,tableProps);
  total.value = res.total;
});

//获取项目id下拉框数据
getProjectIdList().then(res => {
  projectIdOptions.value = res.data;
});
//获取项目名称下拉框数据
getProjectNameList().then(res => {
  projectNameOptions.value = res.data;
});

//获取产品类型下拉框数据
getProductTypeList().then(res => {
  options.value = res.data;
});

//获取资产转让方下拉框数据
getTransferorList().then(res => {
  transferorOptions.value = res.data;
});

//获取合同编号下拉框数据
getContractNumberList().then(res => {
  contractNumberOptions.value = res.data;
});

//获取申请人下拉框数据
getCreateByList().then(res => {
  createByOptions.value = res.data;
});

//获取分组统计状态数量
getCountStatus().then(res => {
  countStatus.value = res.data;
});

//传参获取表格数据
function getList() {
  listAndQuery(queryParams.value)
    .then(res => {
      loading.value = false
      dataList.value = res.rows;
      if (checkedType.value[0] === "本页选中") {
        checkedType.value = [];
        proxy.$refs["multipleTableRef"]?.clearSelection();
      }
      dataList.value = fillEmptyToDash(res.rows,tableProps);
      total.value = res.total;
    })
    .catch(err => {
      proxy.$modal.msgError("获取数据失败")
      loading.value = false
      console.error("获取表格数据失败:", err);
    });
}
// function getList() { 
//   loading.value = true;
//   selectListContract().then((res) => {
//     loading.value = false
//     total.value = res.value.total
//     dataList.value = res.value.rows

//     if (res.code !== 200) console.error('列表数据接口异常')
//   }).catch(error => {
//     proxy.$modal.msgError("获取数据失败")
//     loading.value = false
//     console.error('获取数据失败:', error);
//   })

// }



let isCheck =false
function handleSelectionChange(selection) {
  selectedArr.value = selection;
  isCheck = true
  if(isCheck)return
  if (
      checkedType.value[0] === '本页选中' &&
      selection.length !== dataList.value.length
    ) {
      checkedType.value = [];
    }
}

function resetQuery() {
  // queryParams.pageNum = 1;
  // queryParams.pageSize = 10;
  queryParams.value = { pageNum: 2, pageSize: 10 };

  createTime.value = [];
  checkedType.value = [];
  proxy.$refs["multipleTableRef"]?.clearSelection();
  getList();
}

//tab切换数据变换
function tabChangeList() {
  queryParams.value.status = activeName.value !== 5 ? activeName.value : "";
  getList();
}

//搜索查询
function handleQuery() {
  queryParams.value.status = activeName.value !== 5 ? activeName.value : "";
  getList();
}
function handleDetails(row) {
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}
  const query = {
    path: route.path,
    pageType: "contract",
    progressStatus: 2,
    isDetails: 1,
    activeTab: "2",
    rowData: JSON.stringify(rowData)
  };
  router.push({ path: `/dueDiligence/projectInfo`, query })
}

function handleAdd(row) {
  const rowData ={id:row.id,projectId:row.projectId,approveId:row.approveId}

  const query = {
    path: route.path,
    pageType: "contract",
    progressStatus: 2,
    isDetails: 0,
    activeTab: "2",
    rowData: JSON.stringify(rowData)
  };
  router.push({ path: `/dueDiligence/projectInfo`, query });
}

//全选类型
function checkedTypeChange(val) {
    checkedType.value.length > 1 && checkedType.value.shift(); //单选
    if (checkedType.value.length === 0) {
        //全不选
        proxy.$refs["multipleTableRef"].clearSelection();
        checkStatus.value[0].indeterminate = false;
    } else if (checkedType.value?.[0] == '搜索结果全选') {
        nextTick(() => {
            dataList.value.length > 0 &&
                dataList.value.map((item) => {
                    proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
                });
        })
    } else {
        dataList.value.length > 0 &&
            dataList.value.map((item) => {
                proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
            });
    }
    if (checkedType.value[0] == "搜索结果全选") {
        checkStatus.value[0].indeterminate = false;
        queryParams.value.allQuery = true;
        allQuery.value = true;

    } else {
        queryParams.value.allQuery = false;
        allQuery.value = false;

    }
}

//选择列表
// function handleSelectionChange(selection) {
//     selectedArr.value = selection;
//     ids.value = selection.map(item => item.userId)
//     if (checkedType.value[0] != "搜索结果全选") {
//         if (selectedArr.value.length == dataList.value?.length) {
//             checkedType.value[0] = "本页选中";
//         } else {
//             checkedType.value = [];
//         }
//     }
//     checkStatus.value[0].indeterminate =
//         selectedArr.value.length > 0 &&
//         selectedArr.value.length < dataList.value.length;
// }

//表格行能否选择
function checkSelectable() {
    return !queryParams.value.allQuery;
}
//表格选中
function selectTable() {
    return new Promise((reslove, reject) => {
        try {
            dataList.value.map((item, index) => {
                proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
            });
            reslove(true);
        } catch (error) {
            reject(error);
        }
    });
}

watch(() => dataList.value, (newval, preval) => {
    if (newval.length > 0) {
        //处理禁用表格复选框时无法选中的情况
        if (queryParams.value.allQuery) {
            queryParams.value.allQuery = false;
            nextTick(() => {
                selectTable()
                    .then((res) => { })
                    .finally(() => {
                        queryParams.value.allQuery = true;
                    });
            });
        } 
    }
});


function handleRevoke(row) {  // 批量撤销时，先判断是否有选中
  if (!row && (!selectedArr.value || selectedArr.value.length === 0) && !allQuery.value) {
    proxy.$modal.msgWarning("请先选择要撤销的合同！");
    return;
  }
  proxy.$modal
    .confirm("此操作将撤销选中的申请，是否继续？？", "撤销提示")
    .then(() => {
      if (allQuery.value) {
        // 搜索全选模式
        const { pageSize, pageNum, ...restQueryParams } = queryParams.value;
        revokeFormAll(restQueryParams).then(()=>{
          getList();
        });
      } else {
        // 普通模式
        let data = {
          approveCode: "contract",
          allQuery: false,
          approvePageRequest: {
            pageSize:10,
            pageNum:1,
            approveData: {
              pageSize: queryParams.value.pageSize,
              pageNum: queryParams.value.pageNum,
              allQuery: false,
            }
          }
        };
        if (row) { // 行内撤销
          data.approveIds = [row.approveId=="--"?'':row.approveId];
        } else {

          data.approveIds = selectedArr.value
            .filter(item => item.approveId !== null)
            .map(item => item.approveId);
        }
        revokeProjectApproval(data).then((res)=>{
          getList();
        });
        
      }
    });
}

</script>

<style lang="scss" scoped>
  .this-page-selected {
    display: flex;
    align-items: center;
    gap: 16px; 
  }

  .item-count {
    margin-left: 16px; 
  }

  .this-danger-parent,.danger {
    font-size: 14px;
  }
</style>