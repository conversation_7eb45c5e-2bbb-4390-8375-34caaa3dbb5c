<template>
  <div class="app-container">
    <el-radio-group v-model="radioActive">
      <el-radio-button label="立项方案审批" name="立项方案审批"
        >立项方案审批</el-radio-button
      >
      <el-radio-button label="竞价审批" name="竞价审批"
        >竞价审批</el-radio-button
      >
      <el-radio-button label="合同审批" name="合同审批"
        >合同审批</el-radio-button
      >
      <el-radio-button label="项目建账审批" name="项目建账审批"
        >项目建账审批</el-radio-button
      >
    </el-radio-group>
    <bidding v-if="radioActive == '竞价审批'" />
    <contract v-if="radioActive == '合同审批'" />
    <projectAccounting v-if="radioActive == '项目建账审批'" />
    <projectProposal v-if="radioActive == '立项方案审批'" />
  </div>
</template>

<script setup>
import bidding from "./tabCom/bidding";
import contract from "./tabCom/contract";
import projectAccounting from "./tabCom/projectAccounting";
import projectProposal from "./tabCom/projectProposal";
const radioActive = ref("立项方案审批");
</script>

<style lang="scss" scoped></style>