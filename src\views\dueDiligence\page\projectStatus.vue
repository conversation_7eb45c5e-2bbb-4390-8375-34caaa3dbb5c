<template>
  <div class="project-status">
    <el-card
      class="right-card"
      v-if="formData && formData.progressVoList?.length > 0"
    >
      <div class="sub-title-list">
        <div class="sub-title-item">审批进度</div>
        <img :src="StatusImg" class="status-img" />
      </div>
      <div class="content-list">
        <div class="content-item">流程名称：{{ rowData.title }}</div>
        <div class="content-item">
          审批单号：{{ formData.approveNo || "--" }}
        </div>
      </div>
      <el-timeline style="max-width: 600px">
        <el-timeline-item
          v-for="v in formData.progressVoList"
          :key="v"
          type="primary"
          hollow
        >
          <div>
            <div class="timeline-title">
              {{ approvalStateMap[v.approveStart] }}
            </div>
            <div class="timeline-content mb5">
              {{ v.approveObjectName }}&nbsp;{{ v.approveTime }}
            </div>
            <div class="timeline-content">{{ v.reason || null }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from "vue";
import waitingForApproval from "@/assets/images/waitingForApproval.png";
import approving from "@/assets/images/approving.png";
import notApproved from "@/assets/images/notApproved.png";
import approved from "@/assets/images/approved.png";
import revoked from "@/assets/images/revoked.png";
// import invalidated from '@/assets/images/invalidated.png'

const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});
// 状态映射
const statusMap = {
  0: "waitingForApproval",
  1: "approving",
  2: "approved",
  3: "notApproved",
  4: "revoked",
};

const statusImgMap = {
  waitingForApproval,
  approving,
  approved,
  notApproved,
  revoked,
};

let StatusImg = ref("");

function getStatusImg() {
  let key = "waitingForApproval";
  if (props.formData && props.formData.approveNo) {
    const approveStart = Number(props.formData.examineState);
    key = statusMap[approveStart] || "waitingForApproval";
  }
  StatusImg.value = statusImgMap[key] || waitingForApproval;
}

watch(
  () => props.formData,
  () => {
    if (props.formData && props.formData.progressVoList?.length > 0) {
      getStatusImg();
    }
  },
  { deep: true }
);

const approvalStateMap = {
  0: "待审核",
  1: "审核中",
  2: "已通过",
  3: "未通过",
};
</script>

<style lang="scss" scoped>
.right-card {
  flex: none;
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}
.sub-title-list {
  display: flex;
  position: relative;
  align-items: center;
  margin-bottom: 10px;
  .status-img {
    position: absolute;
    top: 0;
    right: 0;
    height: 50px;
    margin-left: 10px;
  }
}
.sub-title-item {
  color: #000;
  font-size: 14px;
  font-weight: bold;
  margin-right: 20px;
  margin-bottom: 10px;
}
.content-list {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  .content-item {
    margin-bottom: 10px;
    &:last-of-type {
      margin-bottom: 20px;
    }
  }
}
.timeline-title {
  font-weight: bold;
}
.timeline-content {
  font-size: 12px;
  color: #999;
}
</style>
