<template>
  <div class="title mb20">
    <span>开启后，禁止复制系统所有页面。</span>
    <el-switch
      class="myswitch ml20"
      v-model="state.restrictedState"
      active-color="#2ECC71"
      :active-value="1"
      :inactive-value="0"
      active-text="开"
      inactive-text="关"
      @change="change"
    ></el-switch>
  </div>
</template>

<script setup>
import { changeSafeStatus } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const state = inject("state");
const getTeamSafe = inject("getTeamSafe", Function, true);

function change() {
  changeSafeStatus(state.value)
    .then(() => {})
    .catch(() => {
      getTeamSafe();
    });
}
</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
