<template>
  <el-table v-loading="loading" :data="deductList">
    <el-table-column
      label="序号"
      type="index"
      align="center"
      width="50px"
      :index="tableIndex"
    />
    <el-table-column
      label="策略名称"
      prop="strategyName"
      key="strategyName"
      align="center"
    />
    <el-table-column label="更新时间" prop="updateTime" align="center" key="updateTime"></el-table-column>
    <el-table-column label="状态" prop="state" align="center" key="state">
      <template #default="{row}">
        <el-switch
          class="myswitch"
          v-model="row.state"
          active-color="#2ECC71"
          active-value="0"
          inactive-value="1"
          active-text="开"
          inactive-text="关"
          @change="handleState(row)"
          v-if="checkPermi(['tactics:deduct:status'])"
        ></el-switch>
        <span v-else>{{row.state == 0?'开启':'关闭'}}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="{row}">
        <el-button type="primary" v-hasPermi="['tactics:deduct:edit']" link @click="editList(row)">修改</el-button>
        <el-button type="primary" v-hasPermi="['tactics:deduct:del']" link @click="removeList(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    :title="title"
    v-model="open"
    width="750px"
    append-to-body
    :before-close="cancel"
  >
    <el-form :model="form" :rules="rules" :inline="true" ref="deductRef">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略名称" prop="strategyName">
            <el-input v-model="form.strategyName" placeholder="请输入策略名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state">
            <el-switch
              class="myswitch"
              v-model="form.state"
              active-color="#2ECC71"
              active-value="0"
              inactive-value="1"
              active-text="开"
              inactive-text="关"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <!-- 0-费用减免 1-利息减免 2-本金减免 -->
          <el-form-item label="减免政策" prop="reductionMode">
            <el-radio-group v-model="form.reductionMode" @change="reductionModeChange">
              <el-radio v-for="item in reductionModeOptions" :key="item.code" :label="item.code">{{ item.info }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="自动减免公式：" style="margin-bottom: 0px;"></el-form-item>
        </el-col>
        <el-col :span="24" class="my-form-item">
          <el-form-item label=" ">
            债权本金 *
            <el-form-item prop="formulaResidualPrincipal">
              <el-input
                v-model="form.formulaResidualPrincipal"
                :disabled="form.reductionMode == 0 || form.reductionMode == 1"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input>
              %
            </el-form-item>
            + 债权利息 *
            <el-form-item prop="formulaInterestMoney">
              <el-input
                v-model="form.formulaInterestMoney"
                :disabled="form.reductionMode == 0 || form.reductionMode == 2"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input>
              %
            </el-form-item>
            + 债权费用 *
            <el-form-item prop="formulaServiceFee">
              <el-input
                v-model="form.formulaServiceFee"
                :disabled="form.reductionMode == 1 || form.reductionMode == 2"
                style="width: 110px"
                placeholder="请输入数值"
              ></el-input>
              %
            </el-form-item>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="hint">
        <div class="left">示例：</div>
        <div v-if="form.reductionMode == 0">如设置剩余本金（1000元）*100%+利息（100元）*100%+服务费（100元）*<span class="text-danger">90</span>%=1190元，只要申请的减免后应还金额大于等于1190元即可被自动减免通过，小于的则需人工审核；</div>
        <div v-else-if="form.reductionMode == 1">如设置剩余本金（1000元）*100%+利息（100元）*<span class="text-danger">90</span>%+服务费（100元）*0%=1090元，只要申请的减免后应还金额大于等于1090元即可被自动减免通过，小于的则需人工审核；</div>
        <div v-else>如设置剩余本金（1000元）*<span class="text-danger">90</span>%+利息（100元）*0%+服务费（100元）*0%=900元，只要申请的减免后应还金额大于等于900元即可被自动减免通过，小于的则需人工审核；</div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getReductionModes,
  reducetionList,
  addReducetion,
  editReducetion,
  deleteReducetion,
} from "@/api/system/tactic";
import { checkPermi } from "@/utils/permission";
const props = defineProps({
  queryParams: {
    type: Object,
  },
});
const { proxy } = getCurrentInstance();

/** 添加编辑策略表单验证 */
//数字（小数和整数）
const validateNumber = (rule, value, callback) => {
  let reg = /^(([^0][0-9]+|0)\.([0-9]{1,2})$)|^(([^0][0-9]+|0)$)|^(([1-9]+)\.([0-9]{1,2})$)|^(([1-9]+)$)/;
  if (value === "" || value === undefined) {
    callback(new Error("请输入"));
  } else if (!reg.test(value)) {
    callback(new Error("只能输入正数，保留两位小数"));
  } else {
    callback();
  }
};

const total = inject("total");
const $getList = inject("getList");

const loading = ref(false);
const subloading = ref(false);
const deductList = ref([]);

const title = ref("");
const open = ref(false);
const reductionModeOptions = ref([]);
const editCurrent = ref({}); //当前编辑的策略
const data = reactive({
  form: {
    strategyName: undefined,
    state: "0",
    reductionMode: undefined,
    formulaResidualPrincipal: undefined,
    formulaInterestMoney: undefined,
    formulaServiceFee: undefined,
  },
  rules: {
    strategyName: [{ required: true, message: "策略名称不能为空", trigger: "blur" }],
    reductionMode: [{ required: true, message: "请选择减免政策", trigger: "change" }],
    formulaResidualPrincipal: [{ validator: validateNumber, trigger: "change" }],
    formulaInterestMoney: [{ validator: validateNumber, trigger: "change" }],
    formulaServiceFee: [{ validator: validateNumber, trigger: "change" }],
  },
});
const { form, rules } = toRefs(data);

//获取列表
function getList(query) {
  loading.value = true;
  reducetionList(query)
    .then((res) => {
      total.value = res.total;
      deductList.value = res.rows;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

//打开编辑策略弹框
function editList(row) {
  editCurrent.value = JSON.parse(JSON.stringify(row));
  getReductionModes().then(res => {
    form.value = JSON.parse(JSON.stringify(row));
    reductionModeOptions.value = res.data
    if (form.value.reductionMode != null ||  form.value.reductionMode != undefined) {
      form.value.reductionMode = String(form.value.reductionMode)
    } else {
      form.value.reductionMode = reductionModeOptions.value[0].code;
    }
    open.value = true;
    title.value = "编辑策略";
  })
}

//删除
function removeList(row) {
  proxy.$modal
    .confirm('是否确认删除策略名称为"' + row.strategyName + '"的数据项？')
    .then(function () {
      loading.value = true;
      return deleteReducetion(row.id);
    })
    .then(() => {
      $getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {loading.value = false;})
}
//列表序号递增
function tableIndex(index) {
  return (props.queryParams.pageNum - 1) * props.queryParams.pageSize + index + 1;
}

//打开新建策略弹框
function opendialog(tit) {
  getReductionModes().then(res => {
    title.value = tit;
    open.value = true;
    reductionModeOptions.value = res.data
    form.value.reductionMode = reductionModeOptions.value[0].code;
    reductionModeChange(reductionModeOptions.value[0].code)
  })
}

//状态
function handleState(row) {
  editReducetion(row)
    .then((res) => {
      // proxy.$modal.msgSuccess("修改成功！");
      // $getList();
    })
    .catch(() => {
      $getList();
    });
}

//减免政策切换
function reductionModeChange(val) {
  console.log(val)
  console.log(editCurrent.value.reductionMode)
  if (val == editCurrent.value.reductionMode) { //当选择旧的减免政策 恢复旧的填写数据
    form.value = JSON.parse(JSON.stringify(editCurrent.value))
    form.value.reductionMode = String(form.value.reductionMode)
    return
  }
  if (val == 0) {
    form.value.formulaResidualPrincipal = 100;
    form.value.formulaInterestMoney = 100
    form.value.formulaServiceFee = undefined
  } else if (val == 1) {
    form.value.formulaResidualPrincipal = 100;
    form.value.formulaInterestMoney = undefined;
    form.value.formulaServiceFee = 0;
  } else {
    form.value.formulaResidualPrincipal = undefined;
    form.value.formulaServiceFee = 0
    form.value.formulaInterestMoney = 0
  }
}


//重置表单
function reset() {
  proxy.resetForm("deductRef");
  form.value = {
    strategyName: undefined,
    state: "0",
    reductionMode: undefined,
    formulaResidualPrincipal: undefined,
    formulaInterestMoney: undefined,
    formulaServiceFee: undefined,
  };
  editCurrent.value = {}
}

//取消添加/编辑
function cancel() {
  reset();
  open.value = false;
}

//提交表单
function submitForm() {
  proxy.$refs["deductRef"].validate((valid) => {
    if (valid) {
      subloading.value = true;
      if (form.value.hasOwnProperty("id")) {
        editReducetion(form.value).then((res) => {
          subloading.value = false;
          proxy.$modal.msgSuccess("修改成功！");
          editCurrent.value = {};
          cancel();
          $getList();
        });
      } else {
        addReducetion(form.value)
          .then((res) => {
            subloading.value = false;
            proxy.$modal.msgSuccess("添加成功！");
            cancel();
            $getList();
          })
          .catch(() => {
            subloading.value = false;
          });
      }
    }
  });
}

defineExpose({
  getList,
  opendialog,
});
</script>

<style lang="scss" scoped>
.hint {
  display: flex;
  align-items: top;
  justify-content: flex-start;
  font-size: 14px;
  color: #b2b2b1;
  .left {
    width: 58px;
  }
}
.my-form-item :deep(.el-form-item__error) {
  top: 78%;
}

.my-form-item :deep(.el-form-item--default .el-form-item__content) {
  line-height: 56px;
}
</style>
