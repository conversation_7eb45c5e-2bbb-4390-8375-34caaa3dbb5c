<template>
  <div class="upload-file">
    <el-upload
      :multiple="multiple"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-change="handleUploadChange"
      :on-exceed="handleExceed"
      :before-remove="handleBeforeRemove"
      :on-success="handleUploadSuccess"
      :headers="headers"
      class="upload-file-uploader"
      :drag="drag"
      :auto-upload="autoUpload"
      ref="uploadRef"
    >
      <div v-if="drag">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          <div>将文件拖到此处，或<em>点击上传</em></div>
          <slot name="drag-tip-msg">
            <div class="tip-msg">
              <div class="mt10 mb10">
                注意：1.文件需小于{{ fileSize }}M，导入需少于10000条
              </div>
              <div>2.文件仅支持{{ fileType.join("、") }}格式，请优先使用</div>
            </div>
          </slot>
        </div>
      </div>
      <el-button v-if="!drag" type="primary" class="mr15">{{
        btnText
      }}</el-button>
      <template #tip v-if="!autoUpload">
        <el-button
          class="ml15"
          :style="`display:${drag ? 'block' : 'inline'};`"
          type="success"
          @click="handleSubimt"
          >上传到服务器</el-button
        >
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";

const props = defineProps({
  // 数量限制
  limit: { type: [String, Number], default: 1 },
  // 大小限制(MB)
  fileSize: { type: [String, Number], default: 5 },
  // 文件集合
  fileList: { type: Array, default: [] },
  drag: { type: Boolean, default: false },
  // 文件上传路径
  uploadFileUrl: { type: String, default: "" },
  // 是否自动上传
  autoUpload: { type: Boolean, default: false },
  // 是否多选
  multiple: { type: Boolean, default: false },
  // 是否修改网关
  gateway: { type: String },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => [
      "doc",
      "xls",
      "xlsx",
      "ppt",
      "txt",
      "pdf",
      "png",
      "jpg",
      "jpeg",
      "jpge",
      "zip",
    ],
  },
  btnText: { type: String, default: "选取文件" },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["update:fileList"]);
const headers = ref({ Authorization: getToken() });

// 上传前校检格式和大小
function handleBeforeUpload(file, fileList) {
  const index = file.name.indexOf(".");
  const fileType = file.name.slice(index + 1, file.name.length);
  if (!props.fileType.includes(fileType)) {
    proxy.$modal.msgWarning(
      `文件上传格式不对，请上传${props.fileType.join("、")}文件格式`
    );
    return false;
  }
  if (file.size > props.fileSize * 1024 * 1024) {
    proxy.$modal.msgWarning(`上传的单个文件不能超过${props.fileSize}M!`);
    return false;
  }
}

//文件列表变化
function handleUploadChange(file, fileList) {
  const index = file.name.indexOf(".");
  const fileType = file.name.slice(index + 1, file.name.length);
  const newFileList = fileList.filter((item) => item.uid != file.uid);
  if (!props.fileType.includes(fileType)) {
    emit("update:fileList", newFileList);
    proxy.$modal.msgWarning(
      `文件上传格式不对，请上传${props.fileType.join("、")}文件格式`
    );
    return false;
  }
  if (file.size > props.fileSize * 1024 * 1024) {
    emit("update:fileList", newFileList);
    proxy.$modal.msgWarning(`上传的单个文件不能超过${props.fileSize}M!`);
    return false;
  }
  props.autoUpload && handleSubimt();
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传文件失败");
}
/* 文件移除 */
function handleBeforeRemove(file, fileList) {
  if (props.limit == 1) {
    props.fileList = [];
    emit("update:fileList", []);
    return false;
  }
  let name = "";
  if (file.response && file.response.code === 200) {
    name = file.name;
    for (let i = 0; i < props.fileList.length; i++) {
      if (props.fileList[i].name == name) {
        console.log(file, "file");
        props.fileList.splice(i, 1);
        break;
      }
    }
    emit("update:fileList", props.fileList);
  }
}

// 上传成功回调
function handleUploadSuccess(res, file, fileList) {
  const { code, data } = res;
  if (code !== 200) {
    proxy.$modal.msgWarning(`文件《${file.name}》上传失败！`);
    return false;
  } else {
    proxy.$modal.msgSuccess("文件上传成功！");
    props.fileList = fileList;
    emit("update:fileList", fileList);
  }
}
//上传文件
function handleSubimt() {
  proxy.$refs["uploadRef"].submit();
}

// 上传的图片服务器地址
const uploadFileUrl = computed(() => {
  let url = import.meta.env.VITE_APP_BASE_API + props.uploadFileUrl;
  return url;
});
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

:deep(.el-upload .el-upload-dragger) {
  height: auto;
  margin-bottom: 10px;
}

.tip-msg {
  color: #9c9c9c;
  padding-bottom: 20px;
}
.ml15 {
  margin-left: 15px;
}
</style>
