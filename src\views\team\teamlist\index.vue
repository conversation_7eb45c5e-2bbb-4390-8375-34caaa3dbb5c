<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb20">
      <el-col :span="6" :xs="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>机构总数</span>
            </div>
          </template>
          <div>{{ classteamData.number ? classteamData.number : "--" }}个</div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>合作中</span>
            </div>
          </template>
          <div>{{ classteamData.hezuozhong ? classteamData.hezuozhong : "--" }}个</div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>合作暂停</span>
            </div>
          </template>
          <div>
            {{ classteamData.hezuozanting ? classteamData.hezuozanting : "--" }}个
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="24">
        <el-card>
          <template v-slot:header>
            <div>
              <span>合作关闭</span>
            </div>
          </template>
          <div>{{ classteamData.hezuoguanbi ? classteamData.hezuoguanbi : "--" }}个</div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 统计卡片 End -->

    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" inline>
      <el-form-item label="机构名称" prop="cname">
        <el-input v-model="queryParams.cname" clearable placeholder="输入搜索关键词" @keyup.enter="handleQuery"
          style="width: 240px"></el-input>
      </el-form-item>
      <el-form-item label="机构类别">
        <el-select v-model="queryParams.teamLevelType" style="width:120px;" placeholder="请选择" clearable
          @change="changeCategory">
          <el-option v-for="(item, index) in categoryOptions" :key="index" :label="item.teamType"
            :value="item.teamType"></el-option>
        </el-select>
        <el-select v-model="queryParams.category" class="ml20" style="width:120px;" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in lawyerOptions" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="time">
        <el-date-picker v-model="queryParams.time" style="width: 240px" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="机构类型" prop="teamType">
        <el-select v-model="queryParams.teamType" placeholder="请选择" clearable style="width: 240px">
          <el-option v-for="(item, index) in teamTypeList" :key="index" :label="item.info"
            :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="antiShake(handleQuery)">搜索</el-button>
        <el-button icon="Refresh" @click="antiShake(resetQuery)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="toaddteam" v-hasPermi="['team:list:add']">创建机构</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Setting" :disabled="single" @click="openCooperation"
          v-hasPermi="['team:list:setstatus']">设置状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain @click="resetTeamPassword" icon="Lock" :disabled="single"
          v-hasPermi="['team:list:resetpwd']">重置密码</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-tabs class="mb8" v-model="activeTab" @tab-click="tabClick">
      <el-tab-pane label="合作中" name="0"> </el-tab-pane>
      <el-tab-pane label="合作暂停" name="1"> </el-tab-pane>
      <el-tab-pane label="合作关闭" name="2"> </el-tab-pane>
      <el-tab-pane label="全部" name="全部"> </el-tab-pane>
    </el-tabs>

    <el-table v-loading="loading" :data="dataList" ref="multipleTableRef" @select="handleSelect"
      @select-all="handleSelectAll">
      <el-table-column type="selection" width="30" align="right" />
      <el-table-column label="机构ID" align="left" key="id" prop="id" sortable show-overflow-tooltip v-if="columns[0].visible" min-width="100" />
      <el-table-column label="机构名称" align="center" key="cname" prop="cname" sortable v-if="columns[1].visible" min-width="100" />
      <el-table-column label="机构类别" align="center" :width="160" sortable v-if="columns[2].visible">
        <template #default="{ row }">
          <span v-if="row.teamLevelType && row.category">{{ row.teamLevelType }}-{{ row.category }}</span>
          <span v-else>{{ row.category }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录账号" align="center" key="account" prop="account" sortable v-if="columns[3].visible" min-width="100" />
      <el-table-column label="机构类型" align="center" key="teamType" prop="teamType" sortable v-if="columns[4].visible" min-width="100">
        <template #default="{ row }">
          <span v-if="row.teamType !== null">{{
            row.teamType == 0 ? "自营" : "委外"
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="子账号个数" align="center" key="numbers" prop="numbers" sortable v-if="columns[5].visible" min-width="120" />
      <el-table-column label="合作状态" align="center" key="cooperation" prop="cooperation" sortable v-if="columns[6].visible" min-width="100">
        <template #default="{ row }">
          <dict-tag :options="team_cooperation_status" :value="row.cooperation" />
        </template>
      </el-table-column>
      <el-table-column label="月委案金额" align="center" key="debtRepayable" prop="debtRepayable" sortable v-if="columns[7].visible" min-width="120">
        <template #default="{ row }">
          {{ proxy.numFilter(row.debtRepayable || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="月回款总额" align="center" key="totalCollection" prop="totalCollection" sortable
        v-if="columns[8].visible" min-width="120">
        <template #default="{ row }">
          {{ proxy.numFilter(row.totalCollection || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="佣金比" align="center" key="commissionRatio" prop="commissionRatio" sortable
        v-if="columns[9].visible" min-width="100" />
      <el-table-column label="佣金" align="center" key="commission" prop="commission" sortable v-if="columns[10].visible">
        <template #default="{ row }">
          {{ proxy.numFilter(row.commission || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="月回款率（%）" align="center" key="collectionRate" prop="collectionRate" sortable
        v-if="columns[11].visible" min-width="140" />
      <el-table-column label="月回款目标" align="center" key="collectionTargets" prop="collectionTargets" sortable
        v-if="columns[12].visible" min-width="120">
        <template #default="{ row }">
          {{ proxy.numFilter(row.collectionTargets || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="联系手机号码" align="center" key="contact" prop="contact" sortable v-if="columns[13].visible" min-width="130" />
      <el-table-column label="创建人" align="center" key="founder" prop="founder" sortable v-if="columns[14].visible" min-width="100" />
      <el-table-column label="创建日期" align="center" key="creationtime" prop="creationtime" sortable show-overflow-tooltip v-if="columns[15].visible" min-width="100" />
      <el-table-column label="操作" fixed="right" width="360">
        <template #default="{ row }">
          <el-button type="primary" link size="small" v-hasPermi="['team:list:task']"
            @click="takeCaseRules(row)">接案规则</el-button>
          <el-button type="primary" link size="small" v-hasPermi="['team:list:manage']"
            @click="toTeamManage(row)">机构管理</el-button>
          <el-button type="primary" link size="small" v-hasPermi="['team:list:safeset']"
            @click="toSafeset(row)">安全设置</el-button>
          <el-button type="primary" link size="small" v-hasPermi="['team:list:opAn']"
            @click="toOperateAnalyse(row)">运营分析</el-button>
          <!-- <el-button type="primary" link size="small" v-hasPermi="['team:list:menu']"
            @click="toOperateMenu(row)">菜单管理</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 设置状态 -->
    <v-cooperation ref="cooperationRef" :ids="ids" />

    <!-- 目标设置 -->
    <v-target ref="targetRef" />
    <!-- 重置密码 -->
    <edit-password ref="editPasswordRef" />
    <!-- 接案规则 -->
    <taskCaseRules ref="taskCaseRulesRef" @getList="getList" />

    <el-dialog v-model="resetopen" width="600px" append-to-body>
      <div class="text-center reset-pwd">
        <div class="step-icon">
          <el-icon class="check-icon" color="#FFFFFF">
            <check />
          </el-icon>
        </div>
        <h2>密码重置成功</h2>
        <p>默认密码为：{{ resetpwd }}</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="resetopen = false">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="dataList">
import vCooperation from "./dialog/cooperation";
import vTarget from "./dialog/target";
import editPassword from "./dialog/editPassword";
import taskCaseRules from "./dialog/taskCaseRules";

import {
  selectDictData,
  listTeam,
  selectClassTeam,
  resetPassword,
  OrganizationType,
} from "@/api/team/team";
const route = useRoute()
const { proxy } = getCurrentInstance();
const { team_cooperation_status } = proxy.useDict("team_cooperation_status");
const router = useRouter();
const activeTab = ref("0"); //合作状态
const showSearch = ref(true); //搜索显隐
const single = ref(true); //操作按钮能否操作
const multiple = ref(true);
const classteamData = ref({});

//列表选中
const ids = ref([]);
const selectedArr = ref([]);

const loading = ref(true);
const total = ref(0);
const dataList = ref([]); //列表

const cooperationRef = ref();
const targetRef = ref();
const categoryOptions = ref([]); //团队类别下拉
const lawyerOptions = ref([]);

const open = ref(false); //目标弹窗
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    cname: undefined,
    category: undefined,
    time: [],
    teamType: undefined,
    teamLevelType: undefined,
  },
});

// 列显隐信息
const columns = ref([
  { key: 0, label: `机构ID`, visible: true },
  { key: 1, label: `机构名称`, visible: true },
  { key: 2, label: `机构类别`, visible: true },
  { key: 3, label: `登录账号`, visible: true },
  { key: 4, label: `机构类型`, visible: true },
  { key: 5, label: `子账号个数`, visible: true },
  { key: 6, label: `合作状态`, visible: true },
  { key: 7, label: `月委案金额`, visible: true },
  { key: 8, label: `月回款总额`, visible: true },
  { key: 9, label: `佣金比`, visible: true },
  { key: 10, label: `佣金`, visible: true },
  { key: 11, label: `月回款目标%`, visible: true },
  { key: 12, label: `月回款目标`, visible: true },
  { key: 13, label: `联系手机号码`, visible: true },
  { key: 14, label: `创建人`, visible: true },
  { key: 15, label: `创建日期`, visible: true },
]);
const { queryParams } = toRefs(data);

const resetopen = ref(false);
const resetpwd = ref("");
const teamTypeList = ref([]);
//获取列表
function getList() {
  let query = JSON.parse(JSON.stringify(queryParams.value));
  if (queryParams.value.time.length > 0) {
    query.startTime = queryParams.value.time[0];
    query.completeTime = queryParams.value.time[1];
  }
  delete query.time;
  query.cooperation = activeTab.value === "全部" ? undefined : parseInt(activeTab.value);
  loading.value = true;
  listTeam(query)
    .then((res) => {
      total.value = res.total;
      dataList.value = res.rows;
    })
    .finally(() => {
      loading.value = false;
    });
}
getList();

//获取机构类别
function getTeamType() {
  OrganizationType().then((res) => {
    teamTypeList.value = res.data;
  });
}
getTeamType();

//获取分组统计
function getclassTeam() {
  selectClassTeam().then((res) => {
    classteamData.value = res.data;
  });
}
getclassTeam();

//机构类别下拉
selectDictData().then((res) => {
  categoryOptions.value = res.data;
});

const resetTeamTree = () => {
  queryParams.value.category = undefined;
  lawyerOptions.value = [];
};

// 处理机构下拉
const changeCategory = (value) => {
  if (value) {
    resetTeamTree();
    const lawyerArr = categoryOptions.value.filter((item) => item.teamType == value);
    lawyerArr.forEach((item) => {
      lawyerOptions.value = item.categoryList;
    });
  } else {
    resetTeamTree();
  }
};

//创建机构
function toaddteam() {
  const query = { path: route.path }
  router.push({ path: "/team/teamlist-add/addteam", query });
}

//打开接案规则
function takeCaseRules(row) {
  let req = JSON.parse(JSON.stringify(row));
  proxy.$refs["taskCaseRulesRef"].opendialog(req);
}

//tabs
function tabClick() {
  nextTick(() => {
    ids.value = [];
    single.value = true;
    handleQuery();
  });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

//重置
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.category = undefined;
  queryParams.value.teamLevelType = undefined;
  lawyerOptions.value = [];
  handleQuery();
}

//打开设置状态
function openCooperation() {
  cooperationRef.value.opendialog();
}

//重置机构密码
function resetTeamPassword() {
  let data = JSON.parse(JSON.stringify(ids.value));
  proxy.$refs["editPasswordRef"].opendialog(data);
}

/** 多选框选中数据 */
function handleSelect(selection, row) {
  selectedArr.value = selection;
  proxy.handleSelected(selection, row, ids, single, multiple, "id");
}

function handleSelectAll(selection) {
  selectedArr.value = selection;
  proxy.handleSelectedAll(selection, dataList, ids, single, multiple, "id");
}

// 监听表格数组的变化
watch(
  () => dataList.value,
  () => {
    nextTick(() => {
      dataList.value.forEach((item) => {
        if (ids.value.includes(item.id)) {
          proxy.$refs["multipleTableRef"].toggleRowSelection(item, true);
        }
      });
    });
  }
);

//机构类别转换
function categoryChange(row) {
  let arr = ["委外机构", "内催机构"];
  return arr[row.category] || "--";
}

//合作状态
function cooperationChange(row) {
  let arr = ["合作中", "合作暂停", "合作关闭"];
  return arr[row.category] || "--";
}

//机构管理
function toTeamManage(row) {
  router.push(`/team/teamlist-edit/teamManage/${row.id}`);
}

//安全设置
function toSafeset(row) {
  router.push(`/team/teamlist-safe/safeSet/${row.id}`);
}

//运营分析
function toOperateAnalyse(row) {
  router.push(`/team/teamlist-auth/operate/${row.id}`);
}

// 菜单管理
function toOperateMenu(row) {
  router.push(`/team/teamMenu/${row.id}`);
}

//打开目标设置
function openSetTarget(row) {
  console.log(row);
  targetRef.value.openSetTarget({
    id: row.id,
    collectionTargets: row.collectionTargets,
  });
}

provide("getList", getList);
provide("getclassTeam", getclassTeam);
</script>

<style lang="scss" scoped>
.reset-pwd {
  text-align: center;
  margin: 32px auto 25px;

  .step-icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 0 auto;
    background-color: #3cc556;
    border-radius: 50%;

    .check-icon {
      font-size: 34px;
    }
  }

  h2 {
    font-weight: 500;
    line-height: 17px;
    color: #3f3f3f;
    font-size: 18px;
    margin-bottom: 25px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 10px;
    color: #888888;
  }
}
</style>
