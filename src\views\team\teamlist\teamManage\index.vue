<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="3" :xs="24">
        <div class="head-container mb20" style="color: #888888">
          <svg-icon class="mr5" icon-class="user" color="#888888" />
          机构管理
        </div>
        <div class="head-container">
          <el-tabs class="mytabs" tab-position="left" v-model="tabActive">
            <el-tab-pane label="机构资料" name="0"></el-tab-pane>
            <el-tab-pane label="组织架构查看" name="1"></el-tab-pane>
            <el-tab-pane label="标签设置查看" name="3"></el-tab-pane>
            <el-tab-pane label="法院管理" name="4"></el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
      <el-col :span="21" :xs="24">
        <teaminfo v-if="tabActive === '0'" />
        <organization v-if="tabActive === '1'" />
        <verifySet v-if="tabActive === '2'" />
        <stampSet v-if="tabActive === '3'" />
        <courtManage v-if="tabActive === '4'" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="TeamManage">
import teaminfo from "./page/teaminfo";
import organization from "./page/organization";
import verifySet from "./page/verifySet";
import stampSet from "./page/stampSet";
import courtManage from "./page/courtManage";
import signatureManagement from "./page/signatureManagement";
const tabActive = ref("0");
</script>

<style lang="scss" scoped>
:deep(.head-container .el-tabs__nav) {
  width: 100% !important;
}
:deep(.el-tabs__item.is-left){
  justify-content: flex-start !important;
}
</style>