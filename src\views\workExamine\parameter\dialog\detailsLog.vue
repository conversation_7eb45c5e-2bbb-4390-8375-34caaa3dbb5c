<template>
  <el-table v-loading="loading" ref="multipleTableRef" :data="dataList">
      <el-table-column
        label="修改日期"
        align="center"
        key="createTime"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="操作"
        align="center"
        key="operate"
        prop="operate"
        :width="180"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="操作人"
        align="center"
        key="createBy"
        prop="createBy"
        :width="180"
        :show-overflow-tooltip="true"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
</template>

<script setup name="detailsLog">
import {
  selectInfoWithLog,
  selectListWithLog
} from "@/api/workExamine/parameter";
const { proxy } = getCurrentInstance();
//表格参数
const loading = ref(false);
const total = ref(0);
const dataList = ref([]);
const typeInfo = ref([selectInfoWithLog,selectListWithLog]);
const exType = ref(0);
//请求参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id:undefined
  },
});
const { queryParams } = toRefs(data);

//获取列表
function getList() {
  loading.value = false;
  typeInfo.value[exType.value](queryParams.value)
    .then((res) => {
      nextTick(() => {
        loading.value = false;
        dataList.value = res.rows;
        total.value = res.total;
      })
    })
    .catch(() => {
      loading.value = false;
    });
}

//设置id
function setId(id,type) {
  console.log(id)
  queryParams.value.id = id;
  exType.value = type || 0;
  getList()
}
defineExpose({
  getList,
  setId
});
</script>

<style>

</style>