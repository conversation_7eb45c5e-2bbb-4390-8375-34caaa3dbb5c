{"name": "mjiazc", "version": "3.3.0", "description": "资产管理中心", "author": "资产管理中心", "license": "MIT", "scripts": {"dev": "vite --host 0.0.0.0", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "2.1.6", "@antv/x6-plugin-history": "2.2.4", "@antv/x6-plugin-keyboard": "2.2.3", "@antv/x6-plugin-selection": "2.2.2", "@antv/x6-plugin-snapline": "2.1.7", "@antv/x6-plugin-stencil": "2.1.5", "@antv/x6-plugin-transform": "2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@element-plus/icons-vue": "^2.0.6", "@tinymce/tinymce-vue": "^4.0.2", "@vueuse/core": "^9.1.0", "aieditor": "^1.0.14", "axios": "^0.27.2", "dayjs": "^1.11.13", "echarts": "^5.3.2", "element-plus": "^2.2.6", "file-saver": "2.0.5", "fingerprintjs2": "^2.1.4", "fuse.js": "6.4.6", "html2canvas": "^1.4.1", "insert-css": "2.0.0", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "jspdf": "^2.5.1", "nprogress": "0.2.0", "tinymce": "^5.8.0", "vue": "3.2.32", "vue-cropper": "1.0.2", "vue-demi": "^0.13.11", "vue-pdf-embed": "^1.1.4", "vue-router": "4.0.12", "vue3-seamless-scroll": "^1.2.0", "vuex": "4.0.2", "wavesurfer.js": "^7.8.3"}, "devDependencies": {"@vitejs/plugin-vue": "1.9.4", "@vue/compiler-sfc": "3.2.22", "sass": "1.45.0", "unplugin-auto-import": "0.5.3", "vite": "2.6.14", "vite-plugin-compression": "0.3.6", "vite-plugin-inspect": "^0.3.11", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.1.0", "wavesurfer": "^1.3.4"}}