<template>
    <div class="app-container ">
        <el-row v-loading="loading">
            <el-col :span="4">
                <LeftSideTree :data="treeData" :nodeClick="handleTabClick" />
            </el-col>
            <el-col :span="20">
                <el-form :model="queryParams" label-width="auto" inline>
                    <el-form-item prop="batchNums" label="资产包编号">
                        <el-input v-model="queryParams.assetsNo" placeholder="请输入资产包编号" style="width:320px" />
                    </el-form-item>
                    <el-form-item prop="teamIdList" label="资产包名称">
                        <el-cascader v-model="queryParams.teamIdList" :options="teamOption"
                            :props="defaultPropsCascader" collapse-tags collapse-tags-tooltip clearable
                            placeholder="请选择资产包名称" filterable :reserve-keyword="false" style="width:320px" />
                    </el-form-item>
                </el-form>
                <div class="text-center">
                    <el-button :loading="loading" type="primary" @click="antiShake(handleQuery)">搜索</el-button>
                    <el-button :loading="loading" @click="antiShake(resetQuery)">重置</el-button>
                </div>
                <selectedAll>
                    <template #content>
                        <div class="ml10">
                            <span>资产包数量：<i class="danger">72</i></span>
                            <span class="ml20">资产包数量：<i class="danger">241</i></span>
                        </div>
                    </template>
                </selectedAll>
                <div>
                    <el-button type="primary mb10" @click="handleDownload">批量下载</el-button>
                </div>
                <el-table :data="dataList" v-loading="loading" ref="multipleTableRef"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" :selectable="selectable" width="30px" align="right" />
                    <el-table-column label="案件ID" align="center" prop="caseId" min-width="160" />
                    <el-table-column label="所属资产包" align="center" prop="assetsName" min-width="180" />
                    <el-table-column label="所属项目" align="center" prop="projectName" min-width="160" />
                    <el-table-column label="所属资产包编号" align="center" prop="assetsNo" min-width="160" />
                    <el-table-column label="资产包阶段" align="center" prop="stage" min-width="160" />
                    <el-table-column label="剩余未偿本息" align="center" prop="money" min-width="160" />
                    <el-table-column label="姓名" align="center" prop="name" min-width="160" />
                    <el-table-column label="身份证号" align="center" prop="idCard" min-width="180" />
                    <el-table-column label="联系方式" align="center" prop="phone" min-width="160" />
                    <el-table-column label="贷款产品" align="center" prop="product" min-width="160" />
                    <el-table-column fixed="right" width="160" label="操作">
                        <template #default="{ row }">
                            <el-button type="text" @click="handleOpenDialog('MaterialsInfoRef', row)">管理材料</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </el-col>
        </el-row>
        <MaterialsInfo ref="MaterialsInfoRef" />
    </div>
</template>

<script setup>
import LeftSideTree from '@/components/leftSideTree/index';
import MaterialsInfo from './dialog/materialsInfo.vue'
const { proxy } = getCurrentInstance()
const loading = ref(false)
const dataList = ref([
    {
        caseId: 'DKBH231234324',
        assetsName: '广东不良出让第二批次',
        projectName: '对外不良0202',
        assetsNo: 'ZCB893920475',
        stage: '尽调评估',
        money: '1901.21',
        name: '满*晓',
        idCard: '671983************2615',
        phone: '189****1241',
        product: '橙意金',
    },
    {
        caseId: 'DKBH2435453325',
        assetsName: '广东不良出让第三批次',
        projectName: '对外不良0203',
        assetsNo: 'ZCB893920475',
        stage: '尽调评估',
        money: '1201.21',
        name: '满*晓',
        idCard: '671983************2615',
        phone: '189****1241',
        product: '橙意金',
    },
    {
        caseId: 'DKBH3243234326',
        assetsName: '广东不良出让第四批次',
        projectName: '对外不良0204',
        assetsNo: 'ZCB893920475',
        stage: '尽调评估',
        money: '3201.21',
        name: '满*晓',
        idCard: '671983************2615',
        phone: '189****1241',
        product: '橙意金',
    },
    {
        caseId: 'DKBH231233227',
        assetsName: '广东不良出让第五批次',
        projectName: '对外不良0205',
        assetsNo: 'ZCB893920475',
        stage: '尽调评估',
        money: '2901.21',
        name: '满*晓',
        idCard: '671983************2615',
        phone: '189****1241',
        product: '橙意金',
    },
])
const queryParams = ref({
    allQuery: false,
    pageNum: 1,
    pageSize: 10,
})
const total = ref(1)
const treeData = ref([
    {
        id: 1,
        label: '消费贷',
        children: [
            { id: 11, label: '沃享极速分期', },
            { id: 12, label: '智鹿秒批贷', },
            { id: 13, label: '风云循环贷', },
            { id: 14, label: '信用付 PLUS', },
            { id: 15, label: '零零花青春版', },
            { id: 16, label: '教育智分期', },
            { id: 17, label: '家装无忧贷', },
            { id: 18, label: '旅行白条', },
            { id: 19, label: '医享付', },
            { id: 111, label: '绿享贷', },
            { id: 112, label: '乐享贷' },
        ]
    },
    {
        id: 2,
        label: '信用贷',
        children: [
            { id: 20, label: '白领优享贷' },
            { id: 21, label: '业主尊享贷' },
            { id: 22, label: '新市民安居贷' },
            { id: 23, label: '银发暖心贷' },
            { id: 24, label: '创客启航贷' },
            { id: 25, label: 'AI 智能卡' },
            { id: 26, label: '数据宝贷' },
            { id: 27, label: '仲思健康贷' },
            { id: 28, label: '跨境速汇贷' },
            { id: 29, label: '社区普惠贷' },
        ]
    },
]);

function handleTabClick() {
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 1000)
}
function handleOpenDialog(refName, row) {
    proxy.$refs[refName].openDialog({ ...row })
}
function selectable() {
    return !queryParams.value.allQuery
}
function handleDownload() {
    const fileName = `${+new Date()}.xlsx`
    exportFile('https://assest.amcmj.com/resource/preview/2025/06/18/d07d05ec0b6f40b99cff7fb833c46a0c_错误的案件.xlsx', fileName)
}
function exportFile(data, fileName) {
    // 地址不存在时，禁止操作
    if (!data) return;
    proxy.$modal.notify('正在下载中...')
    // 下载文件并保存到本地
    const callback = (data) => {
        // 创建a标签，使用 html5 download 属性下载，
        const link = document.createElement('a');
        // 创建url对象
        const objectUrl = window.URL.createObjectURL(new Blob([data]));
        link.style.display = 'none';
        link.href = objectUrl;
        // 自定义文件名称， fileName
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        // 适当释放url
        window.URL.revokeObjectURL(objectUrl);
    };
    // 把接口返回的url地址转换为 blob
    const xhr = new XMLHttpRequest();
    xhr.open('get', data, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
        // 返回文件流，进行下载处理
        callback(xhr.response);
        proxy.$modal.msgSuccess('操作成功！')
    };
    xhr.send(); // 不要忘记发送
};
</script>

<style lang="scss" scoped></style>