<template>
  <div class="title mb20">
    <span> 开启后，每次登录将进行安全验证</span>
    <el-switch
      class="myswitch ml20"
      v-model="state.securityVerificationStatus"
      active-color="#2ECC71"
      :active-value="0"
      :inactive-value="1"
      active-text="开"
      inactive-text="关"
      @change="change"
    ></el-switch>
  </div>
</template>

<script setup>
import { changeSafeStatus } from "@/api/team/team";
import { checkPermi } from "@/utils/permission";
const { proxy } = getCurrentInstance();
const getTeamSafe = inject("getTeamSafe", Function, true);

const state = inject("state");

//修改
function change() {
  changeSafeStatus(state.value)
    .then(() => {})
    .catch(() => {
      getTeamSafe();
    });
}
</script>

<style scoped>
.title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
