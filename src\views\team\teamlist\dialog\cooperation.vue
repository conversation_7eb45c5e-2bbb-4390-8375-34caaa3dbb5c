<template>
  <el-dialog title="合作状态" v-model="open" width="600px" append-to-body>
    <el-form :model="form" ref="formRef" :rules="rules">
      <el-form-item label="合作状态" prop="cooperation">
        <el-select
          v-model="form.cooperation"
          placeholder="请选择"
          clearable
          style="width: 240px"
        >
          <el-option label="合作中" value="0"></el-option>
          <el-option label="合作暂停" value="1"></el-option>
          <el-option label="合作关闭" value="2"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="subloading" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { editTeamStatus } from "@/api/team/team";
const { proxy } = getCurrentInstance();
const getList = inject("getList", Function, true);
const getclassTeam = inject("getclassTeam", Function, true);

const props = defineProps({
  ids: {
    required: true,
    type: Array,
  },
});
const open = ref(false);
const subloading = ref(false);
const data = reactive({
  form: {},
  rules: {
    cooperation: [{ required: true, message: "请选择合作状态", trigger: "change" }],
  },
});

const { form, rules } = toRefs(data);

//打开弹窗
function opendialog() {
  open.value = true;
}

//提交表单
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      let data = [];
      for (let i = 0; i < props.ids.length; i++) {
        data.push({
          id: props.ids[i],
          cooperation: form.value.cooperation,
        });
      }
      subloading.value = true;
      editTeamStatus(data)
        .then((res) => {
          let { code, msg } = res;
          if (code === 200) {
            proxy.$modal.msgSuccess(msg);
            cancel();
            subloading.value = false;
            getList();
            getclassTeam();
          }
        })
        .catch((err) => {
          subloading.value = false;
        });
    }
  });
}

//取消
function cancel() {
  reset();
  open.value = false;
}

//重置表单
function reset() {
  form.value = {
    cooperation: undefined,
  };
  proxy.resetForm("formRef");
}

defineExpose({
  opendialog,
});
</script>

<style scoped></style>
